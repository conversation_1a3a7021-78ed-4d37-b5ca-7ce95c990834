{"parser": "@typescript-eslint/parser", "plugins": ["@typescript-eslint", "cypress"], "ignorePatterns": ["/*.*", "cypress/*", "coverage/*", "/dist/*.*"], "overrides": [{"files": ["*.ts", "*.tsx"]}], "extends": ["airbnb", "airbnb-typescript", "prettier"], "parserOptions": {"project": ["./tsconfig.json"]}, "rules": {"no-restricted-syntax": "off", "no-await-in-loop": "off", "import/no-unresolved": "off", "import/no-extraneous-dependencies": "off", "import/order": "error", "react/jsx-props-no-spreading": "off", "react/require-default-props": "off", "jsx-a11y/no-noninteractive-element-interactions": "off", "react/function-component-definition": ["error", {"namedComponents": ["function-declaration", "arrow-function"], "unnamedComponents": "arrow-function"}], "react/jsx-no-useless-fragment": "off"}}