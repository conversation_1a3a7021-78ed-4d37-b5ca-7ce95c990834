#!/usr/bin/env bash

# Adapted from template version v1.11.0, ci-jobs/tag.yml

# Check out this repo that we're working on
git clone https://${GIT_CI_USERNAME}:${GIT_TOKEN}@gitlab.com/${CI_PROJECT_PATH}.git target-repo

cd target-repo

git checkout $CI_COMMIT_BRANCH # Commit branch is used incase a team wants to overwrite the rule for default branch.
git config user.name $GIT_CI_USERNAME
git config user.email $GIT_CI_EMAIL

echo "Merge commit comments: ---"
git log -1 --pretty=%B | tee tempFileMergeCommit.txt
echo "---"

# echo Get version command
export COMMIT_MESSAGE=$(sed "3q;d" tempFileMergeCommit.txt)
echo "Merging branch commit message: '${COMMIT_MESSAGE}'"

export VERSION_COMMIT_MESSAGE=$(echo "$COMMIT_MESSAGE" | awk '{print tolower($0)}')
echo "Version commit message: '${VERSION_COMMIT_MESSAGE}'"

VERSION_PREFIX=$(echo "${VERSION_COMMIT_MESSAGE}" | sed -E 's/^\s*\[(patch|minor|major)\].*$/\1/I' | tr '[:upper:]' '[:lower:]')

if [[ ! "${VERSION_PREFIX}" =~ (patch|minor|major) ]]; then
    echo "A missing or invalid prefix was used: '${VERSION_PREFIX}'"
    exit 1
fi

# We use the same prefix as the command given to yarn
VERSION_COMMAND="${VERSION_PREFIX}"
echo "Updating the ${VERSION_COMMAND} version with yarn.."
yarn version --no-git-tag-version --$VERSION_COMMAND

CURRENT_VERSION=$(node -pe "require('./package.json')['version']")
echo "Found the current version: ${CURRENT_VERSION}"

echo "Getting the message for the change log.."
sed -n '/^\*\*Change Log Start\*\*$/,/(^\*\*Change Log Stop\*\*$/p' tempFileMergeCommit.txt > tempChangeLogChunk.txt
sed '/\*\*Change Log Start\*\*/d' tempChangeLogChunk.txt > tempChangeLogChunkMinusStart.txt
sed '/\*\*Change Log Stop\*\*/d' tempChangeLogChunkMinusStart.txt > tempChangeLogEntry.txt

echo "Updating the change log.."
OLD_FILE="oldCHANGELOG.md"
DATESTAMP=$(date '+%Y-%m-%d')

echo "Updating the change log.."
mv CHANGELOG.md ${OLD_FILE} &&
cat - ${OLD_FILE} > CHANGELOG.md <<EOF
## [${CURRENT_VERSION}] - ${DATESTAMP}

$(cat tempChangeLogEntry.txt)


EOF

git add package.json
git add CHANGELOG.md

# PCM#147: Improvements to CI pipelines; Fix any prettier related formatting automatically and include in this commit
echo "Checking source formatting.."
yarn format || {
    echo "Auto-reformatting source and adding updated files to commit.."
    yarn format:fix >/dev/null
    git add -v -u src
}

TAG_NAME="v${CURRENT_VERSION}"
echo " Creating commit '${TAG_NAME}'.."
git commit -m "${TAG_NAME}"
# Push this new versioning & changelog commit, on the main branch
#   note: This should not trigger a CI pipeline
git push origin "${CI_COMMIT_BRANCH}"

# Only actually tag if we're on the main branch
if [[ "${CI_COMMIT_BRANCH}" == "main" ]]; then
    # Create a tag for this latest version, and push it
    #   Note: This should trigger a CI pipeline, which deploys to build,
    #         as well as scripts for QA to review the changelog,
    #         and a message posted to slack into #mso_releases
    #   NOTE: The current triggered scripts assume the latest CHANGELOG.md on the 'main' branch
    #         has been updated with the top entry matching this tag
    git tag -a ${TAG_NAME} -m "${TAG_NAME}" &&
    git push origin ${TAG_NAME}
fi

echo "Successfully completed tag script!"
