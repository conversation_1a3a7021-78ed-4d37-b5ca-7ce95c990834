include:
    - project: 'treez-inc/engineering/ci-templates/mfe'
      ref: '2.7.0'
      file: 'template.yml'

stages:
  - preconfigure
  - test
  # tag job moved earlier in the pipeline so it's run after every merge
  - tag
  - build
  - deploy
  - build_dev
  - deploy_dev
  - post_deploy_dev
  - build_build
  - deploy_build
  - post_deploy_build
  - build_prod
  - deploy_prod

variables:
    # Path to the MFE. Must match the path defined in MSO Core UI.
    BUCKET_PATH: product-control/latest
    DEFAULT_IMAGE: registry.gitlab.com/treez-inc/engineering/docker-images/mfe:1.0.1


install_dependencies:
    extends: .install_dependencies

lint:
    extends: .lint

detect_unused:
  extends: .detect_unused

  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH || $CI_COMMIT_TAG
    
unit_test:
    extends: .unit_test
    script: yarn run test

build_dev:
    extends: .build_dev

deploy_dev:
    extends: .deploy_dev

post_deploy_testing_dev:
    image: registry.gitlab.com/treez-inc/engineering/docker-images/cypress:node-18.20.3
    extends: .post_deploy_testing_dev
    script:
        - yarn install --frozen-lockfile --no-progress
        - yarn run cypress:run:dev
    allow_failure: true
    artifacts:
      when: on_failure
      paths:
        - cypress/screenshots/*

build_build:
    extends: .build_build

deploy_build:
    extends: .deploy_build

post_deploy_testing_build:
    image: registry.gitlab.com/treez-inc/engineering/docker-images/cypress:node-18.20.3
    extends: .post_deploy_testing_build
    script:
        - yarn install --frozen-lockfile --no-progress
        - yarn run cypress:run:build
    allow_failure: true
    artifacts:
      when: on_failure
      paths:
        - cypress/screenshots/*

tag:
    extends: .tag

build_prod:
    extends: .build_prod

deploy_prod:
    extends: .deploy_prod