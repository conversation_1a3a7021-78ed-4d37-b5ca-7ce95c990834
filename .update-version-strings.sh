#!/usr/bin/env sh

# First we need to make sure we have git
git status >/dev/null 2>&1 || {
    echo "Attempting to install git..."
    apk add git ||
        apt-get install -y git ||
        yum -y install git ||
        echo "Error: Unable to install git.."
}

export VERSION_TAG=$(git describe --tags)
export VERSION_COMMIT=$(git rev-parse --short HEAD)
export VERSION_BUILD_DATE=$(date -Iseconds)

echo "Detected version variables:"
env | grep -E '^VERSION_(TAG|COMMIT|BUILD_DATE)'

echo "Updating compiled source with version strings.."
sed -i~ -E "s/_version_tag_/${VERSION_TAG}/g" build-*/*.js
sed -i~ -E "s/_version_commit_/${VERSION_COMMIT}/g" build-*/*.js
sed -i~ -E "s/_version_build_date_/${VERSION_BUILD_DATE}/g" build-*/*.js
rm build-*/*.js~
