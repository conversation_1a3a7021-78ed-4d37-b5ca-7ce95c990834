## [3.47.0] - 2025-06-26
See merge request treez-inc/engineering/back-of-house/product-control-mfe!681

### Description
Related to #918

### Personal
Co-Authors

Merged By
<PERSON> <<EMAIL>>

Approvers
Approved-by: <PERSON> <<EMAIL>>
Approved-by: <PERSON> <<EMAIL>>

## [3.46.0] - 2025-06-26
See merge request treez-inc/engineering/back-of-house/product-control-mfe!682

### Description
### Changed

* Changed logic in ActionBarUtility.ts so that 'Add to product collection' But<PERSON> will be displayed and working for deactivated products. 

### Close Issues

Related to #974

### Personal
Co-Authors

Merged By
<PERSON> <<EMAIL>>

Approvers
Approved-by: <PERSON> <<EMAIL>>
Approved-by: <PERSON> <and<PERSON>.<EMAIL>>

## [3.45.5] - 2025-06-24
See merge request treez-inc/engineering/back-of-house/product-control-mfe!675

### Description


Related to #965

### Personal
Co-Authors

Merged By
<PERSON> <<EMAIL>>

Approvers
Approved-by: <PERSON> Bishop <<EMAIL>>

## [3.45.4] - 2025-06-21
See merge request treez-inc/engineering/back-of-house/product-control-mfe!678

### Description
#970

### Personal
Co-Authors

Merged By
Lucas Giraldelli <<EMAIL>>

Approvers
Approved-by: Andrew Bishop <<EMAIL>>

## [3.45.3] - 2025-06-20
See merge request treez-inc/engineering/back-of-house/product-control-mfe!677

### Description
Name was being used on the wrong property level (inside details) on SKU object.

Related to #969

### Personal
Co-Authors

Merged By
Lucas Giraldelli <<EMAIL>>

Approvers
Approved-by: Andrew Bishop <<EMAIL>>

## [3.45.2] - 2025-06-19
See merge request treez-inc/engineering/back-of-house/product-control-mfe!676

### Description
Fixes issues with the "Same as Total Amount" toggle in the product variant form

### Fixed
- Toggle state properly reflects when amount equals total THC value  
- Removed unnecessary re-renders and duplicate setValue calls
- onBlur function is now only applied to the totalMgThc field rather than all compliance fields

### Changed
- Reorganized useEffect hooks for better clarity and proper dependencies
- Removed unnecessary toUpperCase calls and extraCheck prop in HookFormSwitch

### Close Issues

Closes #967

### Personal
Co-Authors

Merged By
Andrew Bishop <<EMAIL>>

Approvers
Approved-by: Leo Belanger <<EMAIL>>

## [3.45.1] - 2025-06-18
See merge request treez-inc/engineering/back-of-house/product-control-mfe!674

### Description
Partial revert of field validation changes for infused products.

### Changed
- Reverted infused subcategory validation requirements for total flower weight and total concentrate weight

### Close Issues

Closes #966

### Personal
Co-Authors

Merged By
Andrew Bishop <<EMAIL>>

Approvers
Approved-by: Leo Belanger <<EMAIL>>
Approved-by: Miguel Ganoza <<EMAIL>>

## [3.45.0] - 2025-06-18
See merge request treez-inc/engineering/back-of-house/product-control-mfe!673

### Description
This PR makes Total Flower Weight and Total Concentrate Weight required when:
-  Category: Flower | Subcategory: Infused Flower;
-  Category: Preroll | Subcategory: Infused;
-  Category: Preroll | Subcategory: Infused Blunt;

Also removes tooltip from total mg THC input and removes `"*"` from label on inputs total flower and total concentrate;

Related to #965

### Personal
Co-Authors

Merged By
Lucas Giraldelli <<EMAIL>>

Approvers
Approved-by: Miguel Ganoza <<EMAIL>>

## [3.44.0] - 2025-06-18
See merge request treez-inc/engineering/back-of-house/product-control-mfe!670

### Description
Adds same as amount toggle, which binds total amount and total mg thc(making it uneditable) under some conditions or if you turn it on: #946 

![image](/uploads/939df938a9367fc58a4f4c7078b02f20/image.png)

Also strips any fields that aren't required for bulk #921

### Personal
Co-Authors

Merged By
Lucas Giraldelli <<EMAIL>>

Approvers
Approved-by: Andrew Bishop <<EMAIL>>

## [3.43.1] - 2025-06-17
See merge request treez-inc/engineering/back-of-house/product-control-mfe!672

### Description
#### Added

* Added search input to the Pricing page

### Fixed

* Aligned "Add Product" Button with "Clear Filter" button

### Close Issues

Related to #964

### Personal
Co-Authors

Merged By
Miguel Ganoza <<EMAIL>>

Approvers
Approved-by: Andrew Bishop <<EMAIL>>
Approved-by: Lucas Giraldelli <<EMAIL>>

## [3.43.0] - 2025-06-16
See merge request treez-inc/engineering/back-of-house/product-control-mfe!665

### Description
### Related Issues

* Restrict Scrollbar to Product Grid Only [#933](https://gitlab.com/treez-inc/engineering/back-of-house/product-control-mfe/-/issues/933)
* Remove Strain Column from Product Grid #936 
* Move "Last Updated" Column to Far Right of Grid #934 
* Add Divider Lines Above and Below Grid Headers #937 
* Adjust Search Box Position, Size, and Label #938 
* Relocate "Add Product" Button to be right aligned with filters #939 
* Display SKU Name in Expanded Product Row #935 
* Update Product Grid Filters - Product Category / Subcategory / Classification #929
* Remove Product Category Icon Filters #930

### Close Issues

Related to

* #939
* #938
* #937
* #936
* #935
* #934
* #933
* #930 
* #929 
* #930
* #930

### Personal
Co-Authors

Merged By
Miguel Ganoza <<EMAIL>>

Approvers
Approved-by: Andrew Bishop <<EMAIL>>
Approved-by: Lucas Giraldelli <<EMAIL>>

## [3.42.2] - 2025-06-12
See merge request treez-inc/engineering/back-of-house/product-control-mfe!668

### Description
Looks like we had more places using the `sort` mutation method on `product/sku.images`, just fixed these other places as well.


#960

### Personal
Co-Authors

Merged By
Lucas Giraldelli <<EMAIL>>

Approvers
Approved-by: Leo Belanger <<EMAIL>>
Approved-by: Andrew Bishop <<EMAIL>>

## [3.42.1] - 2025-06-12
See merge request treez-inc/engineering/back-of-house/product-control-mfe!667

### Description
After adding immer, we have some access to product.images in `ProductInfoForm` that's performing some mutations in the original value instead of just returning a new one.


#960

### Personal
Co-Authors

Merged By
Lucas Giraldelli <<EMAIL>>

Approvers
Approved-by: Andrew Bishop <<EMAIL>>

## [3.42.0] - 2025-06-10
See merge request treez-inc/engineering/back-of-house/product-control-mfe!661

### Description
Removes the disabling variable for bulk subcategory products, now instead we show a modal telling the person to change the pricing method to flat.

Related to #898

### Personal
Co-Authors

Merged By
Lucas Giraldelli <<EMAIL>>

Approvers
Approved-by: Andrew Bishop <<EMAIL>>
Approved-by: Leo Belanger <<EMAIL>>

## [3.41.0] - 2025-06-10
See merge request treez-inc/engineering/back-of-house/product-control-mfe!641

### Description
Adds feature to generate SKU name automatically or use a custom name.

This tickets also includes some other fixes: 
- Navigation circles z-index above side menu #883:
   ![image](/uploads/4bfcd7f671e08ac5d0ed3401e9c6c39b/image.png)
- Also fixes default values for this ticket #941 

Related to #859

### Personal
Co-Authors

Merged By
Lucas Giraldelli <<EMAIL>>

Approvers
Approved-by: Andrew Bishop <<EMAIL>>

## [3.40.0] - 2025-05-29
See merge request treez-inc/engineering/back-of-house/product-control-mfe!666

### Description
This PR allows non-inv subcategory to be set, while both schema and input would not allow.

This code explicit sets a condition for when you submit non-inv products, in case we other edge-cases.

Related to #926

### Personal
Co-Authors

Merged By
Lucas Giraldelli <<EMAIL>>

Approvers
Approved-by: Andrew Bishop <<EMAIL>>

## [3.39.1] - 2025-05-29
See merge request treez-inc/engineering/back-of-house/product-control-mfe!664

### Description
### Fixed

* Fixed Flaky e2e tests cases.

### Close Issues

Related to #903

### Personal
Co-Authors

Merged By
Miguel Ganoza <<EMAIL>>

Approvers
Approved-by: Andrew Bishop <<EMAIL>>

## [3.39.0] - 2025-05-28
See merge request treez-inc/engineering/back-of-house/product-control-mfe!658

### Description
Changes the order for hide menu toggle, remove double sku title, remove amount per piece calc, sortByCustom/category and break input lines by category and now we show the total amount field in all scenarios, but disable for bulk products;

This ticket also removes the `Accordion` we have wrapping the SKU section.

Hide menu toggle

![image](/uploads/f2b233232b7f33503937f797c3908d55/image.png){width=469 height=160}

Cartridge

![image](/uploads/edca21ca32f74eba91ea861233086ead/image.png)

Packaging Goods

![image](/uploads/25aa9b76db22eadffbaef94ab7076d9a/image.png)

Flower

![image](/uploads/7be51c9817bfc481e41e6beead6e3278/image.png)

Also all other products got THC - CBD swapped.

Related to #899

### Personal
Co-Authors

Merged By
Lucas Giraldelli <<EMAIL>>

Approvers
Approved-by: Miguel Ganoza <<EMAIL>>

## [3.38.2] - 2025-05-21
See merge request treez-inc/engineering/back-of-house/product-control-mfe!644

### Description
### Removed

* Removed all unused files

### Close Issues

Related to #888

### Personal
Co-Authors

Merged By
Miguel Ganoza <<EMAIL>>

Approvers
Approved-by: Chandra Putta <<EMAIL>>
Approved-by: Lucas Giraldelli <<EMAIL>>

## [3.38.1] - 2025-05-20
See merge request treez-inc/engineering/back-of-house/product-control-mfe!663

### Description
This PR resolves the persistent "Failed to decode downloaded font" warnings by replacing deprecated file-loader usage with Webpack 5’s native asset/resource module.

Related to #900

### Personal
Co-Authors

Merged By
Lucas Giraldelli <<EMAIL>>

Approvers
Approved-by: Miguel Ganoza <<EMAIL>>
Approved-by: Andrew Bishop <<EMAIL>>

## [3.38.0] - 2025-05-19
See merge request treez-inc/engineering/back-of-house/product-control-mfe!656

### Description
Adds knip, a tool to see any unused types, files, deps and so on, resulting in a cleaner repo.

Related to #889

### Personal
Co-Authors
Co-authored-by: Raissa Bergamini <<EMAIL>>

Merged By
Lucas Giraldelli <<EMAIL>>

Approvers
Approved-by: Miguel Ganoza <<EMAIL>>

## [3.37.3] - 2025-05-15
See merge request treez-inc/engineering/back-of-house/product-control-mfe!660

### Description
### Fixed

* fixed console errors for both product and pricing page

### Close Issues

Related to #897

### Personal
Co-Authors
Co-authored-by: Miguel-C22 <<EMAIL>>

Merged By
Andrew Bishop <<EMAIL>>

Approvers
Approved-by: Andrew Bishop <<EMAIL>>

## [3.37.2] - 2025-05-15
See merge request treez-inc/engineering/back-of-house/product-control-mfe!659

### Description
Fix for bulk products intermittently showing non-bulk sku form. Also updated the style for subcategory dropdown in product information also added some `.vscode` default settings and extensions recommendations

Related to #902 #901

### Personal
Co-Authors
Co-authored-by: Lucas Giraldelli <<EMAIL>>

Merged By
Andrew Bishop <<EMAIL>>

Approvers
Approved-by: Andrew Bishop <<EMAIL>>

## [3.37.1] - 2025-05-14
See merge request treez-inc/engineering/back-of-house/product-control-mfe!657

### Description
Fixing some tests, adding TODOs for others to come back and fix.

### Fixed
* E2E test suite should now be passing in CI
* Fixed TS types and typechecking in the cypress folder

### Close Issues

Related to #486

### Personal
Co-Authors

Merged By
Andrew Bishop <<EMAIL>>

Approvers
Approved-by: Miguel Ganoza <<EMAIL>>

## [3.37.0] - 2025-05-09
See merge request treez-inc/engineering/back-of-house/product-control-mfe!655

### Description
Update the CI stages so that tags run automatically after merge to the main branch.

### Changed
* ordering of gitlab CI steps, to prevent needing to run the full pipeline on merge commits

### Close Issues

Related to #896

### Personal
Co-Authors

Merged By
Andrew Bishop <<EMAIL>>

Approvers
Approved-by: Lucas Giraldelli <<EMAIL>>
Approved-by: Leo Belanger <<EMAIL>>

## [3.36.2] - 2025-05-08
See merge request treez-inc/engineering/back-of-house/product-control-mfe!654

### Description
### Changed

* Adjusted the styling so the value within the SubcategoryHookFormSelector is aligned and has the same spacing as the other selectors.

### Close Issues

Related to #892

### Personal
Co-Authors
Co-authored-by: Miguel-C22 <<EMAIL>>

Merged By
Andrew Bishop <<EMAIL>>

Approvers
Approved-by: Andrew Bishop <<EMAIL>>

## [3.36.1] - 2025-05-08
See merge request treez-inc/engineering/back-of-house/product-control-mfe!651

### Description
Description goes here
I think the problem was related to how product sub categories were saving:

```ts
    const productUpdate = {
        ...updatedProduct,
        images: updateImagesToProduct(imageResult.result),
        productAttributes: updatedProductAttributes.filter((a) => !!a.id),
        ...(productSubCategory && { productSubCategory }),
    };
```


Related to #895

### Personal
Co-Authors
Co-authored-by: Lucas Giraldelli <<EMAIL>>

Merged By
Andrew Bishop <<EMAIL>>

Approvers
Approved-by: Andrew Bishop <<EMAIL>>

## [3.36.0] - 2025-05-07
See merge request treez-inc/engineering/back-of-house/product-control-mfe!649

### Description
Description goes here

### Added

* Added deli style description to certain subcategories within the drop down

### Changed

* `HookFormSelect` component. Was originally using the Treez Select component but I had to change it to MUI Select component for this to work. 

### Close Issues

Related to #892

### Personal
Co-Authors

Merged By
Miguel Ganoza <<EMAIL>>

Approvers
Approved-by: Andrew Bishop <<EMAIL>>
Approved-by: Lucas Giraldelli <<EMAIL>>

## [3.35.0] - 2025-05-07
See merge request treez-inc/engineering/back-of-house/product-control-mfe!600

### Description
### Removed

* Cypress Screen shots

### Fixed

* All cypress tests are fixed and should be passing expect for anything that has to do with pricing and merging. Those will need to be fixed at a later time.

**Folders that are passing test cases**

* Everything In `CreateProduct` folder except for the file `CreateProductPricing`
* Everything in `EditProduct` folder except for the file `EditProductPricing`
* Everything in `ProductControl`
* All files that are not in subfolders and located in the `e2e` test folder

**Folders that are not passing and still have yet to be changed**

* Pricing folder
* MergeProduct folder

### Close Issues

Related to #486

### Personal
Co-Authors

Merged By
Miguel Ganoza <<EMAIL>>

Approvers
Approved-by: Andrew Bishop <<EMAIL>>
Approved-by: Lucas Giraldelli <<EMAIL>>

## [3.34.2] - 2025-05-07
See merge request treez-inc/engineering/back-of-house/product-control-mfe!648

### Description
Patch MR to trigger tag for previous merge: https://gitlab.com/treez-inc/engineering/back-of-house/product-control-mfe/-/merge_requests/647

### Close Issues

Related to #891

### Personal
Co-Authors

Merged By
Andrew Bishop <<EMAIL>>

Approvers
Approved-by: Leo Belanger <<EMAIL>>
Approved-by: Lucas Giraldelli <<EMAIL>>

## [3.34.1] - 2025-05-05
See merge request treez-inc/engineering/back-of-house/product-control-mfe!643

### Description
### Details 
Some of the compliance keys was thorwing error when details would index using one of them, so we are just safe checking it.


Related to #887

### Personal
Co-Authors
Co-authored-by: Lucas Giraldelli <<EMAIL>>

Merged By
Andrew Bishop <<EMAIL>>

Approvers
Approved-by: Leo Belanger <<EMAIL>>
Approved-by: Andrew Bishop <<EMAIL>>

## [3.34.0] - 2025-05-01
See merge request treez-inc/engineering/back-of-house/product-control-mfe!642

### Description
Displays the tier label for tier-priced products and their variants

### Changed
* Use price tier label in API response for 'Price' column (instead of variant base price range) when product is tier priced

### Close Issues

Related to #885

### Personal
Co-Authors

Merged By
Andrew Bishop <<EMAIL>>

Approvers
Approved-by: Miguel Ganoza <<EMAIL>>

## [3.33.2] - 2025-04-29
See merge request treez-inc/engineering/back-of-house/product-control-mfe!640

### Description
Fix for currency converter in the new pricing form

### Changed
* Using existing util function for converting dollars to cents

### Fixed
* New pricing form util not rounding correctly (example: `8.96` and `8.97` converted to floating points)

### Close Issues

Related to #884

### Personal
Co-Authors

Merged By
Andrew Bishop <<EMAIL>>

Approvers
Approved-by: Lucas Giraldelli <<EMAIL>>

## [3.33.1] - 2025-04-28
See merge request treez-inc/engineering/back-of-house/product-control-mfe!638

### Description
Must not navigate without creating the product in the form.

### Fixed
* Nav issue in Product creation flow

### Close Issues

Related to #881

### Personal
Co-Authors

Merged By
Chandra Putta <<EMAIL>>

Approvers
Approved-by: Silju M C <<EMAIL>>
Approved-by: Lucas Giraldelli <<EMAIL>>
Approved-by: Andrew Bishop <<EMAIL>>

## [3.33.0] - 2025-04-25
See merge request treez-inc/engineering/back-of-house/product-control-mfe!637

### Description
This PR also hides `SKUSelector` when the product is bulk, as requested.

Related to #874

### Personal
Co-Authors
Co-authored-by: Lucas Giraldelli <<EMAIL>>
Co-authored-by: Leo Belanger <<EMAIL>>

Merged By
Silju M C <<EMAIL>>

Approvers
Approved-by: Andrew Bishop <<EMAIL>>
Approved-by: Silju M C <<EMAIL>>

## [3.32.0] - 2025-04-23
See merge request treez-inc/engineering/back-of-house/product-control-mfe!607

### Description
Price Tier MVP

### Personal
Co-Authors
Co-authored-by: Lucas Giraldelli <<EMAIL>>
Co-authored-by: Chandra Putta <<EMAIL>>
Co-authored-by: Leo Belanger <<EMAIL>>
Co-authored-by: Miguel Ganoza <<EMAIL>>
Co-authored-by: Andrew Bishop <<EMAIL>>

Merged By
Silju M C <<EMAIL>>

Approvers
Approved-by: Chandra Putta <<EMAIL>>

## [3.31.1] - 2025-04-17
See merge request treez-inc/engineering/back-of-house/product-control-mfe!634

### Description
In the sku section form, the sku field for sample and promo could not be removed earlier. This change gives the user ability to remove sku field value for sample and promo in the form.

### Changed
* Provides ability to remove sku for sample and promo


### Close Issues

Related to #866

### Personal
Co-Authors

Merged By
Chandra Putta <<EMAIL>>

Approvers
Approved-by: Silju M C <<EMAIL>>

## [3.31.0] - 2025-03-20
See merge request treez-inc/engineering/back-of-house/product-control-mfe!606

### Description
Reverts the following commits from main branch: 
- 45458314: [Minor] Flat Price  - Create/Edit form pricing tier integration.
- bbc1010c: [Minor] Feature flag enabled for bulk flower

Adds hotfix for pricing tab issue when setting sample / promo prices
- See https://treezio.slack.com/archives/CCH23T3NZ/p1742496524712319

### Close Issues
#826

### Personal
Co-Authors

Merged By
Andrew Bishop <<EMAIL>>

Approvers
Approved-by: Leo Belanger <<EMAIL>>

## [3.30.0] - 2025-03-18
See merge request treez-inc/engineering/back-of-house/product-control-mfe!571

### Description
Related to #800

### Personal
Co-Authors
Co-authored-by: Chandra Putta <<EMAIL>>
Co-authored-by: Miguel-C22 <<EMAIL>>
Co-authored-by: Lucas Giraldelli <<EMAIL>>

Merged By
Silju M C <<EMAIL>>

Approvers
Approved-by: Silju M C <<EMAIL>>
Approved-by: Andrew Bishop <<EMAIL>>

## [3.29.5] - 2025-03-14
See merge request treez-inc/engineering/back-of-house/product-control-mfe!601

### Description
Removed totalMgThc and totalMgCbd dependency on amount

### Changed
* Removed totalMgThc and totalMgCbd dependency on amount
* Removed automatically fill in totalMgThc and totalMgCbd with amount in any cases
* Removed totalMgThc and totalMgCbd as a required field in any cases
* Made totalMgThc and totalMgCbd editable in all cases

### Close Issues
https://gitlab.com/treez-inc/engineering/back-of-house/product-management-service/-/issues/1245

### Personal
Co-Authors

Merged By
Chandra Putta <<EMAIL>>

Approvers
Approved-by: Silju M C <<EMAIL>>

## [3.29.4] - 2025-03-12
See merge request treez-inc/engineering/back-of-house/product-control-mfe!599

### Description
Sorts skus in base pricing form

### Changed
* Sorts skus in base pricing form

### Close Issues

Related to #814

### Personal
Co-Authors

Merged By
Chandra Putta <<EMAIL>>

Approvers
Approved-by: Silju M C <<EMAIL>>
Approved-by: Raghul <<EMAIL>>

## [3.29.3] - 2025-03-12
See merge request treez-inc/engineering/back-of-house/product-control-mfe!596

### Description
The subcategory isn't reset when category is changed to a different value. This MR fixes that issue.

### Fixed
* subcategory reset issue

### Close Issues

Related to #812

### Personal
Co-Authors

Merged By
Chandra Putta <<EMAIL>>

Approvers
Approved-by: Silju M C <<EMAIL>>

## [3.29.2] - 2025-03-12
See merge request treez-inc/engineering/back-of-house/product-control-mfe!597

### Description
While creating a product, when navigating back from SKU section form to the Product Information Section form, the Save and Next button is disabled. This MR fixes that issue.

### Fixed
* Save and Next btn disabled issue

### Close Issues

Related to #813

### Personal
Co-Authors

Merged By
Chandra Putta <<EMAIL>>

Approvers
Approved-by: Silju M C <<EMAIL>>

## [3.29.1] - 2025-03-12
See merge request treez-inc/engineering/back-of-house/product-control-mfe!598

### Description
Fixes product details getting wiped out issue. While sending the update request for a product, the product details object is not being sent completely, there by losing the info that is not sent in the details object.

### Fixed
* product details getting wiped out issue

### Close Issues

Related to #816

### Personal
Co-Authors

Merged By
Chandra Putta <<EMAIL>>

Approvers
Approved-by: Silju M C <<EMAIL>>

## [3.29.0] - 2025-03-07
See merge request treez-inc/engineering/back-of-house/product-control-mfe!595

### Description
### Changed

* Changed Error message to "Price is required" instead of "Amount is required"

### Fixed

* Fixed error display. Error for both inputs should now be displayed for both promo and sample.

### 

### Close Issues

Related to #810

### Personal
Co-Authors
Co-authored-by: Miguel-C22 <<EMAIL>>

Merged By
Chandra Putta <<EMAIL>>

Approvers
Approved-by: Chandra Putta <<EMAIL>>

## [3.28.4] - 2025-03-06
See merge request treez-inc/engineering/back-of-house/product-control-mfe!594

### Description
Fixes transformation logic in sku section form. The current transformation logic incorrectly calls the `create` endpoint instead of `update` endpoint.

### Fixed
* Fixes transformation logic in sku section form


### Close Issues

### Personal
Co-Authors

Merged By
Chandra Putta <<EMAIL>>

Approvers
Approved-by: Silju M C <<EMAIL>>

## [3.28.3] - 2025-03-05

See merge request treez-inc/engineering/back-of-house/product-control-mfe!593

### Description
Fixes External Id field issue for sample and promo skus

### Fixed
* Fixes External Id field issue

### Close Issues

Related to #809

### Personal
Co-Authors

Merged By
Chandra Putta <<EMAIL>>

Approvers
Approved-by: Jishnu Mohan <<EMAIL>>
Approved-by: Silju M C <<EMAIL>>


## [3.28.2] - 2025-03-05

See merge request treez-inc/engineering/back-of-house/product-control-mfe!592

### Description
As mentioned in the gitlab issue, prices for values of `16.65` and `16.67` were being incorrectly rounded to large decimal values such as `1667.0000000000002` which caused the `sku` endpoint to throw validation errors and disallow users from setting valid prices.

### Changed

* I updated the rounding logic to use the existing `dollarsToCents` function which handles rounding correctly. 

### Close Issues

Related to #808

### Personal
Co-Authors
Co-authored-by: Leo Belanger <<EMAIL>>

Merged By
Chandra Putta <<EMAIL>>

Approvers
Approved-by: Silju M C <<EMAIL>>
Approved-by: Chandra Putta <<EMAIL>>


## [3.28.1] - 2025-03-04

See merge request treez-inc/engineering/back-of-house/product-control-mfe!590

### Description
Updates data differentiation logic to return only the fields that changed. The SKU section form in product creation flow currently sends the entire data in the payload. This MR will fix that issue.

### Changed
* Updates data differentiation logic to return only the fields that changed

### Close Issues

Related to #806

### Personal
Co-Authors

Merged By
Chandra Putta <<EMAIL>>

Approvers
Approved-by: Silju M C <<EMAIL>>


## [3.28.0] - 2025-02-17

See merge request treez-inc/engineering/back-of-house/product-control-mfe!584

### Description
### Added

* Added a utils for sorting data in alphabetical order 
* Product Information dropdown form fields (Subcategory, Brand, Classification) are not in alphabetical order 

### Fixed

* Fixed fields that had poor or mismatch styling
* All fields should look the same now

![image.png](/uploads/8e6635b4c058522e0e240b25231169b8/image.png){width=603 height=385}

### Close Issues

Related to #799

### Personal
Co-Authors

Merged By
Miguel Ganoza <<EMAIL>>

Approvers
Approved-by: Lucas Giraldelli <<EMAIL>>


## [3.27.0] - 2025-02-17

See merge request treez-inc/engineering/back-of-house/product-control-mfe!583

### Description
Description goes here

### Added

* Added a Pricing input for pricing samples and promos on the SKU section for when adding a new product
* Price is held in defaultPrices and is converted to cents.
* Added custom error handling for the required pricing for sample and promo

### Close Issues

Related to #798

### Personal
Co-Authors
Co-authored-by: Chandra Putta <<EMAIL>>

Merged By
Miguel Ganoza <<EMAIL>>

Approvers
Approved-by: Lucas Giraldelli <<EMAIL>>


## [3.26.0] - 2025-02-14

See merge request treez-inc/engineering/back-of-house/product-control-mfe!585

### Description
Removes old variant mutations and replaces VariantDto interface with SkuDto interface.

### Deprecated
* Old variant mutations

### Removed
* VariantDto interface


### Close Issues

Related to #801

### Personal
Co-Authors

Merged By
Chandra Putta <<EMAIL>>

Approvers
Approved-by: Lucas Giraldelli <<EMAIL>>


## [3.25.0] - 2025-02-05

See merge request treez-inc/engineering/back-of-house/product-control-mfe!580

### Description

Updates the product control grid to use the new SKU endpoints

### Changed

-   product control grid to use the new SKU endpoints

### Fixed

-   Padding of sku name in modal while activating/deactivating a sku
-   Padding of `amount per piece` text

### Close Issues

Related to #789

### Personal

Co-Authors

Merged By Chandra Putta <<EMAIL>>

Approvers Approved-by: Lucas Giraldelli <<EMAIL>> Approved-by: Silju M C <<EMAIL>>

## [3.24.0] - 2025-02-05

See merge request treez-inc/engineering/back-of-house/product-control-mfe!579

### Description

Updates the pricing page to use new SKU endpoints

### Changed

-   pricing page to use new SKU endpoints

### Close Issues

Related to #787

### Personal

Co-Authors

Merged By Chandra Putta <<EMAIL>>

Approvers Approved-by: Lucas Giraldelli <<EMAIL>> Approved-by: Silju M C <<EMAIL>>

## [3.23.0] - 2025-02-05

See merge request treez-inc/engineering/back-of-house/product-control-mfe!578

### Description

Updates the Base Pricing section form to work with new SKU endpoints

### Changed

-   Base Pricing section form to work with new SKU endpoints

### Close Issues

Related to #788

### Personal

Co-Authors

Merged By Chandra Putta <<EMAIL>>

Approvers Approved-by: Lucas Giraldelli <<EMAIL>> Approved-by: Silju M C <<EMAIL>>

## [3.22.0] - 2025-02-05

See merge request treez-inc/engineering/back-of-house/product-control-mfe!576

### Description

Update the SKU section form of Product Creation flow to transform the data right before submitting and make the form
work for the new create and update sku endpoints.

### Changed

-   SKU section form to work with new SKU endpoints

### Close Issues

Related to #786

### Personal

Co-Authors

Merged By Chandra Putta <<EMAIL>>

Approvers Approved-by: Lucas Giraldelli <<EMAIL>> Approved-by: Silju M C <<EMAIL>>

## [3.21.2] - 2025-02-03

See merge request treez-inc/engineering/back-of-house/product-control-mfe!582

### Description

Updates the product attribute mutations to happen in series rather than parallel, to fix a race condition in our Product
Attribute events which can cause PMRS to end up out-of-sync with the Product Management Service DB. A longer-term
solution will be handled with
https://gitlab.com/treez-inc/engineering/back-of-house/product-management-service/-/issues/1212

### Changed

-   Product Attribute mutation API calls to run in series rather than parallel.

### Close Issues

Related to #795

### Personal

Co-Authors

Merged By Andrew Bishop <<EMAIL>>

Approvers Approved-by: Leo Belanger <<EMAIL>>

## [3.21.1] - 2025-01-22

See merge request treez-inc/engineering/back-of-house/product-control-mfe!577

### Description

Fixes state issue in SKU section form of Product Creation flow

### Close Issues

https://gitlab.com/treez-inc/engineering/back-of-house/product-control-mfe/-/issues/790

### Personal

Co-Authors

Merged By Chandra Putta <<EMAIL>>

Approvers Approved-by: Lucas Giraldelli <<EMAIL>> Approved-by: Silju M C <<EMAIL>>

## [3.21.0] - 2025-01-21

See merge request treez-inc/engineering/back-of-house/product-control-mfe!575

### Description

Sync base sku details data to child skus details in SKU section of Product creation flow

### Changed

-   Updated SKU section form to update child sku details when base sku details change

### Close Issues

Related to #790

### Personal

Co-Authors

Merged By Chandra Putta <<EMAIL>>

Approvers Approved-by: Silju M C <<EMAIL>>

## [3.20.2] - 2025-01-17

See merge request treez-inc/engineering/back-of-house/product-control-mfe!574

### Description

Fixes child sku validation in SKU section of Product Creation flow. Since we removed the compliance fields for samples
and promos in the form, we need to modify their validation on the hidden fields to avoid unnecessary validation pop up.

### Fixed

-   Fixes child sku validation in SKU section of Product Creation flow.

### Close Issues

https://gitlab.com/treez-inc/engineering/back-of-house/product-control-mfe/-/issues/785

### Personal

Co-Authors

Merged By Chandra Putta <<EMAIL>>

Approvers Approved-by: Silju M C <<EMAIL>>

## [3.20.1] - 2025-01-16

See merge request treez-inc/engineering/back-of-house/product-control-mfe!573

### Description

Made a minor change to generate a new tag, since the previous MR didn't have a tag in the MR title.

### Close Issues

### Personal

Co-Authors

Merged By Chandra Putta <<EMAIL>>

Approvers Approved-by: subha ks <<EMAIL>>

## [3.20.0] - 2025-01-10

See merge request treez-inc/engineering/back-of-house/product-control-mfe!568

### Description

Visual changes to product edit form to apply SKU naming conventions and to prevent changing base SKU size fields in
promo/sample sections

### Changed

-   Renamed references to 'Size' in Product Edit form to 'SKU'

### Removed

-   Size-related form fields for Sample and Promo, to prevent overriding those values on the child product variant

### Images

**Old** <br /> ![image](/uploads/88decd6d231e7e3abd7865e1f6eff26b/image.png){width=919 height=712}

**New** <br /> ![image](/uploads/4ecdb7bf57ca585d90a7845492cae9e7/image.png){width=919 height=712}

### Close Issues

#782

### Personal

Co-Authors

Merged By Lucas Giraldelli <<EMAIL>>

Approvers Approved-by: Miguel Ganoza <<EMAIL>> Approved-by: Andrew Bishop <<EMAIL>>

## [3.19.3] - 2025-01-08

See merge request treez-inc/engineering/back-of-house/product-control-mfe!567

### Description

Search Fix - Updated debounce logic to fix search issue

### Close Issues

Related to #753

### Personal

Co-Authors

Merged By Silju M C <<EMAIL>>

Approvers Approved-by: Lucas Giraldelli <<EMAIL>> Approved-by: Chandra Putta <<EMAIL>>

## [3.19.2] - 2025-01-03

See merge request treez-inc/engineering/back-of-house/product-control-mfe!566

### Description

Brand Missing message updated and added logic to exclude some categories

### Close Issues

Related to #749

### Personal

Co-Authors

Merged By Silju M C <<EMAIL>>

Approvers Approved-by: Jishnu Mohan <<EMAIL>>

## [3.19.1] - 2024-12-18

See merge request treez-inc/engineering/back-of-house/product-control-mfe!563

### Description

Added safe navigation operator in Variant form

### Fixed

-   Variant form

### Close Issues

### Personal

Co-Authors

Merged By Chandra Putta <<EMAIL>>

Approvers Approved-by: Silju M C <<EMAIL>>

## [3.19.0] - 2024-12-05

See merge request treez-inc/engineering/back-of-house/product-control-mfe!561

### Description

Added prompt message on removing brand

### Close Issues

Related to #749

### Personal

Co-Authors

Merged By Silju M C <<EMAIL>>

Approvers Approved-by: Chandra Putta <<EMAIL>>

## [3.18.1] - 2024-11-28

See merge request treez-inc/engineering/back-of-house/product-control-mfe!559

### Description

When a variant name field is left empty during updates, the system incorrectly assigns the default name to it, instead
of using the most recently updated variant name.

### Closed Issues

#752

### Fixed

Fixed empty variant name updates takes the most recently updated variant name.

## MR Requirements

-   [x] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
-   [x] You added a demo video or a screenshot for any visual changes to a comment on the MR
-   [x] Your changes meet all of the Acceptance Criteria listed in the user story
-   [x] You reviewed your own MR and left comments where needed to provide context for reviewers
-   [x] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type
        can be narrowed down

### Personal

Co-Authors

Merged By Pranoy Sebastian <<EMAIL>>

Approvers Approved-by: Silju M C <<EMAIL>>

## [3.18.0] - 2024-11-27

See merge request treez-inc/engineering/back-of-house/product-control-mfe!556

### Description

Description goes here

Addeed an Expand all Icon to the table header, When pressed it will display all product varients that are on the page.

![image](/uploads/8b6d6b1ae24d07a55d55d9478c860577/image.png){width=730 height=430}

Related to #644

### Personal

Co-Authors

Merged By Miguel Ganoza <<EMAIL>>

Approvers Approved-by: Lucas Giraldelli <<EMAIL>>

## [3.17.0] - 2024-11-27

See merge request treez-inc/engineering/back-of-house/product-control-mfe!557

### Description

Description goes here

### Added

-   for new features.

### Changed

-   for changes in existing functionality.

### Deprecated

-   for soon-to-be removed features.

### Removed

-   for now removed features.

### Fixed

-   for any bug fixes.

### Security

-   in case of vulnerabilities.

### Close Issues

Related to #751

### Personal

Co-Authors

Merged By Miguel Ganoza <<EMAIL>>

Approvers Approved-by: Lucas Giraldelli <<EMAIL>>

## [3.16.1] - 2024-11-26

See merge request treez-inc/engineering/back-of-house/product-control-mfe!558

### Description

Upgraded component library.

### Close Issues

### Personal

Co-Authors

Merged By Chandra Putta <<EMAIL>>

Approvers Approved-by: Pranoy Sebastian <<EMAIL>>

## [3.16.0] - 2024-11-20

See merge request treez-inc/engineering/back-of-house/product-control-mfe!553

### Description

Description goes here

### Added

-   for new features.

### Changed

-   for changes in existing functionality.

### Deprecated

-   for soon-to-be removed features.

### Removed

-   for now removed features.

### Fixed

-   for any bug fixes.

### Security

-   in case of vulnerabilities.

### Close Issues

Related to #743

### Personal

Co-Authors

Merged By Miguel Ganoza <<EMAIL>>

Approvers Approved-by: Lucas Giraldelli <<EMAIL>>

## [3.15.2] - 2024-10-24

See merge request treez-inc/engineering/back-of-house/product-control-mfe!543

### Description

Patches an issue when the user enters a string of empty characters. Added logic to trim the whitespace before and after
the string, and then if the result is an empty string, it will use the default generated name.

This work goes along with the backend changes in PMS, but is not blocked/a blocker for the MR:
https://gitlab.com/treez-inc/engineering/back-of-house/product-management-service/-/merge_requests/851

### Personal

Co-Authors

Merged By Leo Belanger <<EMAIL>>

Approvers Approved-by: Andrew Bishop <<EMAIL>>

## [3.15.1] - 2024-10-22

See merge request treez-inc/engineering/back-of-house/product-control-mfe!547

### Description

UOM, Size, Amount, Attribute & Product images changes to Product control MFE

### Personal

Co-Authors

Merged By subha ks <<EMAIL>>

Approvers Approved-by: Silju M C <<EMAIL>> Approved-by: Chandra Putta <<EMAIL>>

## [3.15.0] - 2024-10-11

See merge request treez-inc/engineering/back-of-house/product-control-mfe!546

### Description

This is for forcing last changes into QA

### Personal

Co-Authors Co-authored-by: Lucas Giraldelli <<EMAIL>>

Merged By Andrew Bishop <<EMAIL>>

Approvers Approved-by: Andrew Bishop <<EMAIL>>

## [3.14.1] - 2024-10-07

See merge request treez-inc/engineering/back-of-house/product-control-mfe!533

### Description

The categories of Beverage, Edible, Pill, Tincture and Topical all should be measured using the UoM of "Mgs". MGs should
be autoselected and UoM should be disabled from selecting anything else.

When a user types in the amount of milligrams in the product, auto populate the totalMgThc Field. This field should be
disabled, with it only editable through the amount field.

### _\*\*CBD Exception_

If the product is a CBD type product, rather than totalMgTHC being updated to the amount automatically, totalMgCBD
should be disabled from being edited and automatically updated.

### _\*\*Imported or Historical Data Exception_

Not all data will have the variants be measured in MGs. Any products that are migrated or historical in these categories
should load in the product form fine. But, when the user edits the product.. they should be forced to edit it to Mgs -
forcing them to correct the data.

### Closed Issues

#728

### Changed

-   The categories of Beverage, Edible, Pill, Tincture and Topical all should be measured using the UoM of "Mgs". MGs
    should be autoselected and UoM should be disabled from selecting anything else.
-   When a user types in the amount of milligrams in the product, auto populate the totalMgThc Field. This field should
    be disabled, with it only editable through the amount field.
-   If the product is a CBD type product, rather than totalMgTHC being updated to the amount automatically, totalMgCBD
    should be disabled from being edited and automatically updated.

## MR Requirements

-   [x] If you made visual changes, you have completed the design review process and your changes have been approved
-   [x] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
-   [x] You added a demo video or a screenshot for any visual changes to a comment on the MR
-   [x] Your changes meet all of the Acceptance Criteria listed in the user story
-   [x] You reviewed your own MR and left comments where needed to provide context for reviewers
-   [x] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type
        can be narrowed down

### Personal

Co-Authors

Merged By Pranoy Sebastian <<EMAIL>>

Approvers Approved-by: Andrew Bishop <<EMAIL>> Approved-by: Chandra Putta <<EMAIL>>

## [3.14.0] - 2024-10-03

See merge request treez-inc/engineering/back-of-house/product-control-mfe!544

### Description

### Removed

-   `useEffect` dependency causing a render loop

### Fixed

-   Inability to search in sandbox/build envs

### Close Issues

### Personal

Co-Authors Co-authored-by: Lucas Giraldelli <<EMAIL>>

Merged By Andrew Bishop <<EMAIL>>

Approvers Approved-by: Andrew Bishop <<EMAIL>>

## [3.13.0] - 2024-10-01

See merge request treez-inc/engineering/back-of-house/product-control-mfe!541

### Description

### Changed

-   Changed the color for the override, to be clearer which one is selected.

### Fixed

-   Now uses value instead of defaultValue (is a controlled component)

### Close Issues

Related to #741

### Personal

Co-Authors

Merged By Lucas Giraldelli <<EMAIL>>

Approvers Approved-by: Leo Belanger <<EMAIL>>

## [3.12.2] - 2024-10-01

See merge request treez-inc/engineering/back-of-house/product-control-mfe!540

### Description

Merch category when Size is missing throws error "variants\[2\].merchandiseSize" must be a string

### Closed Issues

#740

### Fixed

Fixed size validation error message of Merch category

## MR Requirements

-   [x] If you made visual changes, you have completed the design review process and your changes have been approved
-   [x] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
-   [x] You added a demo video or a screenshot for any visual changes to a comment on the MR
-   [x] Your changes meet all of the Acceptance Criteria listed in the user story
-   [x] You reviewed your own MR and left comments where needed to provide context for reviewers
-   [x] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type
        can be narrowed down

### Personal

Co-Authors

Merged By Pranoy Sebastian <<EMAIL>>

Approvers Approved-by: Chandra Putta <<EMAIL>>

## [3.12.1] - 2024-10-01

See merge request treez-inc/engineering/back-of-house/product-control-mfe!542

### Description

Fix - Image order change on edit image

### Personal

Co-Authors

Merged By Silju M C <<EMAIL>>

Approvers Approved-by: Chandra Putta <<EMAIL>>

## [3.12.0] - 2024-09-30

See merge request treez-inc/engineering/back-of-house/product-control-mfe!539

### Description

Image reordering for product and variants

### Close Issues

Related to #636

### Personal

Co-Authors

Merged By Silju M C <<EMAIL>>

Approvers Approved-by: Chandra Putta <<EMAIL>>

## [3.11.0] - 2024-09-26

See merge request treez-inc/engineering/back-of-house/product-control-mfe!538

### Description

### Added

-   Added lodash types (we already had the lib itself)

### Changed

-   Debounce for search field
-   Go back from details retain query params
-   Renamed pricing page (inside details)

### Close Issues

#735 <br /> #736 <br /> #734 <br />

### Personal

Co-Authors

Merged By Lucas Giraldelli <<EMAIL>>

Approvers Approved-by: Andrew Bishop <<EMAIL>>

## [3.10.2] - 2024-09-26

See merge request treez-inc/engineering/back-of-house/product-control-mfe!537

### Description

Fix - Merch sample and promo disappearing issue fix

### Close Issues

Related to #738

### Personal

Co-Authors

Merged By Silju M C <<EMAIL>>

Approvers Approved-by: Pranoy Sebastian <<EMAIL>> Approved-by: Chandra Putta <<EMAIL>>

## [3.10.1] - 2024-09-23

See merge request treez-inc/engineering/back-of-house/product-control-mfe!534

### Description

Updates sample and promo names when main variant name is updated in the form.

### Changed

-   Updates sample and promo names when main variant name is updated

### Fixed

-   Missing key in one of the components
-   Uom validation error message

### Close Issues

Related to #730 https://gitlab.com/treez-inc/engineering/back-of-house/product-control-mfe/-/issues/731

### Personal

Co-Authors Co-authored-by: Chandra Putta <<EMAIL>>

Merged By Silju M C <<EMAIL>>

Approvers Approved-by: Silju M C <<EMAIL>>

## [3.10.0] - 2024-09-20

See merge request treez-inc/engineering/back-of-house/product-control-mfe!535

### Description

Moves add button to the bottom right of the page (as a floating button)

### Changed

Pagination now is on bottom left to better accommodate floating button "Add Product"

### Close Issues

Related to #732

### Personal

Co-Authors Co-authored-by: Lucas Giraldelli <<EMAIL>>

Merged By Lucas Giraldelli <<EMAIL>>

Approvers Approved-by: Miguel Ganoza <<EMAIL>> Approved-by: Kyler Wood <<EMAIL>>

## [3.9.0] - 2024-09-20

See merge request treez-inc/engineering/back-of-house/product-control-mfe!531

### Description

Description goes here

### Added

-   New brand autocomplete and values sorted alphabetically

### Close Issues

Related to #672

### Personal

Co-Authors Co-authored-by: Lucas Giraldelli <<EMAIL>>

Merged By Lucas Giraldelli <<EMAIL>>

Approvers Approved-by: Andrew Bishop <<EMAIL>>

## [3.8.3] - 2024-09-20

See merge request treez-inc/engineering/back-of-house/product-control-mfe!509

### Description

Description goes here

### Added

-   for new features.

### Changed

-   for changes in existing functionality.

### Deprecated

-   for soon-to-be removed features.

### Removed

-   for now removed features.

### Fixed

-   for any bug fixes.

### Security

-   in case of vulnerabilities.

### Close Issues

Related to #684

### Personal

Co-Authors Co-authored-by: Miguel-C22 <<EMAIL>>

Merged By Lucas Giraldelli <<EMAIL>>

Approvers Approved-by: Lucas Giraldelli <<EMAIL>>

## [3.8.2] - 2024-09-19

See merge request treez-inc/engineering/back-of-house/product-control-mfe!532

### Description

Updates the Variant Size Label calculation to match the current backend logic (temp fix until PMS package can be
consumed)

### Fixed

-   Difference in variant label calc between frontend and backend

### Close Issues

Related to #729

### Personal

Co-Authors Co-authored-by: Andrew Bishop <<EMAIL>>

Merged By Chandra Putta <<EMAIL>>

Approvers Approved-by: Chandra Putta <<EMAIL>>

## [3.8.1] - 2024-09-18

See merge request treez-inc/engineering/back-of-house/product-control-mfe!530

### Description

Reverting due to https://treezio.slack.com/archives/C06QM5LFNDT/p1726696172345999

### Fixed

-   Variant search defect

### Close Issues

N/A

### Personal

Co-Authors Co-authored-by: Lucas Giraldelli <<EMAIL>>

Merged By Andrew Bishop <<EMAIL>>

Approvers Approved-by: Andrew Bishop <<EMAIL>>

## [3.8.0] - 2024-09-18

See merge request treez-inc/engineering/back-of-house/product-control-mfe!529

### Description

Forces a validation error and directs the user to update UoM field in Variants form in case of existing variants having
invalid `uom` values.

### Added

-   Logic To Force Update of UoMs Where Invalid in Variants form

### Changed

-   Removed the Size Definition for Plants, as it didn't make sense given that the amount and unit of measure fields are
    not enabled
-   Fixed some styling issues with the Variant Tabs header and the Variant size title (forcing vertical alignment of
    items in flex container) and the Hook Form Switch component (removing the hardcoded height from the component lib to
    fix alignment issues)

### Close Issues

Related to #727

### Personal

Co-Authors Co-authored-by: Chandra Putta <<EMAIL>>

Merged By Andrew Bishop <<EMAIL>>

Approvers Approved-by: Leo Belanger <<EMAIL>>

## [3.7.0] - 2024-09-18

See merge request treez-inc/engineering/back-of-house/product-control-mfe!496

### Description

Description goes here

### Added

-   New brand autocomplete and values sorted alphabetically

### Close Issues

Related to #672

### Personal

Co-Authors Co-authored-by: Lucas Giraldelli <<EMAIL>> Co-authored-by: Lucas Giraldelli
<<EMAIL>>

Merged By Leo Belanger <<EMAIL>>

Approvers Approved-by: Chandra Putta <<EMAIL>> Approved-by: Leo Belanger <<EMAIL>>

## [3.6.2] - 2024-09-13

See merge request treez-inc/engineering/back-of-house/product-control-mfe!528

### Description

Image saving issue fix on variants

### Close Issues

Related to #726

### Personal

Co-Authors

Merged By Silju M C <<EMAIL>>

Approvers Approved-by: Jishnu Mohan <<EMAIL>>

## [3.6.1] - 2024-09-12

See merge request treez-inc/engineering/back-of-house/product-control-mfe!527

### Description

-   Hide preview option in product form
-   Remove sort functionality for strain and classification columns in Product Control grid
-   Make totalMgThc mandatory field for certain categories

### Closed Issues

#722 #723 #724

### Changed

-   Hided preview option in product form
-   Removed sort functionality for strain and classification columns in Product Control grid
-   Updated totalMgThc mandatory field for certain categories

## MR Requirements

-   [x] If you made visual changes, you have completed the design review process and your changes have been approved
-   [x] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
-   [x] Your changes meet all of the Acceptance Criteria listed in the user story
-   [x] You reviewed your own MR and left comments where needed to provide context for reviewers
-   [x] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type
        can be narrowed down

### Personal

Co-Authors

Merged By Pranoy Sebastian <<EMAIL>>

Approvers Approved-by: Chandra Putta <<EMAIL>>

## [3.6.0] - 2024-09-11

See merge request treez-inc/engineering/back-of-house/product-control-mfe!526

### Description

Label Printer Prop added for variants to save printer labels

### Close Issues

Related to #718

### Personal

Co-Authors

Merged By Silju M C <<EMAIL>>

Approvers Approved-by: Chandra Putta <<EMAIL>>

## [3.5.0] - 2024-09-10

See merge request treez-inc/engineering/back-of-house/product-control-mfe!523

### Description

Updates default price of a variant also upon using Mass Price Update feature in Pricing page

### Changed

-   Updates default price of a variant also along with its entity prices while bulk updating prices

### Close Issues

https://gitlab.com/treez-inc/engineering/back-of-house/product-management-service/-/issues/1077

### Personal

Co-Authors

Merged By Chandra Putta <<EMAIL>>

Approvers Approved-by: Pranoy Sebastian <<EMAIL>>

## [3.4.0] - 2024-09-09

See merge request treez-inc/engineering/back-of-house/product-control-mfe!520

### Description

variant "+ Add Size" button validation

### Changed

-   Duplicate valid variant before the "+ Add Size" button is made available.

### Close Issues

#711

### Personal

Co-Authors

Merged By Raghul R Nair <<EMAIL>>

Approvers Approved-by: Chandra Putta <<EMAIL>> Approved-by: Silju M C <<EMAIL>>

## [3.4.0] - 2024-09-06

See merge request treez-inc/engineering/back-of-house/product-control-mfe!524

### Description

When in the product control page, when I click a product, I would like to be brought to the product form with the
current product open ready to be edited.

When I am in the product form editing a product, I want to see a preview button at the top like the one is mocked for
editing a strain resource.

### Closed Issues

#712

<!-- Be sure to delete unused headers and comments -->

### Added

Added product edit form when clicking each product in the grid Added product preview in the product edit form

## MR Requirements

-   [x] If you made visual changes, you have completed the design review process and your changes have been approved
-   [x] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
-   [x] Your changes meet all of the Acceptance Criteria listed in the user story
-   [x] You reviewed your own MR and left comments where needed to provide context for reviewers
-   [x] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type
        can be narrowed down

### Personal

Co-Authors

Merged By Pranoy Sebastian <<EMAIL>>

Approvers Approved-by: Lucas Giraldelli <<EMAIL>>

## [3.3.0] - 2024-09-06

See merge request treez-inc/engineering/back-of-house/product-control-mfe!525

### Description

Remove Kebab Menu from Product Grid

### Closed Issues

#716

### Deleted

Remove Kebab Menu from Product Grid

## MR Requirements

-   [x] If you made visual changes, you have completed the design review process and your changes have been approved
-   [x] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
-   [x] You added a demo video or a screenshot for any visual changes to a comment on the MR
-   [x] Your changes meet all of the Acceptance Criteria listed in the user story
-   [x] You reviewed your own MR and left comments where needed to provide context for reviewers
-   [x] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type
        can be narrowed down

### Personal

Co-Authors

Merged By Pranoy Sebastian <<EMAIL>>

Approvers Approved-by: Lucas Giraldelli <<EMAIL>>

## [3.2.0] - 2024-09-06

See merge request treez-inc/engineering/back-of-house/product-control-mfe!521

### Description

Remove SKU and show strain & classification in product grid"

### Closed Issues

#713

<!-- Be sure to delete unused headers and comments -->

### Added

Added Strain and Classification in product grid

### Deleted

Deleted SKU in product grid

## MR Requirements

-   [x] If you made visual changes, you have completed the design review process and your changes have been approved
-   [x] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
-   [x] Your changes meet all of the Acceptance Criteria listed in the user story
-   [x] You reviewed your own MR and left comments where needed to provide context for reviewers
-   [x] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type
        can be narrowed down

### Personal

Co-Authors

Merged By Pranoy Sebastian <<EMAIL>>

Approvers Approved-by: Lucas Giraldelli <<EMAIL>> Approved-by: Leo Belanger <<EMAIL>>

## [3.1.1] - 2024-09-05

See merge request treez-inc/engineering/back-of-house/product-control-mfe!522

### Description

Use external id is automatically checked when form validation happens in sizes tab

### Closed Issues

#717

### Fixed

Fixed external id automatically checked when form validation error occurs

## MR Requirements

-   [x] If you made visual changes, you have completed the design review process and your changes have been approved
-   [x] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
-   [x] Your changes meet all of the Acceptance Criteria listed in the user story
-   [x] You reviewed your own MR and left comments where needed to provide context for reviewers
-   [x] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type
        can be narrowed down

### Personal

Co-Authors

Merged By Pranoy Sebastian <<EMAIL>>

Approvers Approved-by: Chandra Putta <<EMAIL>> Approved-by: Silju M C <<EMAIL>>

## [3.1.0] - 2024-09-05

See merge request treez-inc/engineering/back-of-house/product-control-mfe!517

### Description

Added size definitions to the variant form

### Added

-   Size definition subheader component
-   Size definition text getter

### Removed

-   invalid category `Prepack`

### Close Issues

#706, #707, #708, #709

### Personal

Co-Authors

Merged By Leo Belanger <<EMAIL>>

Approvers Approved-by: Andrew Bishop <<EMAIL>> Approved-by: Lucas Giraldelli <<EMAIL>>

## [3.0.2] - 2024-09-05

See merge request treez-inc/engineering/back-of-house/product-control-mfe!519

### Description

Hide Variant Drop Down Arrow in Product Control If No Variants Available

### Closed Issues

#715

### Changed

Hide variant drop down arrow for empty variants in the product grid view

## MR Requirements

-   [x] If you made visual changes, you have completed the design review process and your changes have been approved
-   [x] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
-   [x] Your changes meet all of the Acceptance Criteria listed in the user story
-   [x] You reviewed your own MR and left comments where needed to provide context for reviewers
-   [x] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type
        can be narrowed down

### Personal

Co-Authors

Merged By Pranoy Sebastian <<EMAIL>>

Approvers Approved-by: Lucas Giraldelli <<EMAIL>> Approved-by: Andrew Bishop <<EMAIL>>
Approved-by: Leo Belanger <<EMAIL>>

## [3.0.1] - 2024-09-05

See merge request treez-inc/engineering/back-of-house/product-control-mfe!518

### Description

Asterisk Next to Each Required Field

### Closed Issues

#714

<!-- Be sure to delete unused headers and comments -->

### Added

Added asterisk for required fields in variant form Added default variant name if its null

### Changed

Changed variant schema validation for amount, uom and name

## MR Requirements

-   [x] If you made visual changes, you have completed the design review process and your changes have been approved
-   [x] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
-   [x] Your changes meet all of the Acceptance Criteria listed in the user story
-   [x] You reviewed your own MR and left comments where needed to provide context for reviewers
-   [x] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type
        can be narrowed down

### Personal

Co-Authors

Merged By Pranoy Sebastian <<EMAIL>>

Approvers Approved-by: Lucas Giraldelli <<EMAIL>> Approved-by: Andrew Bishop <<EMAIL>>

## [3.0.0] - 2024-08-28

See merge request treez-inc/engineering/back-of-house/product-control-mfe!491

### Description

Fix UoM and Amount on Size Definition in Product Form

### Closed Issues

[937](https://gitlab.com/treez-inc/engineering/back-of-house/product-management-service/-/issues/937)
[696](https://gitlab.com/treez-inc/engineering/back-of-house/product-control-mfe/-/issues/696)

### Added

Added name, uom, amount, unitCount and merchandiseSize to variant sizes

### Removed

Removed uom from the Product Info section. Removed variantProperties.

## MR Requirements

-   [x] If you made visual changes, you have completed the design review process and your changes have been approved
-   [x] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
-   [x] You added a demo video or a screenshot for any visual changes to a comment on the MR
-   [x] Your changes meet all of the Acceptance Criteria listed in the user story
-   [x] You reviewed your own MR and left comments where needed to provide context for reviewers
-   [x] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type
        can be narrowed down

### Personal

Co-Authors Co-authored-by: pranoysp <<EMAIL>> Co-authored-by: Chandra Putta <<EMAIL>>

Merged By Leo Belanger <<EMAIL>>

Approvers Approved-by: Andrew Bishop <<EMAIL>> Approved-by: Silju M C <<EMAIL>>

## [2.68.0] - 2024-08-20

See merge request treez-inc/engineering/back-of-house/product-control-mfe!490

### Description

New Pricing tab UI test cases Execute in Sandbox & Build

### Personal

Co-Authors

Merged By subha ks <<EMAIL>>

Approvers Approved-by: Chandra Putta <<EMAIL>>

## [2.67.0] - 2024-08-07

See merge request treez-inc/engineering/back-of-house/product-control-mfe!508

### Description

Variant image creation issue fix

### Personal

Co-Authors

Merged By Silju M C <<EMAIL>>

Approvers Approved-by: Chandra Putta <<EMAIL>>

## [2.66.0] - 2024-07-31

See merge request treez-inc/engineering/back-of-house/product-control-mfe!507

### Description

Added product images to variants by default

### Close Issues

Related to #669

### Personal

Co-Authors

Merged By Silju M C <<EMAIL>>

Approvers

## [2.65.0] - 2024-07-29

See merge request treez-inc/engineering/back-of-house/product-control-mfe!506

### Description

Variant - Validation issue fix in production

### Close Issues

### Personal

Co-Authors Co-authored-by: Silju M.C <<EMAIL>>

Merged By Chandra Putta <<EMAIL>>

Approvers Approved-by: Chandra Putta <<EMAIL>>

## [2.64.0] - 2024-07-26

See merge request treez-inc/engineering/back-of-house/product-control-mfe!505

### Description

Fix to enable save button on creating sample and variant

### Close Issues

Related to #680

### Personal

Co-Authors

Merged By Silju M C <<EMAIL>>

Approvers

## [2.63.0] - 2024-07-26

See merge request treez-inc/engineering/back-of-house/product-control-mfe!504

### Description

Sample creation issue fix

### Close Issues

Related to #680

### Personal

Co-Authors

Merged By Silju M C <<EMAIL>>

Approvers

## [2.62.0] - 2024-07-24

See merge request treez-inc/engineering/back-of-house/product-control-mfe!503

### Description

Sample and promo creation issue fix

### Close Issues

Related to #679

### Personal

Co-Authors

Merged By Silju M C <<EMAIL>>

Approvers

## [2.61.0] - 2024-07-24

See merge request treez-inc/engineering/back-of-house/product-control-mfe!502

### Description

Sample and promo creation issue fix

### Close Issues

Related to #679

### Personal

Co-Authors

Merged By Silju M C <<EMAIL>>

Approvers Approved-by: Chandra Putta <<EMAIL>>

## [2.60.1] - 2024-07-23

See merge request treez-inc/engineering/back-of-house/product-control-mfe!497

### Description

Currently product update request is triggered when an image is added or deleted and there is no data update for product.
This fix recognizes relevant data changes in the product information form, and sends api requests only for the updated
data (image, attribute, or product).

### Fixed

-   Unwanted api requests in product information form

### Close Issues

Related to #668

### Personal

Co-Authors

Merged By Chandra Putta <<EMAIL>>

Approvers Approved-by: Andrew Bishop <<EMAIL>> Approved-by: Silju M C <<EMAIL>>

## [2.60.0] - 2024-07-23

See merge request treez-inc/engineering/back-of-house/product-control-mfe!501

### Description

Fix - Create/Edit variant save button visibility updated

### Close Issues

Related to #675

### Personal

Co-Authors

Merged By Silju M C <<EMAIL>>

Approvers Approved-by: Chandra Putta <<EMAIL>>

## [2.59.1] - 2024-07-22

See merge request treez-inc/engineering/back-of-house/product-control-mfe!500

### Description

Save button disabled if there is no change on product edit screens

### Close Issues

Related to #667

### Personal

Co-Authors

Merged By Silju M C <<EMAIL>>

Approvers Approved-by: Chandra Putta <<EMAIL>>

## [2.59.0] - 2024-07-22

See merge request treez-inc/engineering/back-of-house/product-control-mfe!499

### Description

Added default variant on creation

### Close Issues

Related to #667

### Personal

Co-Authors

Merged By Silju M C <<EMAIL>>

Approvers Approved-by: Chandra Putta <<EMAIL>>

## [2.58.0] - 2024-07-22

See merge request treez-inc/engineering/back-of-house/product-control-mfe!498

### Description

Bug fixes on product creation flow

### Fixed

-   Navigation issue fix on product creation
-   Issue fix on variant creation failure

### Close Issues

Related to #667

### Personal

Co-Authors

Merged By Silju M C <<EMAIL>>

Approvers Approved-by: Chandra Putta <<EMAIL>>

## [2.57.0] - 2024-07-19

See merge request treez-inc/engineering/back-of-house/product-control-mfe!495

### Description

Create product and update product flow updated.

### Close Issues

Related to #665

### Personal

Co-Authors

Merged By Silju M C <<EMAIL>>

Approvers Approved-by: Chandra Putta <<EMAIL>>

## [2.56.0] - 2024-07-17

See merge request treez-inc/engineering/back-of-house/product-control-mfe!493

### Description

Image delete issue fix

### Close Issues

Related to #665

### Personal

Co-Authors

Merged By Silju M C <<EMAIL>>

Approvers Approved-by: Chandra Putta <<EMAIL>>

## [2.55.2] - 2024-06-27

See merge request treez-inc/engineering/back-of-house/product-control-mfe!489

### Description

Fix pagination on pricing page when there are no search results.

### Fixed

-   Fixed pagination
-   Shows `No products to display.` message as well.

### Close Issues

https://gitlab.com/treez-inc/engineering/back-of-house/product-control-mfe/-/issues/655

### Personal

Co-Authors

Merged By Chandra Putta <<EMAIL>>

Approvers Approved-by: Silju M C <<EMAIL>>

## [2.55.1] - 2024-06-27

See merge request treez-inc/engineering/back-of-house/product-control-mfe!487

### Description

Updates pricing page links by passing the product name to the `search` query parameter and view that specific product's
price details.

### Changed

-   Pricing page links

### Close Issues

https://gitlab.com/treez-inc/engineering/back-of-house/product-control-mfe/-/issues/652

### Personal

Co-Authors

Merged By Chandra Putta <<EMAIL>>

Approvers Approved-by: Silju M C <<EMAIL>> Approved-by: Andrew Bishop <<EMAIL>>

## [2.55.0] - 2024-06-27

See merge request treez-inc/engineering/back-of-house/product-control-mfe!488

### Description

The reducer in the filter component was causing multiple renders of filter component. I've removed the reducer and set
the url query params as the main source for filters state. This change reduced the rendering of filter component a lot.

### Changed

-   Updated state management in filter component
-   Moved debounce on search term to the filter component
-   Removed reducer and its state in the filter component
-   Cleaned up types

### Close Issues

https://gitlab.com/treez-inc/engineering/back-of-house/product-control-mfe/-/issues/653

### Personal

Co-Authors

Merged By Chandra Putta <<EMAIL>>

Approvers Approved-by: Andrew Bishop <<EMAIL>> Approved-by: Silju M C <<EMAIL>>

## [2.54.0] - 2024-06-24

See merge request treez-inc/engineering/back-of-house/product-control-mfe!481

### Description

This PR condenses:

https://gitlab.com/treez-inc/engineering/back-of-house/product-management-service/-/issues/898

https://gitlab.com/treez-inc/engineering/back-of-house/product-control-mfe/-/issues/639

https://gitlab.com/treez-inc/engineering/back-of-house/product-control-mfe/-/issues/623

https://gitlab.com/treez-inc/engineering/back-of-house/product-control-mfe/-/issues/604

https://gitlab.com/treez-inc/engineering/back-of-house/product-control-mfe/-/issues/601

https://gitlab.com/treez-inc/engineering/back-of-house/product-control-mfe/-/issues/635

### Personal

Co-Authors Co-authored-by: Lucas Giraldelli <<EMAIL>> Co-authored-by: Andrew Bishop
<<EMAIL>> Co-authored-by: group_56479611_bot_22a8162cb68dea57eaf1a95b220d3235
<<EMAIL>>

Merged By Chandra Putta <<EMAIL>>

Approvers Approved-by: Andrew Bishop <<EMAIL>> Approved-by: Silju M C <<EMAIL>>

## [2.53.1] - 2024-06-21

See merge request treez-inc/engineering/back-of-house/product-control-mfe!486

### Description

Edit product script fixes

Executed in sandbox environment

### Personal

Co-Authors

Merged By subha ks <<EMAIL>>

Approvers Approved-by: Chandra Putta <<EMAIL>>

## [2.53.0] - 2024-06-06

See merge request treez-inc/engineering/back-of-house/product-control-mfe!483

### Description

Product Image new UI changes, fixes to Product image & variant image scripts CreateProductImage, Editproductimage & edit
attribute scripts fixed

### Personal

Co-Authors

Merged By subha ks <<EMAIL>>

Approvers Approved-by: Silju M C <<EMAIL>>

## [2.52.1] - 2024-06-06

See merge request treez-inc/engineering/back-of-house/product-control-mfe!484

### Description

Moved urls to constants file.

### Changed

-   Moved urls to constants file

### Close Issues

### Personal

Co-Authors Co-authored-by: Chandra Putta <<EMAIL>>

Merged By Silju M C <<EMAIL>>

Approvers Approved-by: Silju M C <<EMAIL>>

## [2.52.0] - 2024-06-05

See merge request treez-inc/engineering/back-of-house/product-control-mfe!482

### Description

New Product Control MFE Attribute section code changes

Test in sandbox & Build

### Personal

Co-Authors

Merged By subha ks <<EMAIL>>

Approvers Approved-by: Chandra Putta <<EMAIL>>

## [2.51.0] - 2024-05-29

See merge request treez-inc/engineering/back-of-house/product-control-mfe!480

### Description

Shows active and inactive variants under a Product in Product Control grid

### Close Issues

Related to #628

### Personal

Co-Authors

Merged By Chandra Putta <<EMAIL>>

Approvers Approved-by: Lucas Giraldelli <<EMAIL>> Approved-by: Silju M C <<EMAIL>>

## [2.50.0] - 2024-05-22

See merge request treez-inc/engineering/back-of-house/product-control-mfe!475

### Description

Update price feature on new Pricing page

### Added

-   Update price for a single store
-   Update price for all stores at once (bulk update)
-   Custom components for renderCell and renderEditCell
-   Appropriate loading indicators

### Fixed

-   Pagination issue
-   Variants are sorted by size
-   Styles

### Related Issues

#624

### Personal

Co-Authors

Merged By Chandra Putta <<EMAIL>>

Approvers Approved-by: Silju M C <<EMAIL>> Approved-by: Lucas Giraldelli <<EMAIL>>

## [2.49.2] - 2024-05-21

See merge request treez-inc/engineering/back-of-house/product-control-mfe!478

### Description

variant UI test cases and size duplicate test case

Test in sandbox & build env

### Personal

Co-Authors

Merged By subha ks <<EMAIL>>

Approvers Approved-by: Chandra Putta <<EMAIL>> Approved-by: Silju M C <<EMAIL>>

## [2.49.1] - 2024-05-21

See merge request treez-inc/engineering/back-of-house/product-control-mfe!457

### Description

Update CI/CD for major GitLab update

### Changed

-   Update after_script on jobs to check if it is not on canceled status

### Personal

Co-Authors

Merged By Samuel Lima <<EMAIL>>

Approvers Approved-by: Silju M C <<EMAIL>>

## [2.49.0] - 2024-05-17

See merge request treez-inc/engineering/back-of-house/product-control-mfe!477

### Description

Save button enabled on each tab for product creation flow

### Close Issues

Related to #542

### Personal

Co-Authors

Merged By Silju M C <<EMAIL>>

Approvers Approved-by: Chandra Putta <<EMAIL>>

## [2.48.1] - 2024-05-16

See merge request treez-inc/engineering/back-of-house/product-control-mfe!476

### Description

Product attribute post data updated

### Personal

Co-Authors

Merged By Silju M C <<EMAIL>>

Approvers Approved-by: Chandra Putta <<EMAIL>>

## [2.48.0] - 2024-05-15

See merge request treez-inc/engineering/back-of-house/product-control-mfe!474

### Description

Fix added to create variant for merch product

### Personal

Co-Authors

Merged By Silju M C <<EMAIL>>

Approvers Approved-by: Chandra Putta <<EMAIL>>

## [2.47.0] - 2024-05-15

See merge request treez-inc/engineering/back-of-house/product-control-mfe!466

### Description

Added images to variant step

### Close Issues

Related to #621

### Personal

Co-Authors Co-authored-by: Chandra Putta <<EMAIL>>

Merged By Silju M C <<EMAIL>>

Approvers Approved-by: Chandra Putta <<EMAIL>>

## [2.46.1] - 2024-05-13

See merge request treez-inc/engineering/back-of-house/product-control-mfe!472

### Description

Size validation error fix

### Close Issues

Related to #632

### Personal

Co-Authors

Merged By Silju M C <<EMAIL>>

Approvers Approved-by: Chandra Putta <<EMAIL>>

## [2.46.0] - 2024-05-13

See merge request treez-inc/engineering/back-of-house/product-control-mfe!468

### Description

Added product attributes under product tab

### Close Issues

Related to #618

### Personal

Co-Authors

Merged By Silju M C <<EMAIL>>

Approvers Approved-by: Chandra Putta <<EMAIL>>

## [2.45.1] - 2024-05-09

See merge request treez-inc/engineering/back-of-house/product-control-mfe!471

### Description

Variant creation issue fix

### Close Issues

Related to #631

### Personal

Co-Authors

Merged By Silju M C <<EMAIL>>

Approvers Approved-by: Andrew Bishop <<EMAIL>>

## [2.45.0] - 2024-05-08

See merge request treez-inc/engineering/back-of-house/product-control-mfe!465

### Description

Adds new "Create New" option when assigning product variants to a collection

### Added

-   New Flow for adding new collection while instead of assigning to existing

### Close Issues

https://gitlab.com/treez-inc/engineering/front-of-house/product-collections-micro-front-end/-/issues/63

### Personal

Co-Authors

Merged By Jeremy Culler <<EMAIL>>

Approvers Approved-by: Andrew Bishop <<EMAIL>>

## [2.44.1] - 2024-05-08

See merge request treez-inc/engineering/back-of-house/product-control-mfe!469

### Description

Removed productIds and duplicated variantIds while saving to collection

### Closed Issues

[#69](https://gitlab.com/treez-inc/engineering/front-of-house/product-collections-micro-front-end/-/issues/69)

### Fixed

Removed productIds and duplicated variantIds while saving to collection

## MR Requirements

-   [x] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
-   [x] You added a demo video or a screenshot for any visual changes to a comment on the MR
-   [x] Your changes meet all of the Acceptance Criteria listed in the user story
-   [x] You reviewed your own MR and left comments where needed to provide context for reviewers
-   [x] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type
        can be narrowed down

### Personal

Co-Authors Co-authored-by: pranoysp <<EMAIL>>

Merged By Andrew Bishop <<EMAIL>>

Approvers Approved-by: Silju M C <<EMAIL>>

## [2.44.0] - 2024-05-08

See merge request treez-inc/engineering/back-of-house/product-control-mfe!467

### Description

Added pagination to products list in new pricing page.

### Added

-   Pagination for products list
-   Added scrolling for products list
-   Added linear progress for products list
-   Updated styles

### Related Issues

#624

### Personal

Co-Authors

Merged By Chandra Putta <<EMAIL>>

Approvers Approved-by: Andrew Bishop <<EMAIL>>

## [2.43.1] - 2024-05-06

See merge request treez-inc/engineering/back-of-house/product-control-mfe!458

### Description

Pricing base price value test cases for Negative & Invalid value Test in sandbox & Build

### Personal

Co-Authors

Merged By subha ks <<EMAIL>>

Approvers Approved-by: Silju M C <<EMAIL>> Approved-by: Chandra Putta <<EMAIL>>

## [2.43.0] - 2024-05-06

See merge request treez-inc/engineering/back-of-house/product-control-mfe!464

### Description

Adds variants data grid on new pricing page

### Added

-   Variants data grid
-   Variant tabs

### Changed

-   Added product category icon to product card
-   Updated variant display name in variant tabs

### Related Issues

#624

### Personal

Co-Authors

Merged By Chandra Putta <<EMAIL>>

Approvers Approved-by: Silju M C <<EMAIL>>

## [2.42.0] - 2024-05-05

See merge request treez-inc/engineering/back-of-house/product-control-mfe!463

### Description

Product Edit Flow - Tab selection for sizes

### Close Issues

Related to #620

### Personal

Co-Authors

Merged By Silju M C <<EMAIL>>

Approvers Approved-by: Chandra Putta <<EMAIL>>

## [2.41.0] - 2024-04-30

See merge request treez-inc/engineering/back-of-house/product-control-mfe!462

### Description

Adds product list component on new pricing page

### Added

-   New product list component

### Related Issues

#624

### Personal

Co-Authors

Merged By Chandra Putta <<EMAIL>>

Approvers Approved-by: Silju M C <<EMAIL>>

## [2.40.2] - 2024-04-26

See merge request treez-inc/engineering/back-of-house/product-control-mfe!460

### Description

Product Edit Update

-   Rename variants tab to sizes
-   Updated information text under variants tab
-   Updated product details popup
-   ![image](/uploads/a7d74a9cac37a0bcbc6214c2cc0fa769/image.png)

### Close Issues

Related to #619

### Personal

Co-Authors

Merged By Silju M C <<EMAIL>>

Approvers Approved-by: Chandra Putta <<EMAIL>>

## [2.40.1] - 2024-04-25

See merge request treez-inc/engineering/back-of-house/product-control-mfe!461

### Description

Hot fix

-   Formatting sku in product grid
-   Removed save button from edit product screen

### Personal

Co-Authors

Merged By Silju M C <<EMAIL>>

Approvers Approved-by: Chandra Putta <<EMAIL>>

## [2.40.0] - 2024-04-19

See merge request treez-inc/engineering/back-of-house/product-control-mfe!456

### Description

When selecting products and 'Add to Product Collection' using the search to look up the product collection doesn't
return the results as expected. More collection results are displayed in the list that does not match the search value
at all, even when there's one exact match.

This is caused because of an issue on duplicated values for the provided options list.

### Closed Issues

https://gitlab.com/treez-inc/engineering/front-of-house/product-collections-micro-front-end/-/issues/65

### Added

-   `renderOption` prop for rendering the displayName and key value as unique

## MR Requirements

-   [x] If you made visual changes, you have completed the design review process and your changes have been approved
-   [x] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
-   [x] You added a demo video or a screenshot for any visual changes to a comment on the MR
-   [x] Your changes meet all of the Acceptance Criteria listed in the user story
-   [x] You reviewed your own MR and left comments where needed to provide context for reviewers
-   [ ] ~~You added unit tests to cover your changes~~
-   [x] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type
        can be narrowed down

### Personal

Co-Authors

Merged By Kelvin Chinchilla <<EMAIL>>

Approvers Approved-by: Jess D <<EMAIL>>

## [2.39.6] - 2024-04-19

See merge request treez-inc/engineering/back-of-house/product-control-mfe!455

### Description

BUG: In BUILD ENV - Price By Product table columns does not ORDER BY the increasing Number of Packs

### Closed Issues

#584

### Fixed

Added ASC sorting based on size in price by product table

## MR Requirements

-   [x] If you made visual changes, you have completed the design review process and your changes have been approved
-   [x] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
-   [x] You added a demo video or a screenshot for any visual changes to a comment on the MR
-   [x] Your changes meet all of the Acceptance Criteria listed in the user story
-   [x] You reviewed your own MR and left comments where needed to provide context for reviewers
-   [x] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type
        can be narrowed down

### Personal

Co-Authors

Merged By Pranoy Sebastian <<EMAIL>>

Approvers Approved-by: Andrew Bishop <<EMAIL>> Approved-by: Chandra Putta <<EMAIL>>

## [2.39.5] - 2024-04-17

See merge request treez-inc/engineering/back-of-house/product-control-mfe!454

### Description

BUG: In BUILD ENV - Price By Product table columns does not ORDER BY the increasing Number of Packs

### Closed Issues

#584

### Fixed

Added sorting for number of packs in price table

## MR Requirements

-   [x] If you made visual changes, you have completed the design review process and your changes have been approved
-   [x] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
-   [x] You added a demo video or a screenshot for any visual changes to a comment on the MR
-   [x] Your changes meet all of the Acceptance Criteria listed in the user story
-   [x] You reviewed your own MR and left comments where needed to provide context for reviewers
-   [x] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type
        can be narrowed down

### Personal

Co-Authors

Merged By Pranoy Sebastian <<EMAIL>>

Approvers Approved-by: Chandra Putta <<EMAIL>> Approved-by: Andrew Bishop <<EMAIL>>

## [2.39.4] - 2024-04-16

See merge request treez-inc/engineering/back-of-house/product-control-mfe!449

### Description

This MR add a additional save button to product create/update flow.

### Added

-   updated with additional save button.

### Close Issues

Related to #542

### Personal

Co-Authors Co-authored-by: Silju M.C <<EMAIL>> Co-authored-by: rahul.reddy3 <<EMAIL>>

Merged By Chandra Putta <<EMAIL>>

Approvers Approved-by: Chandra Putta <<EMAIL>> Approved-by: Leo Belanger <<EMAIL>>

## [2.39.3] - 2024-04-16

See merge request treez-inc/engineering/back-of-house/product-control-mfe!453

### Description

Create Merch product E2E test for Product Control MFE Test in sandbox & build env

### Personal

Co-Authors Co-authored-by: subha <<EMAIL>>

Merged By Chandra Putta <<EMAIL>>

Approvers Approved-by: Andrew Bishop <<EMAIL>> Approved-by: Chandra Putta <<EMAIL>>

## [2.39.2] - 2024-04-09

See merge request treez-inc/engineering/back-of-house/product-control-mfe!452

### Description

-   Fixed the cell conditional checks of DataGridPro component on Pricing page, to not accept non-numeric or negative
    numbers.
-   Fixed the infinite looping of loading indicator when 0 is entered in a data cell.
-   Shows loading indicator only for the concerned cell and not for all data cells in a column.

### Fixed

-   Data cell conditional checks to not accept non-numeric or negative numbers
-   Infinite looping of loading indicator when 0 is entered in a data cell
-   Shows loading indicator only for the concerned data cell that is edited

### Related Issues

#576 #577

### Personal

Co-Authors

Merged By Chandra Putta <<EMAIL>>

Approvers Approved-by: Andrew Bishop <<EMAIL>> Approved-by: Silju M C <<EMAIL>>

## [2.39.1] - 2024-04-04

See merge request treez-inc/engineering/back-of-house/product-control-mfe!451

### Description

Fixes edit pricing button in product details drawer.

### Fixed

-   Fixes edit pricing button in product details drawer.

### Close Issues

Related to #571

### Personal

Co-Authors

Merged By Chandra Putta <<EMAIL>>

Approvers Approved-by: Andrew Bishop <<EMAIL>> Approved-by: Silju M C <<EMAIL>>

## [2.39.0] - 2024-03-19

See merge request treez-inc/engineering/back-of-house/product-control-mfe!448

### Description

Added status toggle button to change product status

### Close Issues

Related to #476

### Personal

Co-Authors

Merged By Silju M C <<EMAIL>>

Approvers Approved-by: Andrew Bishop <<EMAIL>>

## [2.38.1] - 2024-03-15

See merge request treez-inc/engineering/back-of-house/product-control-mfe!444

### Description

deactivate activate products with variants

### Personal

Co-Authors

Merged By subha ks <<EMAIL>>

Approvers Approved-by: Christopher Thomas <<EMAIL>> Approved-by: Keerthana Marappan
<<EMAIL>> Approved-by: subha ks <<EMAIL>>

## [2.38.0] - 2024-03-15

See merge request treez-inc/engineering/back-of-house/product-control-mfe!447

### Description

Updated variant display logic to fix sample and promo variants

### Close Issues

Related to #565

### Personal

Co-Authors

Merged By Silju M C <<EMAIL>>

Approvers Approved-by: Akshay Kumar Dhawle <<EMAIL>>

## [2.37.0] - 2024-03-14

See merge request treez-inc/engineering/back-of-house/product-control-mfe!445

### Description

Adds a new SKU column to the product control data grid component

### Added

-   SKU column + sorting

### Close Issues

Related to #143

### Personal

Co-Authors

Merged By Leo Belanger <<EMAIL>>

Approvers Approved-by: Christopher Thomas <<EMAIL>> Approved-by: Andrew Bishop
<<EMAIL>> Approved-by: Rahul Reddy <<EMAIL>> Approved-by: Akshay Kumar Dhawle
<<EMAIL>>

## [2.36.1] - 2024-03-12

See merge request treez-inc/engineering/back-of-house/product-control-mfe!443

### Description

The duplicate size validation in variants form is currently checking only the size property and doesn't check size label
value. This MR fixes that issue, and checks for both size and size label values while validating duplicate variant sizes
during addition of new variant in the form.

### Fixed

-   Fixes duplicate variant size validation logic in Variants form.

### Close Issues

Related to #586

### Personal

Co-Authors

Merged By Chandra Putta <<EMAIL>>

Approvers Approved-by: Andrew Bishop <<EMAIL>> Approved-by: Silju M C <<EMAIL>>

## [2.36.0] - 2024-03-07

See merge request treez-inc/engineering/back-of-house/product-control-mfe!441

### Description

commented size validation due to its not allowing changes in variant if there is bad data coming through import.

### Changed

-   commented size validation.

### Close Issues

### Personal

Co-Authors

Merged By Akshay Kumar Dhawle <<EMAIL>>

Approvers Approved-by: Silju M C <<EMAIL>>

## [2.35.0] - 2024-03-06

See merge request treez-inc/engineering/back-of-house/product-control-mfe!440

### Description

currently for merchandise size duplicate it's not allowing the add new merchandise size fix merchandise size issue.

### Fixed

-   fix merchandise size issue.

### Security

-   in case of vulnerabilities.

### Close Issues

### Personal

Co-Authors

Merged By Akshay Kumar Dhawle <<EMAIL>>

Approvers Approved-by: Silju M C <<EMAIL>>

## [2.34.0] - 2024-03-06

See merge request treez-inc/engineering/back-of-house/product-control-mfe!435

### Description

Updates the Variant Form to allow users to change the \`Size Label\` value for variants that fall into Product
Categories that contain "Pack" values. For such variants, they will now be able to select between their parent product's
UoM and "Pack".

### Added

-   HookFormSelect dropdown to the SizeLabel section of the variant form

### Changed

-   Update the HookFormSelect to accept an onChange callback to allow the SizeLabel to update the parent SizeLabel in
    the form dynamically

### Close Issues

Related to #563

### Personal

Co-Authors Co-authored-by: Leo Belanger <<EMAIL>>

Merged By Silju M C <<EMAIL>>

Approvers Approved-by: Chandra Putta <<EMAIL>> Approved-by: Silju M C <<EMAIL>>

## [2.33.0] - 2024-03-06

See merge request treez-inc/engineering/back-of-house/product-control-mfe!439

### Description

### Closed Issues

https://gitlab.com/treez-inc/engineering/front-of-house/product-collections-micro-front-end/-/issues/54

### Changed

Changed dropdown in Add collection modal with search.

## MR Requirements

-   [x] If you made visual changes, you have completed the design review process and your changes have been approved
-   [x] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
-   [x] You added a demo video or a screenshot for any visual changes to a comment on the MR
-   [x] Your changes meet all of the Acceptance Criteria listed in the user story
-   [x] You reviewed your own MR and left comments where needed to provide context for reviewers
-   [ ] You added unit tests to cover your changes
-   [x] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type
        can be narrowed down

### Personal

Co-Authors

Merged By Ankita Jain <<EMAIL>>

Approvers Approved-by: Akshay Kumar Dhawle <<EMAIL>> Approved-by: Chandra Putta
<<EMAIL>>

## [2.32.3] - 2024-03-05

See merge request treez-inc/engineering/back-of-house/product-control-mfe!438

### Description

This MR address renaming Base Price on Product Grid to Price and restore the merge button to its disabled state after
the first successful merge.

### Changed

-   Renamed Base Price to Price on Product-Control Grid

### Fixed

-   Fixed merge button to be in disabled state after finishing successful merging as @KeerthanaMarappan suggested below.

```
@rahul.reddy3 While merging for the first time, merge button is disabled before selecting the primary product.

Once the merge process is done for the first time, without reloading the page we are trying merge other 2 different products.

Here merge button is enabled before selecting the primary product in Merge Product screen.

**Note: Once the page is reloaded and tried to merge the products again, merge button is disabled.**
```

[Screen_Recording_2024-03-01_at_2.22.48_PM](https://gitlab.com/treez-inc/engineering/back-of-house/product-control-mfe/uploads/325169cd9fd0d6e3af066b7655a89f90/Screen_Recording_2024-03-01_at_2.22.48_PM.mov)

### Close Issues

Related to #574

### Personal

Co-Authors Co-authored-by: rahul.reddy3 <<EMAIL>>

Merged By Chandra Putta <<EMAIL>>

Approvers Approved-by: Christopher Thomas <<EMAIL>> Approved-by: Chandra Putta
<<EMAIL>>

## [2.32.2] - 2024-03-05

See merge request treez-inc/engineering/back-of-house/product-control-mfe!432

### Description

Added validation for base price in product control MFE

### Added

-   Added test cases for base price in product control home page.

### Changed

-   Updated constant file with element values.

### Removed

-   Removed validation for deactivated variants list in product control homepage.

### Close Issues

Related to #581

### Personal

Co-Authors

Merged By Keerthana Marappan <<EMAIL>>

Approvers Approved-by: subha ks <<EMAIL>> Approved-by: Rahul Reddy <<EMAIL>>

## [2.32.1] - 2024-03-05

See merge request treez-inc/engineering/back-of-house/product-control-mfe!437

### Description

Disables activate button in variant context menu of Product Control grid if its product is inactive.

### Changed

-   Disables activate button in variant context menu if product is inactive

### Close Issues

Related to #477

### Personal

Co-Authors

Merged By Chandra Putta <<EMAIL>>

Approvers Approved-by: Rahul Reddy <<EMAIL>> Approved-by: Silju M C <<EMAIL>>

## [2.32.0] - 2024-03-04

See merge request treez-inc/engineering/back-of-house/product-control-mfe!434

### Description

Size editable test cases for CYPRESS Ticket #558 Execute in sandbox Build ENV

### Personal

Co-Authors

Merged By subha ks <<EMAIL>>

Approvers Approved-by: Keerthana Marappan <<EMAIL>> Approved-by: Chandra Putta <<EMAIL>>

## [2.31.3] - 2024-03-04

See merge request treez-inc/engineering/back-of-house/product-control-mfe!429

### Description

Fixes the entity price page when variants have sample or promo variants.

### Added

-   Logic for parsing the variants for display in the entity price grid, so that samples and promos display differently
    from their parent variant.

### Fixed

-   Entity price page for display of Sample and Promo variants

### Changed

-   Added tooltip for explaining pricing on price by product page
-   Changed name of input field from `BASE` to `PRICE`
-   Updated treez component library to latest version

### Close Issues

Related to #579

### Personal

Co-Authors Co-authored-by: Andrew Bishop <<EMAIL>>

Merged By Chandra Putta <<EMAIL>>

Approvers Approved-by: Chandra Putta <<EMAIL>> Approved-by: Akshay Kumar Dhawle
<<EMAIL>>

## [2.31.2] - 2024-03-01

See merge request treez-inc/engineering/back-of-house/product-control-mfe!436

### Description

### Closed Issues #583

### Added

### Changed

-   List only manual collections when adding products to a collection

### Fixed

### Deleted

## MR Requirements

-   [ ] ~~If you made visual changes, you have completed the design review process and your changes have been approved~~
-   [x] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
-   [ ] ~~You added a demo video or a screenshot for any visual changes to a comment on the MR~~
-   [x] Your changes meet all of the Acceptance Criteria listed in the user story
-   [x] You reviewed your own MR and left comments where needed to provide context for reviewers
-   [ ] ~~You added unit tests to cover your changes~~
-   [x] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type
        can be narrowed down

### Personal

Co-Authors

Merged By Jishnu Mohan <<EMAIL>>

Approvers Approved-by: Silju M C <<EMAIL>>

## [2.31.1] - 2024-02-29

See merge request treez-inc/engineering/back-of-house/product-control-mfe!431

### Description

This MR addresses the bug where the confirmation modal remained open after the first successful merge attempt, leading
to a validation error upon subsequent attempts. Further details are provided in the following Slack thread.

https://treezio.slack.com/archives/C03TSC89XPT/p1708067445766969

![image](/uploads/5d1c9bcefea0e4e64f2be8ddfeb22f2d/image.png)

### Fixed

-   Resolved the issue causing the confirmation modal to remain open after a successful merge, ensuring it closes
    appropriately thereafter.

### Close Issues #582

### Personal

Co-Authors

Merged By Rahul Reddy <<EMAIL>>

Approvers Approved-by: Akshay Kumar Dhawle <<EMAIL>> Approved-by: Chandra Putta
<<EMAIL>>

## [2.31.0] - 2024-02-29

See merge request treez-inc/engineering/back-of-house/product-control-mfe!433

### Description

<!-- Add high-level description here -->

### Closed Issues

-   https://gitlab.com/treez-inc/engineering/front-of-house/product-collections-micro-front-end/-/issues/51

### Changed

Updated condition for Add to collection bulk action condition

## MR Requirements

-   [x] If you made visual changes, you have completed the design review process and your changes have been approved
-   [x] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
-   [x] You added a demo video or a screenshot for any visual changes to a comment on the MR
-   [x] Your changes meet all of the Acceptance Criteria listed in the user story
-   [x] You reviewed your own MR and left comments where needed to provide context for reviewers
-   [ ] You added unit tests to cover your changes
-   [x] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type
        can be narrowed down

### Personal

Co-Authors

Merged By Ankita Jain <<EMAIL>>

Approvers Approved-by: Akshay Kumar Dhawle <<EMAIL>>

## [2.30.3] - 2024-02-27

See merge request treez-inc/engineering/back-of-house/product-control-mfe!422

### Description

Added test validation for sample and promo labels under size/amount column.

### Added

-   Added test validation for sample and promo labels under size/amount column.
-   While running as a whole automation suite random test cases are failing. So, added clearCookies function in all the
    tests.
-   Updated variant test cases to remove validation on deactivated variants in product control homepage.

### Changed

-   Refactored few values based on updates in the page.

### Personal

Co-Authors

Merged By Keerthana Marappan <<EMAIL>>

Approvers Approved-by: subha ks <<EMAIL>> Approved-by: Christopher Thomas <<EMAIL>>

## [2.30.2] - 2024-02-26

See merge request treez-inc/engineering/back-of-house/product-control-mfe!428

### Description

duplicate variant and multiple SKUs Cypress test cases Execute in Sandbox & build ENV.

### Personal

Co-Authors

Merged By subha ks <<EMAIL>>

Approvers Approved-by: Akshay Kumar Dhawle <<EMAIL>> Approved-by: Keerthana Marappan
<<EMAIL>>

## [2.30.1] - 2024-02-21

See merge request treez-inc/engineering/back-of-house/product-control-mfe!423

### Description

### Closed Issues

[42](https://gitlab.com/treez-inc/engineering/front-of-house/product-collections-micro-front-end/-/issues/42 'Product collection - Arrange Product collection listing in Alphabetical order')

### Changed

-   Changed product collection order to alphabetical

## MR Requirements

-   [x] If you made visual changes, you have completed the design review process and your changes have been approved
-   [x] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
-   [x] You added a demo video or a screenshot for any visual changes to a comment on the MR
-   [x] Your changes meet all of the Acceptance Criteria listed in the user story
-   [x] You reviewed your own MR and left comments where needed to provide context for reviewers
-   [ ] ~~You added unit tests to cover your changes~~
-   [x] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type
        can be narrowed down

### Personal

Co-Authors

Merged By Jishnu Mohan <<EMAIL>>

Approvers Approved-by: Chandra Putta <<EMAIL>>

## [2.30.0] - 2024-02-20

See merge request treez-inc/engineering/back-of-house/product-control-mfe!426

### Description

Hide Deactivated variants from Control Grid

### Added

-   showing activate variants for active products
-   showing inactive variants for inactive products

### Close Issues

Related to #559

### Personal

Co-Authors Co-authored-by: Akshaykumar Dhawle <<EMAIL>>

Merged By Chandra Putta <<EMAIL>>

Approvers Approved-by: Chandra Putta <<EMAIL>>

## [2.29.1] - 2024-02-20

See merge request treez-inc/engineering/back-of-house/product-control-mfe!425

### Description

Fixes an issue to show newly added options right away in MultiSelectSearch component.

### Fixed

-   Fixes issue in MultiSelectSearch component.

### Close Issues

Related to #568

### Personal

Co-Authors

Merged By Chandra Putta <<EMAIL>>

Approvers Approved-by: Akshay Kumar Dhawle <<EMAIL>>

## [2.29.0] - 2024-02-20

See merge request treez-inc/engineering/back-of-house/product-control-mfe!427

### Description

removed special character validation for sku and additionalSku

### Removed

-   removed special character validation for sku and additionalSku

### Security

-   in case of vulnerabilities.

### Close Issues

### Personal

Co-Authors Co-authored-by: Akshaykumar Dhawle <<EMAIL>>

Merged By Chandra Putta <<EMAIL>>

Approvers Approved-by: Chandra Putta <<EMAIL>>

## [2.28.1] - 2024-02-20

See merge request treez-inc/engineering/back-of-house/product-control-mfe!424

### Description

Search using SKU number and Updates saved searches in Product Control page

Execute in sandbox and build

### Personal

Co-Authors

Merged By subha ks <<EMAIL>>

Approvers Approved-by: Christopher Thomas <<EMAIL>> Approved-by: Keerthana Marappan
<<EMAIL>>

## [2.28.0] - 2024-02-20

See merge request treez-inc/engineering/back-of-house/product-control-mfe!391

### Description

added base price column in product control grid for products and variants.

### Added

-   added base price column in product control grid for products and variants.
-   shown range price for products and base price for variants.

### Close Issues

Related to #499

### Personal

Co-Authors Co-authored-by: Akshaykumar Dhawle <<EMAIL>>

Merged By Chandra Putta <<EMAIL>>

Approvers Approved-by: Chandra Putta <<EMAIL>> Approved-by: Christopher Thomas
<<EMAIL>>

## [2.27.1] - 2024-02-13

See merge request treez-inc/engineering/back-of-house/product-control-mfe!419

### Description

Updated expiry time on persistence of search term to 10 min.

### Fixed

-   Update search expiry time to 10 min.

### Close Issues

### Personal

Co-Authors

Merged By Chandra Putta <<EMAIL>>

Approvers Approved-by: Akshay Kumar Dhawle <<EMAIL>>

## [2.27.0] - 2024-02-13

See merge request treez-inc/engineering/back-of-house/product-control-mfe!418

### Description

Update saved search in Product Control

### Changed

-   Update saved search in Product Control
-   replaced useLocalstorage with searchStorage context to save search term locally
-   added functionality to delete search term after 10 minutes if search term not changed.

### Close Issues

Related to #561

### Personal

Co-Authors

Merged By Akshay Kumar Dhawle <<EMAIL>>

Approvers Approved-by: Chandra Putta <<EMAIL>>

## [2.26.0] - 2024-02-13

See merge request treez-inc/engineering/back-of-house/product-control-mfe!413

### Description

Updated SelectWithSearch component (used by Brand field) and MultiSelectWithSearch component (used in Attributes
section) to allow creation of new brand and new attribute tags respectively, when they have similar spelling to other
options in the dropdown.

### Changed

-   Updated SelectWithSearch and MultiSelectWithSearch components.

### Close Issues

Related to #556

### Personal

Co-Authors

Merged By Chandra Putta <<EMAIL>>

Approvers Approved-by: Silju M C <<EMAIL>> Approved-by: Akshay Kumar Dhawle <<EMAIL>>

## [2.25.2] - 2024-02-13

See merge request treez-inc/engineering/back-of-house/product-control-mfe!417

### Description

Merge products and check if child variants are auto selected Merge products and check for activate deactivate status
filters

### Personal

Co-Authors

Merged By subha ks <<EMAIL>>

Approvers Approved-by: Keerthana Marappan <<EMAIL>> Approved-by: Akshay Kumar Dhawle
<<EMAIL>>

## [2.25.1] - 2024-02-12

See merge request treez-inc/engineering/back-of-house/product-control-mfe!406

### Description

This MR address adding variant.updatedAt field to be returned correctly if a user makes any change to variants tab.

### Fixed

-   Updated variant Last Updated field to return the variant modified at date.

### Close Issues

Related to #519

### Personal

Co-Authors

Merged By Rahul Reddy <<EMAIL>>

Approvers Approved-by: Chandra Putta <<EMAIL>> Approved-by: Akshay Kumar Dhawle
<<EMAIL>> Approved-by: Christopher Thomas <<EMAIL>>

## [2.25.0] - 2024-02-12

See merge request treez-inc/engineering/back-of-house/product-control-mfe!414

### Description

added XS size for merchandise size

### Added

-   added XS size for merchandise size

### Close Issues

### Personal

Co-Authors

Merged By Akshay Kumar Dhawle <<EMAIL>>

Approvers Approved-by: Chandra Putta <<EMAIL>>

## [2.24.1] - 2024-02-09

See merge request treez-inc/engineering/back-of-house/product-control-mfe!410

### Description

Cypress test cases for Bulk actions deactivate activate

To be tested in Sandbox and Build environment.

### Personal

Co-Authors

Merged By subha ks <<EMAIL>>

Approvers Approved-by: Chandra Putta <<EMAIL>> Approved-by: Keerthana Marappan <<EMAIL>>

## [2.24.0] - 2024-02-08

See merge request treez-inc/engineering/back-of-house/product-control-mfe!405

### Description

Shows sample or promo labels in Size/Amount column of Product control grid.

### Added

-   Shows sample or promo labels in Size/Amount column of Product control grid.

### Close Issues

Related to #527

### Personal

Co-Authors

Merged By Chandra Putta <<EMAIL>>

Approvers Approved-by: Akshay Kumar Dhawle <<EMAIL>> Approved-by: Rahul Reddy <<EMAIL>>

## [2.23.1] - 2024-02-08

See merge request treez-inc/engineering/back-of-house/product-control-mfe!412

### Description

Added test case validation for duplicate SKU.

### Added

-   Added test case validation for duplicate SKU.

### Changed

-   Updated edit product variant test case to validate duplicate SKU.

### Personal

Co-Authors

Merged By Keerthana Marappan <<EMAIL>>

Approvers Approved-by: Silju M C <<EMAIL>> Approved-by: subha ks <<EMAIL>>

## [2.23.0] - 2024-02-08

See merge request treez-inc/engineering/back-of-house/product-control-mfe!407

### Description

Allowed 0 in Total Flower Weight field for Prerolls

### Changed

-   Allowed 0 in Total Flower Weight field for Prerolls

### Close Issues

Related to #550

### Personal

Co-Authors

Merged By Akshay Kumar Dhawle <<EMAIL>>

Approvers Approved-by: Andrew Bishop <<EMAIL>> Approved-by: Rahul Reddy <<EMAIL>>
Approved-by: Leo Belanger <<EMAIL>>

## [2.22.1] - 2024-02-07

See merge request treez-inc/engineering/back-of-house/product-control-mfe!409

### Description

Added test cases in product control cypress test case

### Added

-   Added new test case in product control cypress test case.

### Changed

-   Updated element values in constant files.

Related to #553

### Personal

Co-Authors

Merged By Keerthana Marappan <<EMAIL>>

Approvers Approved-by: subha ks <<EMAIL>> Approved-by: Andrew Bishop <<EMAIL>>

## [2.22.0] - 2024-02-06

See merge request treez-inc/engineering/back-of-house/product-control-mfe!404

### Description

change the size of product name on product control grid to 1.2 flex

### Changed

-   change the size of product name on product control grid to 1.2 flex

### Close Issues

Related to #557

### Personal

Co-Authors

Merged By Akshay Kumar Dhawle <<EMAIL>>

Approvers Approved-by: Chandra Putta <<EMAIL>> Approved-by: Silju M C <<EMAIL>> Approved-by:
Andrew Bishop <<EMAIL>>

## [2.21.0] - 2024-02-06

See merge request treez-inc/engineering/back-of-house/product-control-mfe!403

### Description

fixed catagory filter issue.

### Changed

-   category filter conditions, added check that categories and subcategories should not be empty.

### Deprecated

-   for soon-to-be removed features.

### Fixed

-   fixed catagory filter issue.

### Close Issues

Related to #553

### Personal

Co-Authors

Merged By Akshay Kumar Dhawle <<EMAIL>>

Approvers Approved-by: Chandra Putta <<EMAIL>> Approved-by: Andrew Bishop <<EMAIL>>

## [2.20.2] - 2024-02-05

See merge request treez-inc/engineering/back-of-house/product-control-mfe!394

### Description

Added e2e test cases for merge product and refactored few tests.

### Added

-   Added e2e test cases for merge product.
-   Added new constant values.

### Changed

-   Updated few element values as it got updated.

### Close Issues

Related to #533

### Personal

Co-Authors

Merged By Keerthana Marappan <<EMAIL>>

Approvers Approved-by: Christopher Thomas <<EMAIL>> Approved-by: subha ks <<EMAIL>>

## [2.20.1] - 2024-02-02

See merge request treez-inc/engineering/back-of-house/product-control-mfe!402

### Description

Re-adds the change to allow sku removal on variants. Confirmed that ST is not using the Barcodes field in the Bridge,
which is currently removed when a sku is removed.

### Added

-   Sku Removal for Variants

### Close Issues

Related to #551

### Personal

Co-Authors

Merged By Andrew Bishop <<EMAIL>>

Approvers Approved-by: Chandra Putta <<EMAIL>> Approved-by: Rahul Reddy <<EMAIL>>

## [2.20.0] - 2024-02-02

See merge request treez-inc/engineering/back-of-house/product-control-mfe!401

### Description

Variant validation update

### Added

-   Added validation for duplicate variant

### Personal

Co-Authors Co-authored-by: Silju M.C <<EMAIL>>

Merged By Chandra Putta <<EMAIL>>

Approvers Approved-by: Chandra Putta <<EMAIL>> Approved-by: Akshay Kumar Dhawle
<<EMAIL>>

## [2.19.0] - 2024-02-02

See merge request treez-inc/engineering/back-of-house/product-control-mfe!399

### Description

added local storage to Leave search results in control screen.

### Added

-   added local storage to Leave search results in control screen.
-   created custom hook for local storage.

### Close Issues

Related to #554

### Personal

Co-Authors Co-authored-by: Akshaykumar Dhawle <<EMAIL>>

Merged By Silju M C <<EMAIL>>

Approvers Approved-by: Silju M C <<EMAIL>> Approved-by: Chandra Putta <<EMAIL>>

## [2.18.2] - 2024-02-02

See merge request treez-inc/engineering/back-of-house/product-control-mfe!400

### Description

Deactivate action bar confirmation message updated

### Fixed

-   Fixed issue in displaying merch variants

### Personal

Co-Authors

Merged By Silju M C <<EMAIL>>

Approvers Approved-by: Akshay Kumar Dhawle <<EMAIL>> Approved-by: Chandra Putta
<<EMAIL>>

## [2.18.1] - 2024-02-02

See merge request treez-inc/engineering/back-of-house/product-control-mfe!398

### Description

Undo sku edit changes related to
https://gitlab.com/treez-inc/engineering/back-of-house/product-control-mfe/-/merge_requests/396

### Changed

-   Undo sku edit changes related to
    https://gitlab.com/treez-inc/engineering/back-of-house/product-control-mfe/-/merge_requests/396

### Close Issues

### Personal

Co-Authors

Merged By Chandra Putta <<EMAIL>>

Approvers Approved-by: Silju M C <<EMAIL>> Approved-by: Akshay Kumar Dhawle <<EMAIL>>

## [2.18.0] - 2024-02-02

See merge request treez-inc/engineering/back-of-house/product-control-mfe!397

### Description

Allows making updates on existing duplicate variants in the form, but doesn't allow adding a new variant which is a
duplicate.

### Changed

-   Allows edits on duplicate variants that already exist in the form.
-   Doesn't allow addition of a new variant to the form, which has a duplicate size or merchandise size.

### Close Issues

Related to #555

### Personal

Co-Authors

Merged By Chandra Putta <<EMAIL>>

Approvers Approved-by: Akshay Kumar Dhawle <<EMAIL>> Approved-by: Silju M C <<EMAIL>>

## [2.17.2] - 2024-02-02

See merge request treez-inc/engineering/back-of-house/product-control-mfe!396

### Description

Allows users to remove the sku from a variant. Currently this is not possible in the UI.

### Fixed

-   Variant update when sku field is empty
-   Typo in error message

### Close Issues

Related to #551

### Personal

Co-Authors Co-authored-by: Andrew Bishop <<EMAIL>>

Merged By Silju M C <<EMAIL>>

Approvers Approved-by: Leo Belanger <<EMAIL>> Approved-by: Silju M C <<EMAIL>>

## [2.17.1] - 2024-02-01

See merge request treez-inc/engineering/back-of-house/product-control-mfe!393

### Description

Displayed only active product collections

### Closed Issues

[38](https://gitlab.com/treez-inc/engineering/front-of-house/product-collections-micro-front-end/-/issues/38)

### Fixed

Filtered active product collections

## MR Requirements

-   [x] If you made visual changes, you have completed the design review process and your changes have been approved
-   [x] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
-   [x] Your changes meet all of the Acceptance Criteria listed in the user story
-   [x] You reviewed your own MR and left comments where needed to provide context for reviewers
-   [x] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type
        can be narrowed down

### Personal

Co-Authors

Merged By Pranoy Sebastian <<EMAIL>>

Approvers Approved-by: Akshay Kumar Dhawle <<EMAIL>> Approved-by: Chandra Putta
<<EMAIL>> Approved-by: Leo Belanger <<EMAIL>>

## [2.17.0] - 2024-02-01

See merge request treez-inc/engineering/back-of-house/product-control-mfe!392

### Description

There is an issue with pagination component in DataGridPro of Treez Component Library. This is breaking search and
doesn't show all page numbers to navigate to different pages after filtering.

As a quick fix to this issue, DataGridPro is copied from Treez Component Library into Product Control MFE code base and
fixed the pagination component issue locally.

### Added

-   Copy of DataGridPro component locally

### Fixed

-   Fixed Pagination component issue locally

### Close Issues

Related to #546

### Personal

Co-Authors

Merged By Chandra Putta <<EMAIL>>

Approvers Approved-by: Akshay Kumar Dhawle <<EMAIL>> Approved-by: Rahul Reddy <<EMAIL>>
Approved-by: Silju M C <<EMAIL>>

## [2.16.0] - 2024-01-29

See merge request treez-inc/engineering/back-of-house/product-control-mfe!388

### Description

Cypress tests for Pricing tab Execute in sandbox and Build environment.

### Personal

Co-Authors

Merged By subha ks <<EMAIL>>

Approvers Approved-by: Chandra Putta <<EMAIL>> Approved-by: Keerthana Marappan <<EMAIL>>

## [2.15.2] - 2024-01-26

See merge request treez-inc/engineering/back-of-house/product-control-mfe!381

### Description

Currently When a parent product is selected, the associated child variants should be automatically checked to avoid
confusion for customers.

### Added

-   Updated variants to be auto-selected when its parent product is selected

Related to #536

### Personal

Co-Authors

Merged By Rahul Reddy <<EMAIL>>

Approvers Approved-by: Chandra Putta <<EMAIL>> Approved-by: Christopher Thomas
<<EMAIL>> Approved-by: Andrew Bishop <<EMAIL>> Approved-by: Leo Belanger
<<EMAIL>>

## [2.15.1] - 2024-01-25

See merge request treez-inc/engineering/back-of-house/product-control-mfe!390

### Description

This MR address the following issue

![image](/uploads/333c524c06c0d8bb37049bcb40613808/image.png)

### Fixed

-   Fix to delete additional sku

### Close Issues

### Personal

Co-Authors

Merged By Rahul Reddy <<EMAIL>>

Approvers Approved-by: Silju M C <<EMAIL>>

## [2.15.0] - 2024-01-25

See merge request treez-inc/engineering/back-of-house/product-control-mfe!389

### Description

API throwing error for some of the imported variant updates.

The issue is because of the validation implemented in the back-end service to make sure we are saving the same data all
the time. But we didn't implement the validation import side and hence some bad data generated. Fixed issues by
converting marchentiseSize to expected casing, while submitting patch request from UI

### Close Issues

Related to #541

### Personal

Co-Authors

Merged By Silju M C <<EMAIL>>

Approvers Approved-by: Chandra Putta <<EMAIL>>

## [2.14.5] - 2024-01-24

See merge request treez-inc/engineering/back-of-house/product-control-mfe!386

### Description

Unexpected product collections api call on page load

### Closed Issues

[#34](https://gitlab.com/treez-inc/engineering/front-of-house/product-collections-micro-front-end/-/issues/34)

### Fixed

Fixed permission checking for product collections api call

## MR Requirements

-   [ ] If you made visual changes, you have completed the design review process and your changes have been approved
-   [x] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
-   [ ] You added a demo video or a screenshot for any visual changes to a comment on the MR
-   [x] Your changes meet all of the Acceptance Criteria listed in the user story
-   [x] You reviewed your own MR and left comments where needed to provide context for reviewers
-   [x] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type
        can be narrowed down

### Personal

Co-Authors Co-authored-by: pranoysp <<EMAIL>>

Merged By Chandra Putta <<EMAIL>>

Approvers Approved-by: Akshay Kumar Dhawle <<EMAIL>> Approved-by: Chandra Putta
<<EMAIL>>

## [2.14.4] - 2024-01-24

See merge request treez-inc/engineering/back-of-house/product-control-mfe!387

### Description

Additional sku was copying to sample and promo, so this fix is to remove parent sku while creating sample and promo

### Fixed

-   Removed sku inheritance for sample and promo

### Personal

Co-Authors

Merged By Silju M C <<EMAIL>>

Approvers Approved-by: Akshay Kumar Dhawle <<EMAIL>> Approved-by: Chandra Putta
<<EMAIL>>

## [2.14.3] - 2024-01-24

See merge request treez-inc/engineering/back-of-house/product-control-mfe!385

### Description

This MR address the confirmation modal copy if a customer wants both products and variants to bulk deactivate/activate
and updated to show generic treez image if there is no image for a product

### Fixed

-   Updated confirmation modal copy if a customer wants both products and variants to bulk deactivate/activate and
    updated to show generic treez image if there is no image for a product.

-   Also updating Jest configuration is necessary to ensure proper handling of image imports during testing. It resolves
    issues related to the compatibility of Jest with the specified version of jest-transform-stub for mocking static
    assets. This ensures proper transformation and handling of image imports during testing.

### Close Issues #539

### Personal

Co-Authors

Merged By Rahul Reddy <<EMAIL>>

Approvers Approved-by: Christopher Thomas <<EMAIL>> Approved-by: Andrew Bishop
<<EMAIL>>

## [2.14.2] - 2024-01-23

See merge request treez-inc/engineering/back-of-house/product-control-mfe!384

### Description

Checks for empty value in source name field before converting it to lower case in Reference Id component.

### Fixed

-   Fixes logic to check for empty value before converting value to lower case.

### Security

-   in case of vulnerabilities.

### Close Issues

### Personal

Co-Authors

Merged By Chandra Putta <<EMAIL>>

Approvers Approved-by: Silju M C <<EMAIL>> Approved-by: Akshay Kumar Dhawle <<EMAIL>>

## [2.14.1] - 2024-01-22

See merge request treez-inc/engineering/back-of-house/product-control-mfe!383

### Description

This MR prevent the users from editing and deleting import reference IDs used for migration.

### Changed

-   Updated to be disable editing the ExtrenalId we use for migration. The rest should still be editable and visible.

### Close Issues

Related to #515

### Personal

Co-Authors

Merged By Rahul Reddy <<EMAIL>>

Approvers Approved-by: Christopher Thomas <<EMAIL>> Approved-by: Andrew Bishop
<<EMAIL>>

## [2.14.0] - 2024-01-22

See merge request treez-inc/engineering/back-of-house/product-control-mfe!382

### Description

<!-- Add high-level description here -->

### Closed Issues

https://gitlab.com/treez-inc/engineering/front-of-house/product-collections-micro-front-end/-/issues/32

<!-- Be sure to delete unused headers and comments -->

### Changed

Updated code to handle child variants selection

## MR Requirements

-   [ ] If you made visual changes, you have completed the design review process and your changes have been approved
-   [x] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
-   [ ] You added a demo video or a screenshot for any visual changes to a comment on the MR
-   [x] Your changes meet all of the Acceptance Criteria listed in the user story
-   [x] You reviewed your own MR and left comments where needed to provide context for reviewers
-   [ ] You added unit tests to cover your changes
-   [x] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type
        can be narrowed down

### Personal

Co-Authors

Merged By Ankita Jain <<EMAIL>>

Approvers Approved-by: Akshay Kumar Dhawle <<EMAIL>> Approved-by: Chandra Putta
<<EMAIL>>

## [2.13.1] - 2024-01-17

See merge request treez-inc/engineering/back-of-house/product-control-mfe!380

### Description

Fixes style issue in sample/promo section

### Fixed

-   Fixes style issue in sample/promo section

### Close Issues

Related to #534

### Personal

Co-Authors

Merged By Chandra Putta <<EMAIL>>

Approvers Approved-by: Akshay Kumar Dhawle <<EMAIL>> Approved-by: Silju M C <<EMAIL>>

## [2.13.0] - 2024-01-17

See merge request treez-inc/engineering/back-of-house/product-control-mfe!374

### Description

This MR addresses changes to enable bulk selection and action on both products and variants for deactivation and
activation.

### Added

-   Updated to allow bulk selection of variants for deactivation/activation.

### Close Issues

Related to #513

### Personal

Co-Authors

Merged By Rahul Reddy <<EMAIL>>

Approvers Approved-by: Leo Belanger <<EMAIL>> Approved-by: Chandra Putta <<EMAIL>>

## [2.12.0] - 2024-01-16

See merge request treez-inc/engineering/back-of-house/product-control-mfe!375

### Description

Add the selected product with various applied filters to be added to the product collection directly from the product
control screen.

### Closed Issues

[18](https://gitlab.com/treez-inc/engineering/front-of-house/product-collections-micro-front-end/-/issues/18)

<!-- Be sure to delete unused headers and comments -->

### Added

Add the selected products to product collection

## MR Requirements

-   [x] If you made visual changes, you have completed the design review process and your changes have been approved
-   [x] You have added the appropriate versioning tag (i.e. major, minor, or patch) to the MR title
-   [x] You added a demo video or a screenshot for any visual changes to a comment on the MR
-   [x] Your changes meet all of the Acceptance Criteria listed in the user story
-   [x] You reviewed your own MR and left comments where needed to provide context for reviewers
-   [x] You resolved all TypeScript errors, including the removal of @ts-ignore comments and `any` types where the type
        can be narrowed down

### Personal

Co-Authors

Merged By Pranoy Sebastian <<EMAIL>>

Approvers Approved-by: Akshay Kumar Dhawle <<EMAIL>> Approved-by: Chandra Putta
<<EMAIL>> Approved-by: Silju M C <<EMAIL>>

## [2.11.0] - 2024-01-16

See merge request treez-inc/engineering/back-of-house/product-control-mfe!376

### Description

Product creation flow updated to add additional skus

### Added

-   Variant tab updated to show new component - additional sku

### Close Issues

Related to #532

### Personal

Co-Authors Co-authored-by: Silju M.C <<EMAIL>>

Merged By Chandra Putta <<EMAIL>>

Approvers Approved-by: Christopher Thomas <<EMAIL>> Approved-by: Andrew Bishop
<<EMAIL>> Approved-by: Silju M C <<EMAIL>> Approved-by: Akshay Kumar Dhawle
<<EMAIL>>

## [2.10.0] - 2024-01-12

See merge request treez-inc/engineering/back-of-house/product-control-mfe!377

### Description

Global catalog validation

### Added

-   Disabled delete image button for global images

### Personal

Co-Authors

Merged By Silju M C <<EMAIL>>

Approvers Approved-by: Akshay Kumar Dhawle <<EMAIL>> Approved-by: Chandra Putta
<<EMAIL>>

## [2.9.0] - 2024-01-11

See merge request treez-inc/engineering/back-of-house/product-control-mfe!369

### Description

This MR address merge flow on product control grid

Dev link: https://www.figma.com/file/bbSlaxhH3NHMFJUS0xgHVX/Merge-Flow-Iteration?type=design&node-id=8637-9401&mode=dev

### Added

-   added drawer to see the list of product names that are being merged
-   added confirm modal before merging

### Close Issues

Related to #479

### Personal

Co-Authors

Merged By Rahul Reddy <<EMAIL>>

Approvers Approved-by: Silju M C <<EMAIL>> Approved-by: Andrew Bishop <<EMAIL>> Approved-by:
Akshay Kumar Dhawle <<EMAIL>>

## [2.8.0] - 2024-01-10

See merge request treez-inc/engineering/back-of-house/product-control-mfe!368

### Description

Fix for strain field value not being saved in Product Information section form.

### Fixed

-   Fix for strain field not saving

### Close Issues

Related to #517

### Personal

Co-Authors

Merged By Chandra Putta <<EMAIL>>

Approvers Approved-by: Silju M C <<EMAIL>> Approved-by: Akshay Kumar Dhawle <<EMAIL>>

## [2.7.1] - 2024-01-09

See merge request treez-inc/engineering/back-of-house/product-control-mfe!367

### Description

Refactored e2e cypress test

### Added

-   Added new test cases in variant screen.

### Changed

-   Refactored e2e cypress test.

### Close Issues

Related to #507

### Personal

Co-Authors

Merged By Keerthana Marappan <<EMAIL>>

Approvers Approved-by: Chandra Putta <<EMAIL>> Approved-by: subha ks <<EMAIL>>

## [2.7.0] - 2024-01-09

See merge request treez-inc/engineering/back-of-house/product-control-mfe!373

### Description

Use verifiedReferenceId from variant obj in Variants form

### Add

-   Added verifiedReferenceId property in VariantDto.

### Fixed

-   Use verifiedReferenceId from variant obj in Variants form

### Close Issues

### Personal

Co-Authors

Merged By Chandra Putta <<EMAIL>>

Approvers Approved-by: Akshay Kumar Dhawle <<EMAIL>>

## [2.6.0] - 2024-01-09

See merge request treez-inc/engineering/back-of-house/product-control-mfe!372

### Description

Disables editing of below fields for product and variant in Product Control MFE for Global Catalog products.

-   Product Name
-   Brand
-   Category
-   Subcategory
-   Classification
-   Strain
-   Extraction method
-   Variant Size or Merchandise Size
-   Compliance fields
-   External Ids
-   Sample or Promo content
-   Deactivating or activating or product and variant

Below are the fields that are editable

-   Description
-   eCommerce Title
-   Images
-   Price

### Added

-   Disable editing of certain fields for global content

### Close Issues

Related to #509

### Personal

Co-Authors

Merged By Chandra Putta <<EMAIL>>

Approvers Approved-by: Akshay Kumar Dhawle <<EMAIL>> Approved-by: Rahul Reddy <<EMAIL>>
Approved-by: Andrew Bishop <<EMAIL>>

## [2.5.0] - 2024-01-05

See merge request treez-inc/engineering/back-of-house/product-control-mfe!371

### Description

Adds verified badge icon and shows up for verified content in Product Control grid and Product details drawer.

### Added

-   Verified badge icon for Product Control grid and Product details drawer

### Changed

-   Updated ProductSearchResponse interface to add `productVerifiedReferenceId` field.

### Close Issues

Related to #508

### Personal

Co-Authors

Merged By Chandra Putta <<EMAIL>>

Approvers Approved-by: Rahul Reddy <<EMAIL>> Approved-by: Akshay Kumar Dhawle <<EMAIL>>
Approved-by: Silju M C <<EMAIL>>

## [2.4.1] - 2024-01-03

See merge request treez-inc/engineering/back-of-house/product-control-mfe!370

### Description

Small tweak to a phrase when activating/deactivating multiple product statuses

`has been deactivated` -\> `have been deactivated`

### Personal

Co-Authors

Merged By Leo Belanger <<EMAIL>>

Approvers Approved-by: Andrew Bishop <<EMAIL>> Approved-by: Rahul Reddy <<EMAIL>>

## [2.4.0] - 2023-12-29

See merge request treez-inc/engineering/back-of-house/product-control-mfe!366

### Description

This MR address the product category filter that is hard to use because of the amount of scrolling needed.

### Changed

-   Changed product category filter by adding a nestedAccordianCheckboxes

Related to #498

### Personal

Co-Authors

Merged By Rahul Reddy <<EMAIL>>

Approvers Approved-by: Andrew Bishop <<EMAIL>> Approved-by: Chandra Putta <<EMAIL>>

## [2.3.0] - 2023-12-18

See merge request treez-inc/engineering/back-of-house/product-control-mfe!360

### Description

added bulk action bar into product control

### Added

-   added bulk action bar into product control

### Close Issues

Related to #468

### Personal

Co-Authors Co-authored-by: Akshaykumar Dhawle <<EMAIL>>

Merged By Rahul Reddy <<EMAIL>>

Approvers Approved-by: Silju M C <<EMAIL>> Approved-by: Rahul Reddy <<EMAIL>>

## [2.2.1] - 2023-12-15

See merge request treez-inc/engineering/back-of-house/product-control-mfe!364

### Description

Refactored cypress E2E test cases

### Added

-   Added new elements in cypress E2E test cases.

### Changed

-   Updated few steps in cypress E2E test cases.

### Close Issues

Related to #503

### Personal

Co-Authors

Merged By Keerthana Marappan <<EMAIL>>

Approvers Approved-by: Christopher Thomas <<EMAIL>> Approved-by: Silju M C <<EMAIL>>

## [2.2.0] - 2023-12-13

See merge request treez-inc/engineering/back-of-house/product-control-mfe!363

### Description

Bug Fix

### Fixed

-   CBD category filtering updated

### Personal

Co-Authors Co-authored-by: Silju M.C <<EMAIL>>

Merged By Chandra Putta <<EMAIL>>

Approvers Approved-by: Chandra Putta <<EMAIL>> Approved-by: Akshay Kumar Dhawle
<<EMAIL>>

## [2.1.0] - 2023-12-13

See merge request treez-inc/engineering/back-of-house/product-control-mfe!361

### Description

Minor UX improvement

### Added

-   Loading indicator showing on category loading

### Close Issues

Related to #505

### Personal

Co-Authors

Merged By Silju M C <<EMAIL>>

Approvers Approved-by: Chandra Putta <<EMAIL>> Approved-by: Akshay Kumar Dhawle
<<EMAIL>>

## [2.0.0] - 2023-12-12

See merge request treez-inc/engineering/back-of-house/product-control-mfe!356

### Description

This MR is an enhancement to the Product Control grid to show all variants nested under their parent product.

Issue - https://gitlab.com/treez-inc/engineering/back-of-house/product-control-mfe/-/issues/426

### Added

-   Product Control grid to show all variants nested under their parent product.
-   Added Edit Variant feature on listed variants to redirect the user to variants page.

### Personal

Co-Authors Co-authored-by: Chandra Putta <<EMAIL>> Co-authored-by: Silju M.C <<EMAIL>>

Merged By Rahul Reddy <<EMAIL>>

Approvers Approved-by: Akshay Kumar Dhawle <<EMAIL>> Approved-by: Leo Belanger
<<EMAIL>>

## [1.96.0] - 2023-12-12

See merge request treez-inc/engineering/back-of-house/product-control-mfe!337

### Description

Description goes here

### Added/Updated

-   New Create/Edit Product Flow implemented
-   Some performance improvement
-   Navigation flow updates
-   Ignored unwanted post/patch requests
-   Enabled refresh browser to reload data on create and edit flow
-   API failure handling updates
-   Validations updated
-   Improved typings
-   Code cleanup
-   Removed delete variant button
-   **Sample and promo implemented**
-   **Deactivate variant implementation**

### Close Issues

Closes #456

### Personal

Co-Authors Co-authored-by: Silju M.C <<EMAIL>>

Merged By Chandra Putta <<EMAIL>>

Approvers Approved-by: Christopher Thomas <<EMAIL>> Approved-by: Akshay Kumar Dhawle
<<EMAIL>>

## [1.95.0] - 2023-12-01

See merge request treez-inc/engineering/back-of-house/product-control-mfe!353

### Description

Update to latest version of the component library

### Changed

-   Updated to latest versions of the Linear Stepper and File Upload components
    -   Linear Stepper is now used in Create and Edit flows, and previous steps are now clickable during Create flow

### Added

-   Added commands for running local MFE in sandbox and dev/build environments

### Close Issues

Closes #481

### Personal

Co-Authors

Merged By Andrew Bishop <<EMAIL>>

Approvers Approved-by: Silju M C <<EMAIL>> Approved-by: Rahul Reddy <<EMAIL>>

## [1.94.1] - 2023-11-28

See merge request treez-inc/engineering/back-of-house/product-control-mfe!352

### Description

Add product id to product variant section schema

### Added

-   `productId` as optional string field to schema

### Fixed

-   Inability to edit products with variants

### Close Issues

Closes #485

### Personal

Co-Authors Co-authored-by: Andrew Bishop <<EMAIL>>

Merged By Leo Belanger <<EMAIL>>

Approvers Approved-by: Christopher Thomas <<EMAIL>>

## [1.94.0] - 2023-11-27

See merge request treez-inc/engineering/back-of-house/product-control-mfe!350

### Description

marked status field optional in the product form.

### Changed

-   updated status field optional in the product form instead of required.

### Close Issues

Closes #483

### Personal

Co-Authors

Merged By Akshay Kumar Dhawle <<EMAIL>>

Approvers Approved-by: Christopher Thomas <<EMAIL>> Approved-by: Andrew Bishop
<<EMAIL>>

## [1.93.0] - 2023-11-27

See merge request treez-inc/engineering/back-of-house/product-control-mfe!340

### Description

Automated cypress test for Edit product information screen

### Added

-   Added test cases for Edit product information screen.

### Close Issues

Closes #467

### Personal

Co-Authors

Merged By Keerthana Marappan <<EMAIL>>

Approvers Approved-by: Andrew Bishop <<EMAIL>> Approved-by: Rahul Reddy <<EMAIL>>

## [1.92.0] - 2023-11-24

See merge request treez-inc/engineering/back-of-house/product-control-mfe!343

### Description

Provides the ability to reactivate a variant if its product is active.

### Added

-   'Activate' variant functionality.

### Fixed

-   Some variant typings

### Close Issues

Closes #475

### Personal

Co-Authors

Merged By Chandra Putta <<EMAIL>>

Approvers Approved-by: Leo Belanger <<EMAIL>> Approved-by: Akshay Kumar Dhawle
<<EMAIL>>

## [1.91.0] - 2023-11-21

See merge request treez-inc/engineering/back-of-house/product-control-mfe!344

### Description

Revert MR for #426

We saw the following `GET /product` calls fail in the product-control MFE with large data sets:
![image](/uploads/a85065b5c5fced3394338a8eb12bbdac/image.png)

### Close Issues

### Personal

Co-Authors

Merged By Leo Belanger <<EMAIL>>

Approvers Approved-by: Leo Belanger <<EMAIL>> Approved-by: Andrew Bishop <<EMAIL>>

## [1.90.0] - 2023-11-21

See merge request treez-inc/engineering/back-of-house/product-control-mfe!325

### Description

This MR is an enhancement to the Product Control grid to show all variants nested under their parent product. Also
upgraded the dataGridPro Component.

Note - In the Figma design, to represent nested product variants, the icon (>) is placed after three dots (⋮), followed
by other column attributes. However the DataGridPro Component from the treez-component-library doesn't support this, so
displays the greater-than icon first, followed by the column attributes. To address this, we need to make contribution
to update the DataGridPro at
[TCL](https://im360us.atlassian.net/wiki/spaces/development/pages/2896166916/Treez+Component+Library+Ownership+Standards)
in future. So for now, we are listing the variants with a header for variants under the parent product.

### Added

-   Product Control grid to show all variants nested under their parent product with a variant header.
-   Added Edit Variant feature on listed variants to redirect the user to variants page.

### Close Issues

### Personal

Co-Authors

Merged By Rahul Reddy <<EMAIL>>

Approvers Approved-by: Silju M C <<EMAIL>> Approved-by: Christopher Thomas <<EMAIL>>
Approved-by: Leo Belanger <<EMAIL>>

## [1.89.0] - 2023-11-20

See merge request treez-inc/engineering/back-of-house/product-control-mfe!341

### Description

The "Deactivate" button is deleting the variant in Variants form instead of deactivating it. This MR fixes that issue.

### Added

-   Added status field to Variant model.
-   Added status field to Variants form validation schema.

### Changed

-   Updated "Deactivate" button logic to update status instead of deleting the Variant.

### Removed

-   Removed duplicate files for Status enum.
-   Removed unused/unwanted files and React Hooks.

### Close Issues

Closes #471

### Personal

Co-Authors Co-authored-by: Chandra Putta <<EMAIL>>

Merged By Rahul Reddy <<EMAIL>>

Approvers Approved-by: Andrew Bishop <<EMAIL>> Approved-by: Leo Belanger <<EMAIL>>
Approved-by: Akshay Kumar Dhawle <<EMAIL>> Approved-by: Christopher Thomas
<<EMAIL>>

## [1.88.1] - 2023-11-17

See merge request treez-inc/engineering/back-of-house/product-control-mfe!335

### Description

This MR addresses the Create/Edit Flow Copy Updates from Step 2-4 on product control.

### Changed

Step 2

-   'Sizes' placeholder text -> `Size`
-   'Total MG THC' placeholder text -> `Total mg THC`
-   'Total MG CBD' placeholder text -> `Total mg CBD`
-   'THC Per Dose' -> `THC per Dose`
-   'CBD Per Dose' -> `CBD per Dose`
-   'Doses' -> `Doses per Unit`
-   External ID 'Source name' text -> `Source Name`
-   Hide from menu Tooltip -> `When this toggle is on, the variant is hidden from your eCommerce menu.`
-   'Use Global Description' checkbox ->\`Use global description

Step 3

-   Change Attributes heading to Headings/H6
-   Helper text 'Brief explanations on what attributes contribute to product' ->
    `Attributes help you track and tag products. You can show them on your retail labels and eCommerce menu, and use them to create discounts and reports. Internal Tags are only visible to employees.`

Step 4

-   Change Pricing heading to Headings/H6
-   Helper text ->
    `This price applies to all of your stores. You can manage pricing by store in Pricing Management. For stores set to Pre-Tax, this is the price before tax. For stores set to Post-Tax, this is the price including tax.`
-   '$ Variant Price' -> `Variant Price ($)`

### Close Issues

Closes #387

### Personal

Co-Authors

Merged By Rahul Reddy <<EMAIL>>

Approvers Approved-by: Silju M C <<EMAIL>> Approved-by: Andrew Bishop <<EMAIL>>

## [1.88.0] - 2023-11-17

See merge request treez-inc/engineering/back-of-house/product-control-mfe!342

### Description

Adds compliance fields to variant validation schema instead of dynamically setting them using the product category data
in the backend. This is done to avoid validation errors with imported variant data which doesn't always match with the
variant validations.

### Changed

-   Added compliance fields to variant validation schema

### Close Issues

Closes #473

### Personal

Co-Authors

Merged By Chandra Putta <<EMAIL>>

Approvers Approved-by: Akshay Kumar Dhawle <<EMAIL>> Approved-by: Silju M C <<EMAIL>>

## [1.87.0] - 2023-11-16

See merge request treez-inc/engineering/back-of-house/product-control-mfe!336

### Description

applied debouncing on search API to reduce frequent API calls.

### Added

-   applied debouncing on search API to reduce frequent API calls

### Close Issues

Closes #466

### Personal

Co-Authors Co-authored-by: Akshaykumar Dhawle <<EMAIL>>

Merged By Chandra Putta <<EMAIL>>

Approvers Approved-by: Christopher Thomas <<EMAIL>> Approved-by: Silju M C <<EMAIL>>

## [1.86.1] - 2023-11-16

See merge request treez-inc/engineering/back-of-house/product-control-mfe!339

### Description

This MR address modal copy change for deactivate products

![image](/uploads/3e73cf4d90976293dce2f409728333e7/image.png)

### Changed

-   changed deleted to deactivated in modal copy

### Close Issues

### Personal

Co-Authors Co-authored-by: rahul.reddy3 <<EMAIL>>

Merged By Silju M C <<EMAIL>>

Approvers Approved-by: Silju M C <<EMAIL>>

## [1.86.0] - 2023-11-15

See merge request treez-inc/engineering/back-of-house/product-control-mfe!331

### Description

This MR updates the modal copy when a user deactivates (formerly deletes) a variant.

### Changed

-   changed delete butto to deactivate and modal copy to be below

```
**Header**: Confirm Deactivation

**Body**:

Are you sure you want to deactivate Original TranWreck Max?

Deactivated variants cannot be invoiced in Purchasing or edited in the Catalog. You can reactivate them later, if needed.

**Note**: Any remaining inventory will still be sellable. To prevent sales, move the items to an unsellable location in Inventory Management.
```

### Close Issues

Closes #454

### Personal

Co-Authors

Merged By Rahul Reddy <<EMAIL>>

Approvers Approved-by: Chandra Putta <<EMAIL>> Approved-by: Silju M C <<EMAIL>>

## [1.85.4] - 2023-11-15

See merge request treez-inc/engineering/back-of-house/product-control-mfe!333

### Description

Added cypress test for product control screen

### Added

-   Added validation test cases on filter, sort and pagination function.
-   Added data field function in helpers.

### Changed

-   Updated few existing tests.

### Deprecated

-   for soon-to-be removed features.

### Removed

-   for now removed features.

### Fixed

-   for any bug fixes.

### Security

-   in case of vulnerabilities.

### Close Issues

### Personal

Co-Authors

Merged By Keerthana Marappan <<EMAIL>>

Approvers Approved-by: Silju M C <<EMAIL>> Approved-by: subha ks <<EMAIL>>

## [1.85.3] - 2023-11-15

See merge request treez-inc/engineering/back-of-house/product-control-mfe!317

### Description

Automated product information screen test cases.

### Added

-   Added product information screen automation test.

### Changed

-   Updated package.json file with catalog dev user credentials to run our e2e tests in build environment.

### Close Issues

#422

### Personal

Co-Authors

Merged By Keerthana Marappan <<EMAIL>>

Approvers Approved-by: subha ks <<EMAIL>> Approved-by: Silju M C <<EMAIL>>

## [1.85.2] - 2023-11-15

See merge request treez-inc/engineering/back-of-house/product-control-mfe!334

### Description

Cypress test cases for edit product detail screen

### Added

-   Added cypress autoamtiontest cases for edit product detail screen.

### Close Issues

Closes #463

### Personal

Co-Authors

Merged By Keerthana Marappan <<EMAIL>>

Approvers Approved-by: Silju M C <<EMAIL>> Approved-by: subha ks <<EMAIL>>

## [1.85.1] - 2023-11-14

See merge request treez-inc/engineering/back-of-house/product-control-mfe!319

### Description

Added test cases for - ProductDetailScreen, Create ProductPricingScreen and EditProduct Variant.

### Personal

Co-Authors

Merged By subha ks <<EMAIL>>

Approvers Approved-by: Keerthana Marappan <<EMAIL>> Approved-by: subha ks <<EMAIL>> Approved-by:
Chandra Putta <<EMAIL>>

## [1.85.0] - 2023-11-14

See merge request treez-inc/engineering/back-of-house/product-control-mfe!324

### Description

Integrated Search API into price by product page,

### Added

-   Integrated Search API into price by product page,
-   added data transformation logic.
-   handled pricing actions and provided data through location.
-   added pagination, sorting by lastUpdated and searchTerm option to call API.

### Removed

-   remove client side pagination and added server side pagination 1 to 15 size.

### Close Issues

Closes #452

### Personal

Co-Authors

Merged By Akshay Kumar Dhawle <<EMAIL>>

Approvers Approved-by: Chandra Putta <<EMAIL>> Approved-by: Silju M C <<EMAIL>>

## [1.84.0] - 2023-11-08

See merge request treez-inc/engineering/back-of-house/product-control-mfe!332

### Description

Added Product Control MFE test cases for Attribute, Edit attribute, Create Image screen and edit image test cases

### Personal

Co-Authors

Merged By subha ks <<EMAIL>>

Approvers Approved-by: Chandra Putta <<EMAIL>> Approved-by: Akshay Kumar Dhawle
<<EMAIL>> Approved-by: subha ks <<EMAIL>>

## [1.83.0] - 2023-11-03

See merge request treez-inc/engineering/back-of-house/product-control-mfe!330

### Description

Cleans up compliance fields updates.

### Changed

-   Clean up compliance fields updates.

### Close Issues

### Personal

Co-Authors

Merged By Chandra Putta <<EMAIL>>

Approvers Approved-by: Leo Belanger <<EMAIL>> Approved-by: Rahul Reddy <<EMAIL>>

## [1.82.0] - 2023-11-01

See merge request treez-inc/engineering/back-of-house/product-control-mfe!327

### Description

Dynamic field update

### Fixed

-   Issue in data not showing in product details fields

### Close Issues

### Personal

Co-Authors

Merged By Silju M C <<EMAIL>>

Approvers Approved-by: Akshay Kumar Dhawle <<EMAIL>>

## [1.81.0] - 2023-10-31

See merge request treez-inc/engineering/back-of-house/product-control-mfe!326

### Description

Sets `totalConcentrateWeight` field in variants form of Product Control MFE as soft required for Preroll product
category

### Changed

-   `totalConcentrateWeight` to be soft required for Preroll product category

### Close Issues

Closes #455

### Personal

Co-Authors

Merged By Chandra Putta <<EMAIL>>

Approvers Approved-by: Akshay Kumar Dhawle <<EMAIL>> Approved-by: Rahul Reddy <<EMAIL>>

## [1.80.0] - 2023-10-31

See merge request treez-inc/engineering/back-of-house/product-control-mfe!322

### Description

Dynamic fields for variant and product

### Added

-   Loaded detail fields dynamically based on back-end data

### Close Issues

Closes #442

### Personal

Co-Authors Co-authored-by: Silju M C <<EMAIL>> Co-authored-by: Yaswanth V <<EMAIL>>

Merged By Chandra Putta <<EMAIL>>

Approvers Approved-by: Chandra Putta <<EMAIL>>

## [1.79.0] - 2023-10-27

See merge request treez-inc/engineering/back-of-house/product-control-mfe!310

### Description

Adds new compliance fields like totalFlowerWeight, totalConcentrateWeight, and netWeightUom to Variants form in Product
Control MFE

### Added

-   Adds new compliance fields in Variants form

### Changed

-   Updates Variant validation schema to include these new compliance fields

### Personal

Co-Authors Co-authored-by: Silju M C <<EMAIL>> Co-authored-by: Yaswanth V <<EMAIL>>

Merged By Chandra Putta <<EMAIL>>

Approvers Approved-by: Chandra Putta <<EMAIL>>

## [1.78.4] - 2023-10-26

See merge request treez-inc/engineering/back-of-house/product-control-mfe!323

### Description

Sets size label to 'Pack', for any UoM option selected for Beverage category

### Fixed

-   Sets size label to 'Pack' for any UoM option selected for Beverage

### Close Issues

Closes #451

### Personal

Co-Authors Co-authored-by: Chandra Putta <<EMAIL>>

Merged By Leo Belanger <<EMAIL>>

Approvers Approved-by: Leo Belanger <<EMAIL>> Approved-by: Rahul Reddy <<EMAIL>> Approved-by:
Akshay Kumar Dhawle <<EMAIL>>

## [1.78.3] - 2023-10-25

See merge request treez-inc/engineering/back-of-house/product-control-mfe!302

### Description

Updates the test id property of Validation Error Box component. It uses the field name parameter now in the test id.
This helps in targeting a specific instance of component in cypress test cases.

### Changed

-   Updated Validation Error Box component to use field name parameter in test id.

### Close Issues

### Personal

Co-Authors Co-authored-by: Michael Moedt <<EMAIL>>

Merged By Chandra Putta <<EMAIL>>

Approvers Approved-by: Mike M <<EMAIL>>

## [1.78.2] - 2023-10-24

See merge request treez-inc/engineering/back-of-house/product-control-mfe!321

### Description

Updated the changelog and version in package.json to fill in the details missed from the previously merged MR's tag job:
https://gitlab.com/treez-inc/engineering/back-of-house/product-control-mfe/-/merge_requests/320

### Added

Changelog details for v1.78.0

### Personal

Co-Authors

Merged By Leo Belanger <<EMAIL>>

Approvers Approved-by: Leo Belanger <<EMAIL>> Approved-by: Rahul Reddy <<EMAIL>>

## [1.78.1] - 2023-10-24

See merge request treez-inc/engineering/back-of-house/product-control-mfe!320

### Description

Reverts a previous MR that was reverted. The reason this MR was originally reverted, was due to us wanting to test other
specific changes.

Original MR: https://gitlab.com/treez-inc/engineering/back-of-house/product-control-mfe/-/merge_requests/307 Original
Revert MR: https://gitlab.com/treez-inc/engineering/back-of-house/product-control-mfe/-/merge_requests/314 Associated
PMS changes: https://gitlab.com/treez-inc/engineering/back-of-house/product-management-service/-/merge_requests/540

### Personal

Co-Authors

Merged By Leo B <<EMAIL>>

Approvers Approved-by: Rahul Reddy <<EMAIL>> Approved-by: Mike M <<EMAIL>>

## [1.78.0] - 2023-10-19

See merge request treez-inc/engineering/back-of-house/product-control-mfe!312

### Description

Added new field "Total Flower Weight" to the variant section

### Added

-   Added new field, "Total Flower Weight" to the variant section
-   Added tooltip for all product types except Preroll and Flower
-   For Preroll and Flower added, required field validation

### Close Issues

Closes #406

### Personal

Co-Authors

Merged By Yaswanth V <<EMAIL>>

Approvers Approved-by: Akshay Kumar Dhawle <<EMAIL>> Approved-by: Jishnu S <<EMAIL>>
Approved-by: Chandra Putta <<EMAIL>>

## [1.77.2] - 2023-10-19

See merge request treez-inc/engineering/back-of-house/product-control-mfe!309

### Description

Updating CI config to fix errors

### Changed

-   Tweak to improve readability of tagging script

### Removed

-   Removed unneeded 'yarn install' from tagging script

### Fixed

-   Added using the cache to CI 'tag' job, resolving prettier failure and can't-find-package-issue

### Close Issues

Closes #432

### Personal

Co-Authors Co-authored-by: Michael Moedt <<EMAIL>>

Merged By Chandra Putta <<EMAIL>>

Approvers Approved-by: Rahul Reddy <<EMAIL>> Approved-by: Chandra Putta <<EMAIL>>

## [1.77.1] - 2023-10-18

See merge request treez-inc/engineering/back-of-house/product-control-mfe!298

### Description

Reverting sample promo checkboxes for bug fixes.

### Removed

-   Sample promo checkboxes from variant form.

### Personal

Co-Authors Co-authored-by: @jishnu.s <<EMAIL>>

Merged By Jason Asavadejkajorn <<EMAIL>>

Approvers Approved-by: Yaswanth V <<EMAIL>> Approved-by: Silju M C <<EMAIL>> Approved-by: Chandra
Putta <<EMAIL>>

## [1.77.0] - 2023-10-16

See merge request treez-inc/engineering/back-of-house/product-control-mfe!305

### Description

Updating version number and including missed changelog updates to fix potential tag job failures due to existing tab

### Changed

-   Adding missed updates to CHANGELOG.md

### Fixed

-   Updated version in package.json

### Close Issues

### Personal

Co-Authors

Merged By Mike M <<EMAIL>>

Approvers Approved-by: Rahul Reddy <<EMAIL>> Approved-by: Yaswanth V <<EMAIL>>

## [1.75.2] - 2023-10-16

See merge request treez-inc/engineering/back-of-house/product-control-mfe!303

### Description

Null reference error fix

### Fixed

-   Null reference error fix for category name conversion

### Close Issues

### Personal

Co-Authors

Merged By Silju M C <<EMAIL>>

Approvers Approved-by: Chandra Putta <<EMAIL>> Approved-by: Jishnu S <<EMAIL>>

## [1.76.0] - 2023-10-12

See merge request treez-inc/engineering/back-of-house/product-control-mfe!297

### Description

Fix issue with the product details slider not working for some Categories

### Fixed

-   Fixed issue with the icon component on the product details slider for the mis.matching icon name.

### Close Issues

Closes #427

### Personal

Co-Authors

Merged By Yaswanth V <<EMAIL>>

Approvers Approved-by: Chandra Putta <<EMAIL>> Approved-by: Akshay Kumar Dhawle
<<EMAIL>>

## [1.75.1] - 2023-10-16

See merge request treez-inc/engineering/back-of-house/product-control-mfe!301

### Description

Updated display deactivate name

### Personal

Co-Authors

Merged By Yaswanth V <<EMAIL>>

Approvers Approved-by: Silju M C <<EMAIL>> Approved-by: Jishnu S <<EMAIL>>

## [1.75.1] - 2023-10-12

See merge request treez-inc/engineering/back-of-house/product-control-mfe!294

### Description

Adding Variant UI verification test cases along with single and multiple variant creation

### Added

Variant UI verification test cases from Test plan Single variant creation and multiple variant creation Deletion of 1
variant test case

### Personal

Co-Authors

Merged By subha ks <<EMAIL>>

Approvers Approved-by: Dean Harmon <<EMAIL>>

## [1.75.0] - 2023-10-11

See merge request treez-inc/engineering/back-of-house/product-control-mfe!289

### Description

Updated error message for step 1 and previous button flow name

### Changed

-   Updated error message as per acceptance criteria
-   Modified existing previous button with step name as a secondary button.

### Close Issues

-   #386
-   #197

### Personal

Co-Authors

Merged By Yaswanth V <<EMAIL>>

Approvers Approved-by: Akshay Kumar Dhawle <<EMAIL>> Approved-by: Chandra Putta
<<EMAIL>>

## [1.74.0] - 2023-10-10

See merge request treez-inc/engineering/back-of-house/product-control-mfe!295

### Description

Adds `imageUrl` field to Images Section, which is part of Product creation flow.

### Added

-   Adds `imageUrl` field to Images Section

### Close Issues

Closes #420

### Personal

Co-Authors

Merged By Chandra Putta <<EMAIL>>

Approvers Approved-by: Akshay Kumar Dhawle <<EMAIL>> Approved-by: Silju M C <<EMAIL>>

## [1.73.1] - 2023-10-09

See merge request treez-inc/engineering/back-of-house/product-control-mfe!293

### Description

The Images section in Product Control MFE, throws a validation error in snackbar, while editing a product. It is due to
the newly added verifiedReferenceId field in backend, which the Images section schema in MFE doesn't recognize. This MR
fixes the issue.

### Added

-   Added verifiedReferenceId field to Images Section schema.

### Fixed

-   Fixed validation issue in Images Section which happend because the Images Section schema did not recognize the newly
    added verifiedReferenceId field.

### Close Issues

Closes #416

### Personal

Co-Authors

Merged By Chandra Putta <<EMAIL>>

Approvers Approved-by: Silju M C <<EMAIL>> Approved-by: Dean Harmon <<EMAIL>>

## [1.73.0] - 2023-10-09

See merge request treez-inc/engineering/back-of-house/product-control-mfe!290

### Description

This MR adds a product status filter to the product control grid view. This will allow the user to show active, inactive
& draft products in the list or any combination of these three options.

### Added

-   Product Status Filter

### Changed

-   Updated some interfaces that the category filter was using so that the interface could be shared with product
    status.

### Close Issues

Closes #389

### Personal

Co-Authors

Merged By Dean Harmon <<EMAIL>>

Approvers Approved-by: Yaswanth V <<EMAIL>> Approved-by: Akshay Kumar Dhawle <<EMAIL>>
Approved-by: Mike M <<EMAIL>> Approved-by: Jishnu S <<EMAIL>>

## [1.72.0] - 2023-10-06

See merge request treez-inc/engineering/back-of-house/product-control-mfe!292

### Description

Added Sample and Promo workflow into variant creation form.

### Added

-   Sample and Promo checkboxes in variant form.
-   New badge for sample and promo variants in accordion.
-   Form values autopopulation logic for sample and promo variant.

### Close Issues

Closes #404

### Personal

Co-Authors Co-authored-by: @jishnu.s <<EMAIL>>

Merged By Yaswanth V <<EMAIL>>

Approvers Approved-by: Yaswanth V <<EMAIL>> Approved-by: Silju M C <<EMAIL>>

## [1.71.0] - 2023-10-05

See merge request treez-inc/engineering/back-of-house/product-control-mfe!287

### Description

Change the product's status on the Product Control page.

### Added

-   Add menu options to kebab for Deactivate and Activate.
-   Added modal component with required content
-   Intergrated update product API

### Close Issues

#397

### Personal

Co-Authors

Merged By Yaswanth V <<EMAIL>>

Approvers Approved-by: Leo Belanger <<EMAIL>> Approved-by: Jishnu S <<EMAIL>>

## [1.70.0] - 2023-10-03

See merge request treez-inc/engineering/back-of-house/product-control-mfe!286

### Description

Product details moved into drawer component and minor design fixes.

### Added

-   New drawer component for product details.
-   New backdrop loader for product selection.

### Changed

-   Old product details view.

### Removed

-   Un-used logics in previous view.

### Close Issues

Closes #341

### Personal

Co-Authors

Merged By Jishnu S <<EMAIL>>

Approvers Approved-by: Silju M C <<EMAIL>> Approved-by: Chandra Putta <<EMAIL>>

## [1.69.0] - 2023-09-28

See merge request treez-inc/engineering/back-of-house/product-control-mfe!282

### Description

Added a filter to the Product Control view.

### Added

-   Added category along with subcategory filter to the product control screen
-   Added total row count to the display side to filter
-   Adjust the styling for the `add product` button container to match the figma design.

### Fixed

-   A bug with the add variant button in the variant creation flow has been fixed.

### Screenshot

![Screenshot_2023-09-20_at_1.47.43_PM](/uploads/315e0149efc16eda80b7bd2dea445cda/Screenshot_2023-09-20_at_1.47.43_PM.png)

### Close Issues

#329

### Personal

Co-Authors

Merged By Yaswanth V <<EMAIL>>

Approvers Approved-by: Chandra Putta <<EMAIL>> Approved-by: Jishnu S <<EMAIL>>

## [1.68.1] - 2023-09-26

See merge request treez-inc/engineering/back-of-house/product-control-mfe!285

### Description

`amount` on the ProductDto interface needs to have its type updated from string to number.

### Changed

update `amount` on ProductDto to be a number type.

### Close Issues

Closes #297

### Personal

Co-Authors Co-authored-by: rahul.reddy3 <<EMAIL>>

Merged By Leo Belanger <<EMAIL>>

Approvers Approved-by: Leo Belanger <<EMAIL>> Approved-by: Dean Harmon <<EMAIL>>

## [1.68.0] - 2023-09-25

See merge request treez-inc/engineering/back-of-house/product-control-mfe!284

### Description

Added Status column to Product Control

### Added

-   Add the status column to the product control data table.

### Close Issues

Closes #388

### Personal

Co-Authors

Merged By Yaswanth V <<EMAIL>>

Approvers Approved-by: Chandra Putta <<EMAIL>> Approved-by: Jishnu S <<EMAIL>>

## [1.67.3] - 2023-09-21

See merge request treez-inc/engineering/back-of-house/product-control-mfe!283

### Description

GitLab CI configuration update to enable auto-deploy to the build environment when MRs are successfully merged into the
main branch (and a new version is tagged)

### Added

-   Added conditions to enable testing this case

### Changed

-   Updated the rules for the deploy_build job

### Close Issues

Closes #399

### Personal

Co-Authors

Merged By Mike M <<EMAIL>>

Approvers Approved-by: Dean Harmon <<EMAIL>>

## [1.67.2] - 2023-09-15

See merge request treez-inc/engineering/back-of-house/product-control-mfe!281

### Description

sub-category name issue on the product control screen.

### Added

-   Subcategory name undefined issue

### Personal

Co-Authors Co-authored-by: yaswanth v <<EMAIL>>

Merged By Jishnu S <<EMAIL>>

Approvers Approved-by: Jishnu S <<EMAIL>>

## [1.67.1] - 2023-09-14

See merge request treez-inc/engineering/back-of-house/product-control-mfe!280

### Description

Variant label bug fix in image section.

### Fixed

-   Variant label undefined issue

### Personal

Co-Authors Co-authored-by: @jishnu.s <<EMAIL>>

Merged By Mike M <<EMAIL>>

Approvers Approved-by: Yaswanth V <<EMAIL>>

## [1.67.0] - 2023-09-14

See merge request treez-inc/engineering/back-of-house/product-control-mfe!279

### Description

Swap combo box for input & select.

### Added

-   Replaced existing combo with treez select and input component.

### Removed

-   Removed existing combo box for size and merchant size in variants section

### Close Issues

Closes #395

### Personal

Co-Authors Co-authored-by: yaswanth v <<EMAIL>> Co-authored-by: @jishnu.s <<EMAIL>>

Merged By Chandra Putta <<EMAIL>>

Approvers Approved-by: Jishnu S <<EMAIL>>

## [1.66.0] - 2023-09-13

See merge request treez-inc/engineering/back-of-house/product-control-mfe!278

### Description

Introduced variant size field to the second step

### Added

-   Introduced variant size field to the second step in the edit or create flow.

### Changed

-   Logics in the variant size section.

### Removed

-   Variant size section from the first step of the edit or create flow.

### Fixed

-   Made minor design fixes.

### Close Issues

Closes #367

### Personal

Co-Authors

Merged By Jishnu S <<EMAIL>>

Approvers Approved-by: Yaswanth V <<EMAIL>> Approved-by: Chandra Putta <<EMAIL>>

## [1.65.0] - 2023-09-11

See merge request treez-inc/engineering/back-of-house/product-control-mfe!275

### Description

Creating edit test for product images.

### Added

-   Test to edit an existing product's image.

### Close Issues

Closes #369

### Personal

Co-Authors Co-authored-by: Dean Harmon <<EMAIL>>

Merged By Mike M <<EMAIL>>

Approvers Approved-by: Mike M <<EMAIL>>

## [1.64.0] - 2023-09-07

See merge request treez-inc/engineering/back-of-house/product-control-mfe!277

### Description

Moved product details fields in (step 2) to product information page(Step 1).

### Changed

-   Moved product details fields to product information section.
-   Modified Cypress tests according to latest changes
-   Updated the API functionality to handle the change of product details to product information section.

Close Issue #365

### Personal

Co-Authors Co-authored-by: Vaishnavi Guntaka <<EMAIL>>

Merged By Yaswanth V <<EMAIL>>

Approvers Approved-by: Dean Harmon <<EMAIL>> Approved-by: Jishnu S <<EMAIL>>

## [1.63.0] - 2023-09-06

See merge request treez-inc/engineering/back-of-house/product-control-mfe!276

### Description

Updated Product Control grid to show Merch sizes for the Merch category in Size/Amount column

### Added

-   Added size, merchandise size along with size label in static chip to the existing size/amount column in the product
    control data table.
-   Added content container wrapper at root level.

### Changed

-   Changed existing column definition for size/amount column to get data from variantProperties.

### Close Issues

#340

### Personal

Co-Authors

Merged By Yaswanth V <<EMAIL>>

Approvers Approved-by: Chandra Putta <<EMAIL>> Approved-by: Silju M C <<EMAIL>>

## [1.62.2] - 2023-08-28

See merge request treez-inc/engineering/back-of-house/product-control-mfe!272

### Description

This MR removes the override value of the node image for the tagging job in the build pipeline. This will now cause the
tagging job to use the default image location for this step.

### Removed

-   tag job node image override

### Close Issues

### Personal

Co-Authors

Merged By Dean Harmon <<EMAIL>>

Approvers Approved-by: Leo Belanger <<EMAIL>>

## [1.62.1] - 2023-08-23

See merge request treez-inc/engineering/back-of-house/product-control-mfe!262

### Description

Fixed pagination navigator on control screen to highlight new (2,3...) pages when clicked.

Close Issue #337

### Personal

Co-Authors

Merged By Vaishnavi Guntaka <<EMAIL>>

Approvers Approved-by: Chandra Putta <<EMAIL>> Approved-by: Mike M <<EMAIL>>

## [1.62.0] - 2023-08-21

See merge request treez-inc/engineering/back-of-house/product-control-mfe!265

### Description

Description

Hided multi-select boxes from pricing table.

Close issue #360

### Personal

Co-Authors

Merged By Vaishnavi Guntaka <<EMAIL>>

Approvers Approved-by: Chandra Putta <<EMAIL>> Approved-by: Jishnu S <<EMAIL>> Approved-by:
Silju M C <<EMAIL>>

## [1.61.2] - 2023-08-21

See merge request treez-inc/engineering/back-of-house/product-control-mfe!263

### Description

Updated the instructions within the README for initial setup of the product-control-mfe to setup a certificate / env
vars.

### Added

-   Instruction within README

### Personal

Co-Authors Co-authored-by: Leo Belanger <<EMAIL>>

Merged By Chandra Putta <<EMAIL>>

Approvers Approved-by: Mike M <<EMAIL>> Approved-by: Jason Asavadejkajorn <<EMAIL>>

## [1.61.1] - 2023-08-16

See merge request treez-inc/engineering/back-of-house/product-control-mfe!257

### Description

Fix float handling with updates in pricing grid.

There was a bug with handling numeric values as floats, causing the sending of an invalid price ("254.99999..." instead
of "255") and resulting in an error from the back-end.

### Fixed

-   Fixed number handling for updated price value

### Close Issues

Follow-up to #250

### Personal

Co-Authors

Merged By Mike M <<EMAIL>>

Approvers Approved-by: Chandra Putta <<EMAIL>> Approved-by: Vaishnavi Guntaka
<<EMAIL>>

## [1.61.0] - 2023-08-16

See merge request treez-inc/engineering/\_team-catalog/product-control-mfe!261

### Description

Updating setting of variant details .menuTitle instead of .name, so any new created events will include the .menuTitle
field instead of .name

### Close Issues

Relates to #349

### Personal

Co-Authors

Merged By Mike M <<EMAIL>>

Approvers Approved-by: Silju M C <<EMAIL>> Approved-by: Chandra Putta <<EMAIL>>

## [1.60.0] - 2023-08-16

See merge request treez-inc/engineering/\_team-catalog/product-control-mfe!255

### Description

Added cypress test case to search products in Product Control grid.

### Added

-   Added cypress test case to search products in Product Control grid.

### Close Issues

Closes #107

### Personal

Co-Authors

Merged By Chandra Putta <<EMAIL>>

Approvers Approved-by: Dean Harmon <<EMAIL>> Approved-by: Silju M C <<EMAIL>>

## [1.59.2] - 2023-08-15

See merge request treez-inc/engineering/\_team-catalog/product-control-mfe!249

### Description

Adding onKeyDown handler to save input value

### Added

-   Adding onKeyDown handler to handle the 'Enter' key and save input value

### Close Issues

Closes #250

### Personal

Co-Authors

Merged By Mike M <<EMAIL>>

Approvers Approved-by: Vaishnavi Guntaka <<EMAIL>> Approved-by: Dean Harmon <<EMAIL>>

## [1.59.1] - 2023-08-15

See merge request treez-inc/engineering/\_team-catalog/product-control-mfe!260

### Description

This MR converts the variant size value to a string from a number before getting loaded into the product form so it can
pass form validation.

### Fixed

-   fixed validation issue related to variant displayname type when it got loaded into the product form.

### Security

-   in case of vulnerabilities.

### Close Issues

### Personal

Co-Authors

Merged By Dean Harmon <<EMAIL>>

Approvers Approved-by: Mike M <<EMAIL>>

## [1.59.0] - 2023-08-14

See merge request treez-inc/engineering/\_team-catalog/product-control-mfe!258

### Description

This MR limits the number of rendered results on the product pricing search and also displays a message to the end user
to indicate that it is not the full set of results.

### Added

-   Message to indicate that a full set of search results on product pricing search is not returned.

### Changed

-   Product Pricing Search now only renders 15 results max at a time.

### Close Issues

Closes #310

### Personal

Co-Authors

Merged By Dean Harmon <<EMAIL>>

Approvers Approved-by: Vaishnavi Guntaka <<EMAIL>> Approved-by: Mike M <<EMAIL>>

## [1.58.1] - 2023-08-14

See merge request treez-inc/engineering/\_team-catalog/product-control-mfe!259

### Description

This MR updates the copy of the variant delete confirmation modal.

### Added

-   Link to Treez Support page for re-assigning inventory to a different product.

### Changed

-   Copy on variant delete confirmation modal.

### Close Issues

Closes #346

### Personal

Co-Authors

Merged By Dean Harmon <<EMAIL>>

Approvers Approved-by: Mike M <<EMAIL>> Approved-by: Vaishnavi Guntaka <<EMAIL>>

## [1.58.0] - 2023-08-12

See merge request treez-inc/engineering/\_team-catalog/product-control-mfe!217

### Description

Adds new Merchandise Size field with "Small", "Medium", and "Large" options when Merch category is selected in Product
Information section of Product creation flow. Also it reflects these options when selected in Product Details section.

### Added

-   Adds Merchandise Size field in Product Information section

### Close Issues

Closes #274

### Personal

Co-Authors

Merged By Chandra Putta <<EMAIL>>

Approvers Approved-by: Dean Harmon <<EMAIL>> Approved-by: Jason Asavadejkajorn <<EMAIL>>
Approved-by: Vaishnavi Guntaka <<EMAIL>> Approved-by: Silju M C <<EMAIL>>

## [1.57.0] - 2023-08-11

See merge request treez-inc/engineering/\_team-catalog/product-control-mfe!251

### Description

Replaced components with slots as components were deprecated.

and also renamed pricing text

Closes Issue #319 #339

### Personal

Co-Authors

Merged By Vaishnavi Guntaka <<EMAIL>>

Approvers Approved-by: Chandra Putta <<EMAIL>> Approved-by: Dean Harmon <<EMAIL>>

## [1.56.1] - 2023-08-11

See merge request treez-inc/engineering/\_team-catalog/product-control-mfe!256

### Description

Updates findCategoryIcon utility function to return a default icon if an icon doesn't exist for a product category in
Treez Component library.

### Changed

-   Updates findCategoryIcon utility function to return a default icon for product category

### Close Issues

Closes #358

### Personal

Co-Authors

Merged By Chandra Putta <<EMAIL>>

Approvers Approved-by: Silju M C <<EMAIL>>

## [1.56.0] - 2023-08-10

See merge request treez-inc/engineering/\_team-catalog/product-control-mfe!253

### Description

Enabled fields for Brand and Product name before selecting a product.

### Close Issues

#323

### Personal

Co-Authors

Merged By Vaishnavi Guntaka <<EMAIL>>

Approvers Approved-by: Chandra Putta <<EMAIL>> Approved-by: Mike M <<EMAIL>>

## [1.55.1] - 2023-08-09

See merge request treez-inc/engineering/\_team-catalog/product-control-mfe!250

### Description

The product table will now sort by latest updated product by default.

### Added

-   default sort by desc date added in initial search call.

### Close Issues

Closes #320

### Personal

Co-Authors Co-authored-by: Dean Harmon <<EMAIL>>

Merged By Mike M <<EMAIL>>

Approvers Approved-by: Vaishnavi Guntaka <<EMAIL>> Approved-by: Mike M <<EMAIL>>
Approved-by: Chandra Putta <<EMAIL>>

## [1.55.0] - 2023-08-08

See merge request treez-inc/engineering/\_team-catalog/product-control-mfe!243

### Description

Made Description field non editable when description checkbox is checked. Also, made checkbox checked in by default.

Closes Issue #299

### Personal

Co-Authors Co-authored-by: Vaishnavi Guntaka <<EMAIL>>

Merged By Chandra Putta <<EMAIL>>

Approvers Approved-by: Silju M C <<EMAIL>> Approved-by: Jason Asavadejkajorn <<EMAIL>> Approved-by:
Chandra Putta <<EMAIL>>

## [1.54.1] - 2023-08-05

See merge request treez-inc/engineering/\_team-catalog/product-control-mfe!246

### Description

Update to number parsing to allow decimals to be input without leading zero e.g. Now '.5' is accepted as the number
value 0.5

### Added

-   Added ability to input decimals by starting with the decimal point

### Close Issues

Closes #275

### Personal

Co-Authors

Merged By Mike M <<EMAIL>>

Approvers Approved-by: Jason Asavadejkajorn <<EMAIL>> Approved-by: Vaishnavi Guntaka <<EMAIL>>

## [1.54.0] - 2023-08-04

See merge request treez-inc/engineering/\_team-catalog/product-control-mfe!247

### Description

Added Product category icons for Edible, Extract, Misc by using enums.

Closes Issue #332

### Personal

Co-Authors

Merged By Vaishnavi Guntaka <<EMAIL>>

Approvers Approved-by: Jason Asavadejkajorn <<EMAIL>> Approved-by: Mike M <<EMAIL>>

## [1.53.0] - 2023-08-04

See merge request treez-inc/engineering/\_team-catalog/product-control-mfe!244

### Description

Deleted tax column from the pricing page.

Close Issue #326

### Personal

Co-Authors

Merged By Vaishnavi Guntaka <<EMAIL>>

Approvers Approved-by: Silju M C <<EMAIL>> Approved-by: Chandra Putta <<EMAIL>>

## [1.52.0] - 2023-08-03

See merge request treez-inc/engineering/\_team-catalog/product-control-mfe!240

### Description

Added a styled box to the Product Form Section Container

### Close Issues

Closes #286

### Personal

Co-Authors

Merged By Mike M <<EMAIL>>

Approvers Approved-by: Jason Asavadejkajorn <<EMAIL>>

## [1.51.3] - 2023-08-02

See merge request treez-inc/engineering/\_team-catalog/product-control-mfe!241

### Description

This MR fixes an issue where input labels on the product create/edit form were overlaying the button navigation at the
bottom of the form.

### Fixed

-   fixed visual issue for input labels showing up on top of bottom form navigation.

### Close Issues

Closes #328

### Personal

Co-Authors

Merged By Dean Harmon <<EMAIL>>

Approvers Approved-by: Mike M <<EMAIL>> Approved-by: Vaishnavi Guntaka <<EMAIL>>

## [1.51.2] - 2023-08-02

See merge request treez-inc/engineering/\_team-catalog/product-control-mfe!234

### Description

Further cleanup and refactor re: tokens and authentication handling

### Changed

-   Code cleanup, no functional changes.

### Related Issues

Related to #334

### Personal

Co-Authors

Merged By Mike M <<EMAIL>>

Approvers Approved-by: Silju M C <<EMAIL>> Approved-by: Mike M <<EMAIL>>

## [1.51.1] - 2023-08-02

See merge request treez-inc/engineering/\_team-catalog/product-control-mfe!221

### Description

Description

Updated Save and Close button functionality to check validation error before it guides to product control screen.

Closes Issue #289

### Personal

Co-Authors Co-authored-by: Vaishnavi Guntaka <<EMAIL>>

Merged By Chandra Putta <<EMAIL>>

Approvers Approved-by: Chandra Putta <<EMAIL>> Approved-by: Jason Asavadejkajorn <<EMAIL>>

## [1.51.0] - 2023-08-01

See merge request treez-inc/engineering/\_team-catalog/product-control-mfe!224

### Description

Sorted Brand and Subcategory names in Alphabetical order.

Closes Issue #301

### Personal

Co-Authors Co-authored-by: Dean Harmon <<EMAIL>>

Merged By Vaishnavi Guntaka <<EMAIL>>

Approvers Approved-by: Chandra Putta <<EMAIL>> Approved-by: Mike M <<EMAIL>>

## [1.50.0] - 2023-08-01

See merge request treez-inc/engineering/\_team-catalog/product-control-mfe!239

### Description

Updating look and feel of Product Create/Edit View.

### Changed

-   Product Create/Edit view is now wrapped in a Dialog Component making it full screen.
-   Form is now scrollable instead of entire page.
-   Form buttons moved.

### Close Issues

#283

### Personal

Co-Authors Co-authored-by: Dean Harmon <<EMAIL>>

Merged By Mike M <<EMAIL>>

Approvers Approved-by: Mike M <<EMAIL>> Approved-by: Vaishnavi Guntaka <<EMAIL>>

## [1.49.2] - 2023-08-01

See merge request treez-inc/engineering/\_team-catalog/product-control-mfe!238

### Description

Updated kebab icon in Product Control grid.

### Changed

-   Updated kebab icon in Product Control grid.

### Close Issues

Closes #305

### Personal

Co-Authors

Merged By Chandra Putta <<EMAIL>>

Approvers Approved-by: Vaishnavi Guntaka <<EMAIL>> Approved-by: Silju M C <<EMAIL>>

## [1.49.1] - 2023-08-01

See merge request treez-inc/engineering/\_team-catalog/product-control-mfe!236

### Description

Fixes variant price calculation in Pricing section of Product creation flow.

### Fixed

-   Fixes variant price calculation in Pricing section.

### Close Issues https://gitlab.com/treez-inc/engineering/_team-catalog/product-management-service/-/issues/546

### Personal

Co-Authors

Merged By Chandra Putta <<EMAIL>>

Approvers Approved-by: Dean Harmon <<EMAIL>> Approved-by: Vaishnavi Guntaka <<EMAIL>>

## [1.49.0] - 2023-07-31

See merge request treez-inc/engineering/\_team-catalog/product-control-mfe!223

### Description

Removed validation for Menu Title field.

Closes Issue #292

### Personal

Co-Authors

Merged By Vaishnavi Guntaka <<EMAIL>>

Approvers Approved-by: Chandra Putta <<EMAIL>> Approved-by: Mike M <<EMAIL>>

## [1.48.0] - 2023-07-31

See merge request treez-inc/engineering/\_team-catalog/product-control-mfe!229

### Description

Made checkbox checked as default for Use global description for variants and Made sure the text in global description
replica the text when checkbox checked in.

Closes Issue #299

### Personal

Co-Authors Co-authored-by: Chandra Putta <<EMAIL>>

Merged By Vaishnavi Guntaka <<EMAIL>>

Approvers Approved-by: Silju M C <<EMAIL>> Approved-by: Chandra Putta <<EMAIL>>

## [1.47.1] - 2023-07-31

See merge request treez-inc/engineering/\_team-catalog/product-control-mfe!237

### Description

Fixes access token issue in Images section of Product creation flow in UI.

### Fixed

-   Fixes access token issue in Images section

### Close Issues

Closes #321

### Personal

Co-Authors

Merged By Chandra Putta <<EMAIL>>

Approvers Approved-by: Silju M C <<EMAIL>> Approved-by: Dean Harmon <<EMAIL>> Approved-by: Mike M
<<EMAIL>>

## [1.47.0] - 2023-07-31

See merge request treez-inc/engineering/\_team-catalog/product-control-mfe!231

### Description

Added some styles in Product Control page.

### Changed

-   Changed Background color to white, #FFFFFF
-   Updated horizontal grid lines to light grey: #F0F0F0
-   Updated grid header/hover line to light grey: #F0F0F0
-   Updated search input background to light grey: #F0F0F0

### Close Issues

Closes #285

### Personal

Co-Authors

Merged By Chandra Putta <<EMAIL>>

Approvers Approved-by: Silju M C <<EMAIL>> Approved-by: Vaishnavi Guntaka <<EMAIL>>

## [1.46.1] - 2023-07-28

See merge request treez-inc/engineering/\_team-catalog/product-control-mfe!235

### Description

Updating CI pipeline to autocorrect style violatons

### Added

-   Adds auto-correction of code via prettier, reducing failing pipelines.

### Close Issues

Closes #147

### Personal

Co-Authors

Merged By Mike M <<EMAIL>>

Approvers Approved-by: Vaishnavi Guntaka <<EMAIL>>

## [1.46.0] - 2023-07-26

See merge request treez-inc/engineering/\_team-catalog/product-control-mfe!222

### Description

Update the API accessor functions to use the framework provided getTokens functions for the Authorization header.

### Changed

-   Using framework provided function directly in the accessor functions, instead of used only once to get the initial
    tokens

### Deprecated

-   There is needed cleanup to remove unused(?) code that tracks the tokens ourselves. (See comments added in this MR)

### Fixed

-   Fixes token expiry issues; the latest refreshed tokens should now be used

### Close Issues

Closes #290

### Personal

Co-Authors

Merged By Mike M <<EMAIL>>

Approvers Approved-by: Vaishnavi Guntaka <<EMAIL>> Approved-by: Chandra Putta
<<EMAIL>>

## [1.45.0] - 2023-07-26

See merge request treez-inc/engineering/\_team-catalog/product-control-mfe!218

### Description

When product category is changed, the form field validations do not work properly. Also, the product category can be
changed after a product has been created. Allowing the product category to be changed will cause more issues. This MR
fixes these two issues.

### Changed

-   Product Category cannot be changed once that particular product is created.

### Fixed

-   Shows validation errors correctly after product category is changed.

### Close Issues

Closes #291

### Personal

Co-Authors

Merged By Chandra Putta <<EMAIL>>

Approvers Approved-by: Vaishnavi Guntaka <<EMAIL>> Approved-by: Mike M <<EMAIL>>

## [1.44.1] - 2023-07-25

See merge request treez-inc/engineering/\_team-catalog/product-control-mfe!232

### Description

This MR will allow the user to clear the value of brand name from the autocomplete input in the Product Information
section of the product create/edit view.

### Fixed

-   value of brand was being set, but not allowing for a null value to occur when the clear icon was clicked in the
    autocomplete component. This has been addressed.

### Close Issues

Closes #293

### Personal

Co-Authors

Merged By Dean Harmon <<EMAIL>>

Approvers Approved-by: Vaishnavi Guntaka <<EMAIL>> Approved-by: Mike M <<EMAIL>>

## [1.44.0] - 2023-07-25

See merge request treez-inc/engineering/\_team-catalog/product-control-mfe!227

### Description

Replaced Kebab and Context Menu components with the components from Treez Component Library.

### Changed

-   Replaced Kebab and Context Menu components

### Close Issues

Closes #302

### Personal

Co-Authors

Merged By Chandra Putta <<EMAIL>>

Approvers Approved-by: Dean Harmon <<EMAIL>> Approved-by: Vaishnavi Guntaka <<EMAIL>>

## [1.43.1] - 2023-07-19

See merge request treez-inc/engineering/\_team-catalog/product-control-mfe!220

### Description

Change Image CDN URL based on env.

### Added

-   conditional in constants file to check for prod stage value and set Image CDN URL accordingly when we are on the
    prod env.

### Fixed

-   prod image CDN URL

### Close Issues

### Personal

Co-Authors

Merged By Dean Harmon <<EMAIL>>

Approvers Approved-by: Jason Asavadejkajorn <<EMAIL>> Approved-by: Mike M <<EMAIL>>

## [1.43.0] - 2023-07-19

See merge request treez-inc/engineering/\_team-catalog/product-control-mfe!216

### Description

1. Fixed Save and Close button by adding the check condition event is getting or not. The save and close button will not
   get event related objects.

2. Also Fixed Deleting duplicate sizes.

Close Issue #289

### Personal

Co-Authors Co-authored-by: Vaishnavi Guntaka <<EMAIL>>

Merged By Chandra Putta <<EMAIL>>

Approvers Approved-by: Chandra Putta <<EMAIL>> Approved-by: Dean Harmon <<EMAIL>>

## [1.42.0] - 2023-07-19

See merge request treez-inc/engineering/\_team-catalog/product-control-mfe!205

### Description

Modified Brand Prompt.

Updated Text for Brand Component.

Updated Confirmation snackbar.

Closes Issue #263

### Personal

Co-Authors Co-authored-by: Vaishnavi Guntaka <<EMAIL>>

Merged By Chandra Putta <<EMAIL>>

Approvers Approved-by: Jason Asavadejkajorn <<EMAIL>> Approved-by: Chandra Putta <<EMAIL>>
Approved-by: Dean Harmon <<EMAIL>>

## [1.41.0] - 2023-07-18

See merge request treez-inc/engineering/\_team-catalog/product-control-mfe!214

### Description

When there are no variants added in Product Information section, and we try to add one in Product Details section, it
throws a validation error. The error indicates that the size label field is empty, which is not allowed. This MR fixes
that issue.

### Fixed

-   Fixes Size Label validation issue in Product Details section when no prior variants exist for a product.

### Close Issues

Closes #288

### Personal

Co-Authors

Merged By Chandra Putta <<EMAIL>>

Approvers Approved-by: Dean Harmon <<EMAIL>> Approved-by: Vaishnavi Guntaka <<EMAIL>>

## [1.40.0] - 2023-07-17

See merge request treez-inc/engineering/\_team-catalog/product-control-mfe!215

### Description

This MR will add images to the product preview modal.

### Added

-   Main image
-   Secondary images
-   Product Images interface

### Changed

-   Product interface

### Close Issues

Closes #280

### Personal

Co-Authors

Merged By Dean Harmon <<EMAIL>>

Approvers Approved-by: Mike M <<EMAIL>>

## [1.39.0] - 2023-07-17

See merge request treez-inc/engineering/\_team-catalog/product-control-mfe!202

### Description

Updating format and minor functionality of the product preview modal.

### Added

-   Edit button to take user directly to edit product screen from the modal.
-   Added `amount` type to `ProductDto` interface.

### Changed

-   Type is now Category
-   SubType is now Subcategory
-   Minor layout updates
-   Made Category, Subcategory, Amount & Price Range h6 variant so they are more called out to closer match the design.

### Removed

-   Close button at bottom of modal is removed.

### Close Issues

Closes #262

### Personal

Co-Authors

Merged By Dean Harmon <<EMAIL>>

Approvers Approved-by: Vaishnavi Guntaka <<EMAIL>> Approved-by: Chandra Putta
<<EMAIL>>

## [1.38.0] - 2023-07-17

See merge request treez-inc/engineering/\_team-catalog/product-control-mfe!212

### Description

Fixes the submit data issue when there are no variants to show in Pricing section.

### Fixed

-   Fixes submit data issue when there is no data in Pricing section.

### Close Issues

Closes #287

### Personal

Co-Authors

Merged By Chandra Putta <<EMAIL>>

Approvers Approved-by: Dean Harmon <<EMAIL>> Approved-by: Vaishnavi Guntaka <<EMAIL>>
Approved-by: Jason Asavadejkajorn <<EMAIL>>

## [1.37.0] - 2023-07-13

See merge request treez-inc/engineering/\_team-catalog/product-control-mfe!204

### Description

Changed the text to save and continue in create and edit.

Removed "Existing" from product details.

Added Blue colour filled chip with text "New".

Closes Issues #265 #267

### Personal

Co-Authors Co-authored-by: Vaishnavi Guntaka <<EMAIL>>

Merged By Chandra Putta <<EMAIL>>

Approvers Approved-by: Silju M C <<EMAIL>> Approved-by: Chandra Putta <<EMAIL>>

## [1.36.0] - 2023-07-12

See merge request treez-inc/engineering/\_team-catalog/product-control-mfe!211

### Description

The Done button in Images section is not functional. This MR fixes that issue.

### Fixed

-   Fixed Done button functionality issue in Images section.

### Close Issues

### Personal

Co-Authors

Merged By Chandra Putta <<EMAIL>>

Approvers Approved-by: Chandra Putta <<EMAIL>>

## [1.35.0] - 2023-07-12

See merge request treez-inc/engineering/\_team-catalog/product-control-mfe!209

### Description

The value of Use Global Description checkbox in Product Details section is not getting set to a boolean in some
occasions. It causes the form to be non-functional and doesn't submit the form. This MR fixes that issue.

### Fixed

-   Fixes the improper setting of value for Use Global Description checkbox field

### Close Issues

Closes #282

### Personal

Co-Authors

Merged By Chandra Putta <<EMAIL>>

Approvers Approved-by: Silju M C <<EMAIL>>

## [1.34.0] - 2023-07-12

See merge request treez-inc/engineering/\_team-catalog/product-control-mfe!206

### Description

Fixes a validation issue in Pricing section of Product creation flow.

### Fixed

-   Fixes a validation issue in pricing section.

### Close Issues

### Personal

Co-Authors

Merged By Chandra Putta <<EMAIL>>

Approvers Approved-by: Silju M C <<EMAIL>>

## [1.33.1] - 2023-07-11

See merge request treez-inc/engineering/\_team-catalog/product-control-mfe!200

### Description

This MR will update formatting for the product preview modal.

### Changed

-   styling on text on the product preview modal.

### Close Issues

Closes #261

### Personal

Co-Authors

Merged By Dean Harmon <<EMAIL>>

Approvers Approved-by: Jason Asavadejkajorn <<EMAIL>> Approved-by: Vaishnavi Guntaka <<EMAIL>>

## [1.33.0] - 2023-07-10

See merge request treez-inc/engineering/\_team-catalog/product-control-mfe!203

### Description

Fixes "path.split is not a function" error in Product Information section of Product creation flow.

### Fixed

-   Fixed "path.split is not a function" error

### Close Issues

Closes #279

### Personal

Co-Authors Co-authored-by: Chandra Putta <<EMAIL>>

Merged By Mike M <<EMAIL>>

Approvers Approved-by: Vaishnavi Guntaka <<EMAIL>> Approved-by: Mike M <<EMAIL>>

## [1.32.0] - 2023-07-10

See merge request treez-inc/engineering/\_team-catalog/product-control-mfe!193

### Description

Fixes validation issues for all sections of Product form including the changes made to validation schemas as per
requirements. Also added snackbar message to show back end error if it occurs.

### Changed

-   Changed validation schemas of Product form as per requirements.

### Fixed

-   Validation issues in all sections of Product form.

### Close Issues

Closes #244

### Personal

Co-Authors

Merged By Chandra Putta <<EMAIL>>

Approvers Approved-by: Jason Asavadejkajorn <<EMAIL>> Approved-by: Vaishnavi Guntaka <<EMAIL>>

## [1.31.0] - 2023-07-07

See merge request treez-inc/engineering/\_team-catalog/product-control-mfe!201

### Description

Added toast message when product search returns an error.

### Added

-   Treez component snackbar to product control screen.

### Close Issues

Closes #273

### Personal

Co-Authors

Merged By Dean Harmon <<EMAIL>>

Approvers Approved-by: Vaishnavi Guntaka <<EMAIL>> Approved-by: Mike M <<EMAIL>>

## [1.30.0] - 2023-07-06

See merge request treez-inc/engineering/\_team-catalog/product-control-mfe!198

### Description

-   Stored the price value from dollar to cents in database and displayed the price back in dollars and conversion is
    done in frontend. (ex: in the screenshot default prices shown is 700cents and but displays back in $7.
-   Done for whole pricing section which includes manage pricing section.

Closes Issue #270

![Screenshot_2023-07-01_at_12.24.09_AM](/uploads/68e134f22cc0f60ad7e5865559763683/Screenshot_2023-07-01_at_12.24.09_AM.png)

### Personal

Co-Authors

Merged By Vaishnavi Guntaka <<EMAIL>>

Approvers Approved-by: Dean Harmon <<EMAIL>> Approved-by: Mike M <<EMAIL>> Approved-by:
Chandra Putta <<EMAIL>>

## [1.29.0] - 2023-07-05

See merge request treez-inc/engineering/\_team-catalog/product-control-mfe!195

### Description

Saves the progress in current section when clicked on Previous button. Previously this functionality was available only
for the Next button.

### Added

-   Saves data in current section in Product creation flow when clicked on Previous button

### Close Issues

Closes #269

### Personal

Co-Authors

Merged By Chandra Putta <<EMAIL>>

Approvers Approved-by: Mike M <<EMAIL>> Approved-by: Vaishnavi Guntaka <<EMAIL>>

## [1.28.0] - 2023-07-04

See merge request treez-inc/engineering/\_team-catalog/product-control-mfe!197

### Description

Removes leading and trailing spaces when creating new brand and attribute values in Product Information section and
Product Attributes section respectively.

### Changed

-   Removes leading and trailing spaces when creating new values for brand and attribute.

### Fixed

-   Resets the typed in new value when clicked on cancel in confirmation modal.
-   Doesn't allow creation of empty string as value for both brand and attribute.

### Close Issues

Closes #260

### Personal

Co-Authors Co-authored-by: Chandra Putta <<EMAIL>>

Merged By Mike M <<EMAIL>>

Approvers Approved-by: Mike M <<EMAIL>>

## [1.27.1] - 2023-07-03

See merge request treez-inc/engineering/\_team-catalog/product-control-mfe!194

### Description

With this MR the product create/edit form will no longer proceed when hitting the enter key.

### Fixed

-   added an event listener and prevent default form submission behavior when the enter key is pressed.

### Close Issues

#277

### Personal

Co-Authors

Merged By Dean Harmon <<EMAIL>>

Approvers Approved-by: Chandra Putta <<EMAIL>> Approved-by: Mike M <<EMAIL>> Approved-by:
Vaishnavi Guntaka <<EMAIL>>

## [1.27.0] - 2023-07-03

See merge request treez-inc/engineering/\_team-catalog/product-control-mfe!196

### Description

This MR changes some permissions in the product create/edit form.

### Changed

-   CATALOG_SERVICE_CREATE -> ADJUST_PRODUCT
-   CREATE_ATTRIBIUTE -> ADJUST_ATTRIBUTE
-   CREATE_BRAND -> ADJUST_BRAND

### Removed

-   CATALOG_SERVICE_CREATE permission
-   CREATE_ATTRIBUTE permission
-   CREATE_BRAND permission

### Close Issues

Closes #272

### Personal

Co-Authors

Merged By Dean Harmon <<EMAIL>>

Approvers Approved-by: Silju M C <<EMAIL>>

## [1.26.1] - 2023-06-28

See merge request treez-inc/engineering/\_team-catalog/product-control-mfe!191

### Description

This MR fixes pagination issues.

### Removed

-   loading state check from DataGridPro component. When doing a new search, it was resetting the default pagination to
    0 each time.

### Fixed

-   Product Grid Pagination.

### Close Issues

Closes #266

### Personal

Co-Authors

Merged By Dean Harmon <<EMAIL>>

Approvers Approved-by: Mike M <<EMAIL>>

## [1.26.0] - 2023-06-28

See merge request treez-inc/engineering/\_team-catalog/product-control-mfe!184

### Description

### Personal

Co-Authors

Merged By Vaishnavi Guntaka <<EMAIL>>

Approvers Approved-by: Dean Harmon <<EMAIL>>

## [1.25.2] - 2023-06-27

See merge request treez-inc/engineering/\_team-catalog/product-control-mfe!188

### Description

Reverts changes to domain names.

### Personal

Co-Authors

Merged By Jason Asavadejkajorn <<EMAIL>>

Approvers Approved-by: Jason Asavadejkajorn <<EMAIL>>

## [1.25.1] - 2023-06-26

See merge request treez-inc/engineering/\_team-catalog/product-control-mfe!186

### Description

Quick CI configuration fix

### Changed

-   Updated to use the latest template version

### Fixed

-   Enabling deploy_prod job for versioned branch that has tag job on it

### Close Issues

### Personal

Co-Authors

Merged By Mike M <<EMAIL>>

Approvers Approved-by: Vaishnavi Guntaka <<EMAIL>> Approved-by: Jason Asavadejkajorn <<EMAIL>>

## [1.25.0] - 2023-06-26

See merge request treez-inc/engineering/\_team-catalog/product-control-mfe!182

### Description

Description

Added SizeLabel in images section.

### Personal

Co-Authors

Merged By Vaishnavi Guntaka <<EMAIL>>

Approvers Approved-by: Chandra Putta <<EMAIL>>

## [1.24.1] - 2023-06-21

See merge request treez-inc/engineering/\_team-catalog/product-control-mfe!181

### Description

Changes SKU field validation to allow null values in Product Control MFE.

### Changed

Changes SKU field validation to allow null values

### Close Issues

Closes #256

### Personal

Co-Authors

Merged By Chandra Putta <<EMAIL>>

Approvers Approved-by: Jonatan Brandao <<EMAIL>> Approved-by: Vaishnavi Guntaka
<<EMAIL>>

## [1.24.0] - 2023-06-20

See merge request treez-inc/engineering/\_team-catalog/product-control-mfe!179

### Description

This fixes the permission access for users who should not have access to the Product Control Grid. This is not the
long-term fix. After permissions for catalog have been finalized, we should come back to this work and update.

### Added

-   loading state for ProductControl Page (PageLoader component from Treez component library)
-   Error message that displays when receiving a 401 or 403 response from the data call to the catalog product list
    endpoint.

### Changed

-   The product grid will now only display when user has permissions.
-   updated Treez component library version to fix issue with PageLoader component.

### Close Issues

Closes #249

### Personal

Co-Authors

Merged By Dean Harmon <<EMAIL>>

Approvers Approved-by: Mike M <<EMAIL>> Approved-by: Vaishnavi Guntaka <<EMAIL>>

## [1.23.0] - 2023-06-20

See merge request treez-inc/engineering/\_team-catalog/product-control-mfe!180

### Description

Show "No products to display" message in Product Control grid when there is no data or when searched for a product that
doesn't exist.

### Added

-   Shows "No products to display" message in Product Control grid.

### Close Issues

Closes #247

### Personal

Co-Authors

Merged By Chandra Putta <<EMAIL>>

Approvers Approved-by: Mike M <<EMAIL>> Approved-by: Vaishnavi Guntaka <<EMAIL>>

## [1.22.1] - 2023-06-19

See merge request treez-inc/engineering/\_team-catalog/product-control-mfe!175

### Description

Removes brand filter in Price by Product page.

### Removed

-   Removed brand filter in Price by Product page.

### Close Issues

Closes #232

### Personal

Co-Authors

Merged By Chandra Putta <<EMAIL>>

Approvers Approved-by: Vaishnavi Guntaka <<EMAIL>> Approved-by: Jason Asavadejkajorn <<EMAIL>>

## [1.22.0] - 2023-06-16

See merge request treez-inc/engineering/\_team-catalog/product-control-mfe!174

### Description

Description

Disabled Manage Pricing button when no input was given in retail price. Also Changed the labels for image section.

Closes tickets #198 #245

### Personal

Co-Authors

Merged By Vaishnavi Guntaka <<EMAIL>>

Approvers Approved-by: Chandra Putta <<EMAIL>> Approved-by: Jason Asavadejkajorn <<EMAIL>>

## [1.21.2] - 2023-06-14

See merge request treez-inc/engineering/\_team-catalog/product-control-mfe!173

### Description

Changes placeholder image in Price by Product page.

### Changed

-   Changed placeholder image in Price by Product page.

### Close Issues

Closes #229

### Personal

Co-Authors

Merged By Chandra Putta <<EMAIL>>

Approvers Approved-by: Dean Harmon <<EMAIL>> Approved-by: Jonatan Brandao <<EMAIL>>

## [1.21.1] - 2023-06-13

See merge request treez-inc/engineering/\_team-catalog/product-control-mfe!167

### Description

In the pricing management area, when editing price for a store in the grid is causing an issue. This MR fixes it.

### Fixed

-   Fixed edit price feature in pricing management grid in Price by Product page.

### Close Issues

Closes #242

### Personal

Co-Authors

Merged By Chandra Putta <<EMAIL>>

Approvers Approved-by: Vaishnavi Guntaka <<EMAIL>> Approved-by: Jason Asavadejkajorn <<EMAIL>>

## [1.21.0] - 2023-06-12

See merge request treez-inc/engineering/\_team-catalog/product-control-mfe!165

### Description

Description

Updated UI to filter product information, product details and variants by product category. Displaying the fields as per
category.

Closes #209

### Personal

Co-Authors Co-authored-by: Chandra Putta <<EMAIL>> Co-authored-by: Mike M <<EMAIL>>

Merged By Vaishnavi Guntaka <<EMAIL>>

Approvers Approved-by: Dean Harmon <<EMAIL>> Approved-by: Mike M <<EMAIL>>

## [1.20.0] - 2023-06-12

See merge request treez-inc/engineering/\_team-catalog/product-control-mfe!168

### Description

This MR will change the behavior of the Product Form Exit Modal. The exit modal will now appear only when a user has
made a change to one of the form inputs.

### Changed

-   behavior of product form exit modal now based on user interaction. It no longer appears every time the user changes
    sections.

### Fixed

-   The section the user will be nagigating to now has a hover effect.

Closes #136

### Personal

Co-Authors

Merged By Dean Harmon <<EMAIL>>

Approvers Approved-by: Mike M <<EMAIL>> Approved-by: Vaishnavi Guntaka <<EMAIL>>

## [1.19.2] - 2023-06-12

See merge request treez-inc/engineering/\_team-catalog/product-control-mfe!164

### Description

In Price by Product page, the Pricing Management table doesn't show variants as columns. This MR adds variants as
columns to that table.

### Fixed

Variants were not showing up as columns in Pricing Management table. This is fixed now.

### Close Issues

Closes #233

### Personal

Co-Authors

Merged By Chandra Putta <<EMAIL>>

Approvers Approved-by: Jason Asavadejkajorn <<EMAIL>> Approved-by: Vaishnavi Guntaka <<EMAIL>>

## [1.19.1] - 2023-06-12

See merge request treez-inc/engineering/\_team-catalog/product-control-mfe!161

### Description

When the last image in order for a product or variant is deleted, the Images section tries to reorder other images and
throws an error. The ordering works only if there are other images after the image that is subjected to change. This MR
fixes that bug.

### Changed

-   Changed naming of some methods to make it more apt to the method's functionality.

### Fixed

-   Fixed the issue of image reordering when last image in order is deleted.
-   Removed Authorization header in PUT request of Axios, which was causing issues uploading images using File Upload
    component.

### Close Issues

Closes #231

### Personal

Co-Authors

Merged By Chandra Putta <<EMAIL>>

Approvers Approved-by: Jason Asavadejkajorn <<EMAIL>> Approved-by: Vaishnavi Guntaka <<EMAIL>>

## [1.19.0] - 2023-06-09

See merge request treez-inc/engineering/\_team-catalog/product-control-mfe!166

### Description

Allow zero value for all number validation for Variant Section.

### Changed

-   Allow zero value for all number validation for Variant Section.

### Close Issues

Closes #236

### Personal

Co-Authors Co-authored-by: Jonatan Brandao <<EMAIL>>

Merged By Mike M <<EMAIL>>

Approvers

## [1.18.0] - 2023-06-06

See merge request treez-inc/engineering/\_team-catalog/product-control-mfe!158

### Description

Clicking on Done button in Images section doesn't do anything. This MR fixes this issue.

### Fixed

-   Images section form submit issue.

### Close Issues

Closes #230

### Personal

Co-Authors

Merged By Chandra Putta <<EMAIL>>

Approvers Approved-by: Jason Asavadejkajorn <<EMAIL>> Approved-by: Vaishnavi Guntaka <<EMAIL>>

## [1.17.0] - 2023-06-05

See merge request treez-inc/engineering/\_team-catalog/product-control-mfe!155

### Description

Reformatted variant details form to match new UX screen.

Closes issue #216

### Personal

Co-Authors Co-authored-by: Vaishnavi Guntaka <<EMAIL>>

Merged By Dean Harmon <<EMAIL>>

Approvers Approved-by: Dean Harmon <<EMAIL>> Approved-by: Chandra Putta <<EMAIL>>

## [1.16.0] - 2023-06-05

See merge request treez-inc/engineering/\_team-catalog/product-control-mfe!153

### Description

Refactored product details form to match new UX screen. Strain field is added to product form and classification is
changed to dropdown.

Closes issue #217

### Personal

Co-Authors

Merged By Vaishnavi Guntaka <<EMAIL>>

Approvers Approved-by: Dean Harmon <<EMAIL>> Approved-by: Chandra Putta <<EMAIL>>

## [1.15.0] - 2023-06-05

See merge request treez-inc/engineering/\_team-catalog/product-control-mfe!152

### Description

Adds new Variants section (comprising of Size Label and Size Suggestions Dropdown) to the Product Information section in
Product Create/Edit flow.

### Added

-   Added new Variants section in Product Information section of Product Create/Edit flow.
-   Added Amount field to Product Information section form.

### Changed

-   Updated MultiSelectWithSearch component to work with both newly added Variants section and Product Attributes
    section
-   Updated React Hook form to combine both Product Information section form and newly added Variants section form.
-   Updated validation schema to include both Product Information section form and newly added Variants section form
    fields.
-   Moved Product Information section code logic to custom hooks.

### Deprecated

-   for soon-to-be removed features.

### Removed

-   for now removed features.

### Fixed

-   for any bug fixes.

### Security

-   in case of vulnerabilities.

### Close Issues

Closes #213 Closes #190

### Personal

Co-Authors Co-authored-by: Chandra Putta <<EMAIL>>

Merged By Dean Harmon <<EMAIL>>

Approvers Approved-by: Vaishnavi Guntaka <<EMAIL>> Approved-by: Dean Harmon <<EMAIL>>

## [1.14.0] - 2023-06-05

See merge request treez-inc/engineering/\_team-catalog/product-control-mfe!157

### Description

This MR will add the unit of measurement to the retail price product amount. It also adds a dollar sign to the price
input label.

### Added

-   UoM to pricing
-   dollar sign to input label

### Close Issues

Closes #201

### Personal

Co-Authors

Merged By Dean Harmon <<EMAIL>>

Approvers Approved-by: Jason Asavadejkajorn <<EMAIL>> Approved-by: Vaishnavi Guntaka <<EMAIL>>

## [1.13.0] - 2023-06-02

See merge request treez-inc/engineering/\_team-catalog/product-control-mfe!154

### Description

This MR covers several changes. Some of them are replacing the MUI DataGrid component with the Treez DataGrid component
and updating the Treez Component Library to the latest. Other changes are noted below.

### Added

-   Product Details now has a loading animation for the content. This will improve user experience.

### Changed

-   Updated Treez Component Library version
-   Updated MUI DataGrid component version (for purpose of using the exported types).
-   Updated product data grid to use server side data. This applies to sorting, pagination and searching.

### Removed

-   Filters are removed for now (they will be added back in the future).
-   Removed the All Products button. This was causing confusion. The full list of products will now load again when the
    search input is cleared.
-   Removed custom search input. Now using the built in search in the Treez DataGrid component.

### Fixed

-   Fixed the variant pricing DataGrid component. It broke when upgrading the Treez component library version.

### Close Issues

Closes #219

### Personal

Co-Authors Co-authored-by: Jonatan Brandao <<EMAIL>>

Merged By Dean Harmon <<EMAIL>>

Approvers Approved-by: Jason Asavadejkajorn <<EMAIL>> Approved-by: Chandra Putta <<EMAIL>>

## [1.12.1] - 2023-06-01

See merge request treez-inc/engineering/\_team-catalog/product-control-mfe!149

### Description

Description

Added cypress tests for Create-product product- variants that verifies the Product Variants form loads during the create
product workflow. Added cypress tests for Create-product product-attributes test that verifies the Attributes form loads
during the create product workflow.

## Type of change

-   [ ] Bug fix (fixes an issue)
-   [x] New feature (adds functionality)

Closes tickets https://gitlab.com/treez-inc/engineering/_team-catalog/product-control-mfe/-/issues/166
https://gitlab.com/treez-inc/engineering/_team-catalog/product-control-mfe/-/issues/165

### Personal

Co-Authors

Merged By Vaishnavi Guntaka <<EMAIL>>

Approvers Approved-by: Dean Harmon <<EMAIL>>

## [1.12.0] - 2023-05-29

See merge request treez-inc/engineering/\_team-catalog/product-control-mfe!150

### Description

## Change description

Wraps Image Thumbnails beneath the Main image into new line if more images are uploaded for Product or Variant.

## Type of change

-   [ ] Bug fix (fixes an issue)
-   [x] New feature (adds functionality)

## Related issues

#195

## Checklists

### Development

-   [ ] Application changes have been tested thoroughly
-   [ ] Automated tests covering modified code pass

### Security

-   [ ] Security impact of change has been considered
-   [ ] Code follows company security practices and guidelines

### Network

-   [ ] Changes to network configurations have been reviewed
-   [ ] Any newly exposed public endpoints or data have gone through security review

### Code review

-   [ ] Pull request has a descriptive title and context useful to a reviewer. Screenshots or screencasts are attached
        as necessary
-   [ ] "Ready for review" label attached and reviewers assigned
-   [ ] Changes have been reviewed by at least one other contributor
-   [ ] Pull request linked to task tracker where applicable

Closes #195

### Personal

Co-Authors

Merged By Chandra Putta <<EMAIL>>

Approvers Approved-by: Silju M C <<EMAIL>> Approved-by: Vaishnavi Guntaka <<EMAIL>>

## [1.11.0] - 2023-05-26

See merge request treez-inc/engineering/\_team-catalog/product-control-mfe!151

### Description

1. Removed isDraft and isActive from all the models.
2. Updated types according to the tables.
3. Set the status to be string value of Active.

### Personal

Co-Authors Co-authored-by: Vaishnavi Guntaka <<EMAIL>>

Merged By Silju M C <<EMAIL>>

Approvers Approved-by: Silju M C <<EMAIL>> Approved-by: Mike M <<EMAIL>>

## [1.10.0] - 2023-05-24

## [1.9.0] - 2023-05-23

## [1.8.2] - 2023-05-23

## [1.8.1] - 2023-05-23

## [1.8.0] - 2023-05-22

## [1.7.0] - 2023-05-18

## [1.6.0] - 2023-05-18

## [1.5.3] - 2023-05-17

## [1.5.2] - 2023-05-17




























































































