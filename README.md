## MSO UI MFE Template

product-control-mfe repository.

## Description

The basis for all MSO micro frontends to work off from. This repo minimizes configuration to get integrated and running in the MSO project and also sets standards for how all MSO MFEs should be structured.

## Installation

1. Clone this repo
2. Ask _DevOps_ to create a repo for your MFE
3. Clone the empty repo for your MFE locally.
4. Copy files from this repo over into your own MFE repo locally on your machine (do not forget any dot files (.eslintrc, .gitignore), but do exclude the `.git` directory)
5. Setup your machine for private NPM repos if this has not already been done: [GitLab NPM Private Repository Config](https://im360us.atlassian.net/wiki/spaces/development/pages/2471035030/GitLab+-+NPM+Private+Repository+Config)
6. to install packages use `yarn`

## How to setup and run locally

1. Update the name of the MFE by changing `projectName` **key** in the **singleSpaDefaults** object in the **webpack.config.js** file to the name of what you want the MFE to be called.
   ex: `projectName: 'admin-tools,`
2. Rename the `treez-mfe.tsx` file to be `treez-name-of-mfe.tsx`. So in this example: `treez-admin-tools.tsx`.
3. Follow the instructions in the following document to set up environment variables and to obtain a certificate: https://im360us.atlassian.net/wiki/spaces/development/pages/2719187344/Local+Development+Setup
4. Run this repo by using the command `yarn start`.
5. You will then need to clone the framework repo at: [https://gitlab.com/treez-inc/engineering/ui-architecture/mso-ui-core](https://gitlab.com/treez-inc/engineering/ui-architecture/mso-ui-core).
6. Run the setup for the MSO core project.
7. Add your MFE to the MSO core project by following the steps provided in the MSO UI Core readme.
8. You should then be able to see your MFE running at [http://localhost:3000](http://localhost:3000).

## Testing

### Unit Tests

Unit tests are done via Jest (and React Testing Library for React components) and run with the command `yarn jest`.

Unit Test files should live in the component folder and be named in this format: **Component.test.tsx** where **Component** is the name of the folder of the component the test lives in.

File extensions should be `.tsx` for component (React Testing Library) tests and should be `.ts` if it is a regular non-component unit test as this will not require React Testing Library (ex: util function).

### End to End Tests

End to End tests (real browser tests) are done via Cypress and are run using the commands:

```sh
yarn cypress:run:sandbox # headless

yarn cypress:open:sandbox # using cypress UI
```

## Deployment

To deploy your MFE to the different environments, please do the following:

1. Create a new local branch for your MFE
2. Update the `BUCKET_PATH` value in `gitlab-ci.yml` to the name of your MFE in this format: `mfe-name/latest` (There is plans to automate part of this in the future to be able to deploy different versions of the MFEs)
3. When your MR is approved & merged, you should see a build pipeline start in your repository if the build & tests pass.
4. In [MSO Core UI](https://gitlab.com/treez-inc/engineering/ui-architecture/mso-ui-core/-/blob/main/src/index.ejs), create an MR to update your JS import in the `index.ejs` file to end with `mfe-name/latest/treez-mfe.js`
5. Your MFE should be accessible at the sandbox environment below.

### Environment URLs

**Dev (Sandbox):** https://app.sandbox.treez.io/
**Build:** https://app.dev.treez.io/
**Production:** https://app.mso.treez.io/
