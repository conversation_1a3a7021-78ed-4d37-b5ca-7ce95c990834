import { defineConfig } from 'cypress';
import { getCypressEnv } from './cypress/support/cypressEnv';

const isKnipAnalysis = process.env.KNIP_ANALYSIS === 'true';

export default defineConfig({
    video: false,

    e2e: {
        async setupNodeEvents(on, config) {
            if (isKnipAnalysis) return config;

            const { GetParameterCommand } = await import('@aws-sdk/client-ssm');
            const { logger } = await import('./cypress/support/logging');
            const { default: ssmClient } = await import('./cypress/support/ssmClient');
            logger.debug(JSON.stringify(config, null, 2));
            const passwordCache: Record<string, string> = {};

            on('task', {
                getPassword(username) {
                    if (passwordCache[username] !== undefined) {
                        logger.debug(`Returning password from cache for ${username}`);
                        return { error: null, password: passwordCache[username] };
                    }
                    const command = new GetParameterCommand({
                        Name: `/qa/user/${username.replace('+', '_').replace('@treez.io', '')}`,
                        WithDecryption: true,
                    });
                    console.log(command);
                    return ssmClient.send(command).then(
                        (data) => {
                            const pw = data.Parameter?.Value ?? null;
                            if (pw !== null) {
                                passwordCache[username] = pw;
                            }
                            logger.debug(`Returning password from SSM for ${username}`);
                            return { error: null, password: pw };
                        },
                        (error) => {
                            logger.error(`Error getting password from SSM: ${error}`);
                            return { error, password: null };
                        },
                    );
                },
            });
        },
        env: {
            sandbox: getCypressEnv('sandbox'),
            build: getCypressEnv('build'),
        },
    },

    component: {
        devServer: {
            framework: 'react',
            bundler: 'webpack',
        },
    },

    retries: {
        // Configure retry attempts for `cypress run`
        // Default is 0
        runMode: 2,
        // Configure retry attempts for `cypress open`
        // Default is 0
        openMode: 0,
      },
    
});
