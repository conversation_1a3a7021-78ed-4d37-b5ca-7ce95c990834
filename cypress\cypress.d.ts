/// <reference types="cypress" />
import { mount } from 'cypress/react';
import { TUser } from './support/types';

declare global {
    namespace Cypress {
        interface Chainable<Subject> {
            mount: typeof mount;
            loginAs(userType: TUser): Chainable<void>;
            login(username: string, password: string): Chainable<void>;
            checkIfPricingGridIsVisible(): Chainable<void>;
            clearCache(): Chainable<Element>;
        }
    }
}
