import { applicationPaths,selectors } from '../support/constants';
import { getRandomNumber, generateRandomAlphabets } from '../support/helpers';

describe('Create Product Flow', () => {
  const randomProductNumber = getRandomNumber(1, 1000);
  let randomAlphabets = generateRandomAlphabets();
  beforeEach(() => {
    cy.clearCookies();
    cy.loginAs('admin');
    cy.visit(applicationPaths.homePage);
  });

  it('Should be able to create a product  successfully', () => {

    // Wait for page to be fully loaded and interactive
    cy.get('body').should('not.have.class', 'loading');

    cy.contains('button', 'Add Product', { timeout: 10000 }).should('be.visible').first().click({force: true});

    // Category
    cy.get(selectors.productFormProductInfoSectionSelectors.productCategoryBeverage, { timeout: 10000 }).should('be.visible').click({force: true});
        
    // Upload Image
    cy.get(selectors.productFormImagesSection.fileUpload, { timeout: 10000 }).first().selectFile('cypress/fixtures/kush.jpeg', {force: true}); // this is set to force due to the input being a hidden element
    cy.get(selectors.productFormImagesSection.imageTitle, { timeout: 10000 })
    .should('have.length.at.least', 2)
    .eq(1)
    .type(`ProductImage ${randomProductNumber}`);    
    cy.get(selectors.productFormImagesSection.imageDescription).eq(0).type(`NewProductDescription ${randomProductNumber}`);
       
    // Save the image
    cy.contains('button', 'OK', { timeout: 10000 }).click();
    cy.get(selectors.productFormImagesSection.productImage).should('exist');
        
    // Product Name
    cy.get(selectors.productFormProductInfoSectionSelectors.productName).type(`Cypress Test ${randomProductNumber} ${randomAlphabets}`);
        
    // Brand
    cy.get(selectors.productFormProductInfoSectionSelectors.productFormBrandInput);
    cy.get(selectors.productFormProductInfoSectionSelectors.productFormBrandInput).click();
    cy.get(selectors.productFormProductInfoSectionSelectors.productFormBrandInput)
    .type("Test Brand").get("#select-with-search-option-0").click();
        
    // Sub Category
    cy.get(selectors.productFormProductInfoSectionSelectors.productFormSubCatLabel).contains("Subcategory *");
    cy.get(selectors.productFormProductInfoSectionSelectors.productFormSubCategoryInput)
    .click().get('ul > li[tabindex="0"]').click();
        
    // Description
    cy.get(selectors.productFormProductInfoSectionSelectors.productFormDescriptionInput)
    .type("Cypress product create test description.");
        
    // Classification
    cy.get(selectors.productFormProductInfoSectionSelectors.productFormClassification).click().get('ul > li[data-value="Sativa"').click();
        
    // Strain
    cy.get(selectors.productFormProductInfoSectionSelectors.productFormStrainInput).type('12');
    cy.contains('button', 'Save And Next').click();
        
    // Verify Variant header is visible
    cy.contains('h6', 'SKU', { timeout: 10000 }).should('be.visible');
        
    // Create Variant
    cy.get(selectors.productFormVariantsSection.sizeAmount).type(`${randomProductNumber}`);
        
    // Enter SKU ,MG THC. MG CBD, THC PerDose , CBD PerDose & Doses
    cy.get(selectors.productFormVariantsSection.totalMgThc).scrollIntoView().should('be.visible').click().type('20');
    cy.get(selectors.productFormVariantsSection.totalMgCbd).click({ force: true }).type('20');
    cy.get(selectors.productFormVariantsSection.doses).click().type('20');
    cy.get(selectors.productFormVariantsSection.thcPerDose).click().type('20');
    cy.get(selectors.productFormVariantsSection.cbdPerDose).click().type('20');
    cy.get(selectors.productFormVariantsSection.totalFlowerweight).click().type('12');
    cy.get(selectors.productFormVariantsSection.netWeight).click().type('12');
    cy.get(selectors.productFormVariantsSection.netWeightUom).click().get('ul > li[data-value="MILLIGRAMS"').click();
        
    // Menu title
    cy.get(selectors.productFormVariantsSection.menuTitle).click().type(`Cypress Test ${randomProductNumber} Menu title ${randomAlphabets}`);
    cy.contains('Use global description')
        
    // Description is enabled
    cy.get(selectors.productFormVariantsSection.globalDescriptionCheckBox).should('exist');
    cy.contains('button', 'Save And Next').click();

    //Validate pricing screen and Skip filling out pricing        
    cy.contains('h6', 'Pricing', { timeout: 10000 }).scrollIntoView().should('be.visible');        
    cy.contains('button', 'Finish').click();
    cy.contains('button', 'Skip for now').click()
  });

  it('Should be able to select all the product successfully', () => { 
    cy.get('.MuiDataGrid-main').then((body) => {
      cy.get(selectors.productControl.checkBox).first().click();
    });
  });

  it('Should be able to bulk select and deactivate products/variants successfully', () => {
    cy.get(selectors.productControl.gridRowRole).then((rows) => {
      if (rows.length === 0) return;
  
      // Select checkboxes and expand rows using Cypress retry-ability
      for (let i = 0; i < Math.min(3, rows.length); i++) {
        cy.get(selectors.productControl.checkBox).eq(i + 1).click(); // Skip header checkbox
        cy.get(selectors.productControl.expandRowButton).eq(i).click();
      }
    });
  
    // Handle deactivation
    cy.contains('button', 'Deactivate')
    .scrollIntoView()
    .should('be.visible')
    .click();
    cy.contains('button', 'Confirm').should('be.visible').click();
  });
});