import { applicationPaths, selectors } from '../../support/constants';
import { useTestIdSelector, getRandomNumber, generateRandomAlphabets } from '../../support/helpers';

describe('Create a product and verify for Product details - Positive cases', () => {
    let randomProductNumber = getRandomNumber(1, 1000);
    let randomAlphabets;
    
    beforeEach(() => {
      randomAlphabets = generateRandomAlphabets()
      cy.clearCookies();
      cy.loginAs('admin');
      cy.visit(applicationPaths.homePage);
      
      // Wait for page to be fully loaded and interactive
      cy.get('body').should('not.have.class', 'loading');
      
      // Wait for Add Product button and ensure it's visible and clickable
      cy.contains('button', 'Add Product', { timeout: 10000 }).first().click();

      // Category - wait for element to be visible and clickable
      cy.get(selectors.productFormProductInfoSectionSelectors.productCategoryBeverage, { timeout: 15000 }).click();

      // Product Name - wait for input to be ready
      cy.get(selectors.productFormProductInfoSectionSelectors.productName, { timeout: 10000 })
        .type(`Cypress Test ${randomProductNumber} ${randomAlphabets}`);

      // Brand - ensure dropdown is loaded and interactive
      cy.get(selectors.productFormProductInfoSectionSelectors.productFormBrandInput, { timeout: 10000 }).click().type("Test Brand");
      cy.get(useTestIdSelector('autocomplete-option')).first().click();

      // Sub Category - wait for options to be loaded
      cy.get(selectors.productFormProductInfoSectionSelectors.productFormSubCatLabel).contains("Subcategory *");
      cy.get(selectors.productFormProductInfoSectionSelectors.productFormSubCategoryInput).click();
      cy.get('ul > li[tabindex="0"]').click();

      // Description
      cy.get(selectors.productFormProductInfoSectionSelectors.productFormDescriptionInput).type("Cypress product create test description.");

      // Classification
      cy.get(selectors.productFormProductInfoSectionSelectors.productFormClassification).click();
      cy.get('ul > li[data-value="Sativa"').click();

      // Strain
      cy.get(selectors.productFormProductInfoSectionSelectors.productFormStrainInput).type('12');
    });


    it('Should select an existing attribute from dropdown', () => {
      cy.get(selectors.productFormAttributesSection.productInfoForm).should('exist');

      // Internal Tags attribute
      cy.get(selectors.productFormAttributesSection.attributeDropdown).eq(4).click()
      .then(() => { cy.get(selectors.productFormAttributesSection.selectFirstOption).click(); });
    
      // Aroma Attribute
      cy.get(selectors.productFormAttributesSection.attributeDropdown).eq(5).click()
      .then(() => { cy.get(selectors.productFormAttributesSection.selectFirstOption).click(); });
    
      // Flavour attribute
      cy.get(selectors.productFormAttributesSection.flavourAttributeDropdown).click()
      .then(() => { cy.get(selectors.productFormAttributesSection.selectFirstOption).click(); });
    
      // General attribute
      cy.get(selectors.productFormAttributesSection.generalAttributeDropdown).click()
      .then(() => { cy.get(selectors.productFormAttributesSection.selectFirstOption).click(); });
    
      // Effects attribute
      cy.get(selectors.productFormAttributesSection.effectsAttributeDropdown).click()
      .then(() => { cy.get(selectors.productFormAttributesSection.selectFirstOption).click(); });
    
      // Ingredients attribute
      cy.get(selectors.productFormAttributesSection.ingredientsAttributeDropdown).click()
      .then(() => { cy.get(selectors.productFormAttributesSection.selectFirstOption).click(); });
    
      // Save and continue button
      cy.contains('button', 'Save And Next').click();
    });

    it('Should add, delete, and re-add an attribute', () => {
      cy.get(selectors.productFormAttributesSection.productInfoForm).should('exist')

      // Internal Tags attribute
      cy.get(selectors.productFormAttributesSection.attributeDropdown).eq(4).click()
      .then(() => { cy.get(selectors.productFormAttributesSection.selectFirstOption).click(); });

      // clear the selected internal attribute
      cy.get(selectors.productFormAttributesSection.cancelSingleAttribute).click();

      // re-add the internal tags attribute
      cy.get(selectors.productFormAttributesSection.attributeDropdown).eq(4).click();
      cy.get(selectors.productFormAttributesSection.selectFirstOption).should('exist');
      cy.get(selectors.productFormAttributesSection.selectFirstOption).click();

      // Save and continue button
      cy.contains('button', 'Save And Next').click();
    });

    it('Should Add Multiple Attributes in a single field and Delete all added values from attribute field all at once', () => {
      cy.get(selectors.productFormAttributesSection.productInfoForm).should('exist')

      // Select Three Internal Tags attribute
      cy.get(selectors.productFormAttributesSection.attributeDropdown).eq(4).click()
      .then(() => { cy.get(selectors.productFormAttributesSection.selectFirstOption).click(); });
      cy.get(selectors.productFormAttributesSection.attributeDropdown).eq(4).click()
      .then(() => { cy.get(selectors.productFormAttributesSection.selectSecondOption).click(); });
      cy.get(selectors.productFormAttributesSection.attributeDropdown).eq(4).click()
      .then(() => { cy.get(selectors.productFormAttributesSection.selectThirdOption).click(); });

      // clear the selected internal attribute
      cy.get(selectors.productFormAttributesSection.removeAllOptions).eq(1)
      .closest('button') 
      .should('be.visible')
      .click();

      // Save and continue button
      cy.contains('button', 'Save And Next').click();
    });
});