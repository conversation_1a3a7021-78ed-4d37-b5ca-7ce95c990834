import { applicationPaths, selectors } from '../../support/constants';
import { getRandomNumber, generateRandomAlphabets } from '../../support/helpers';

describe('Create a product and verify for Product details - Positive cases', () => {
    let randomProductNumber = getRandomNumber(1, 1000);
    let randomAlphabets: string;
    
    beforeEach(() => {
        randomAlphabets = generateRandomAlphabets()
        cy.clearCookies();
        cy.loginAs('admin');
        cy.visit(applicationPaths.homePage);
        cy.contains('button', 'Add Product', { timeout: 10000 }).first().click();

        // Category
        cy.get(selectors.productFormProductInfoSectionSelectors.productCategoryBeverage).click();

        // Product Name
        cy.get(selectors.productFormProductInfoSectionSelectors.productName, { timeout: 10000 }).type(`Cypress Test ${randomProductNumber} ${randomAlphabets}`);

        // Brand
        cy.get(selectors.productFormProductInfoSectionSelectors.productFormBrandInput);
        cy.get(selectors.productFormProductInfoSectionSelectors.productFormBrandInput).click();
        cy.get(selectors.productFormProductInfoSectionSelectors.productFormBrandInput)
            .type("Test Brand").get("#select-with-search-option-0").click();

        // Sub Category
        cy.get(selectors.productFormProductInfoSectionSelectors.productFormSubCatLabel).contains("Subcategory *");
        cy.get(selectors.productFormProductInfoSectionSelectors.productFormSubCategoryInput)
            .click().get('ul > li[tabindex="0"]').click(); 

        // Description
        cy.get(selectors.productFormProductInfoSectionSelectors.productFormDescriptionInput)
            .type("Cypress product create test description.");

        // Classification
        cy.get(selectors.productFormProductInfoSectionSelectors.productFormClassification).click()
        .get('ul > li[data-value="Sativa"').click();

        // Strain
        cy.get(selectors.productFormProductInfoSectionSelectors.productFormStrainInput).type('12');
    });

    it('Verifying the UI elements in Image screen ', () => {
        cy.get(selectors.productFormImagesSection.imageText).eq(0).should('exist');
        cy.get(selectors.productFormImagesSection.fileUploadComponent).should('exist');
        cy.get(selectors.productFormImagesSection.fileUploadComponent).should('not.be.disabled');
        cy.contains('span', 'Supported formats .jpeg .jpg .png. Max file size 10 MB.').should('exist');

        // Upload image
        cy.get(selectors.productFormImagesSection.fileUpload).first()
        .selectFile('cypress/fixtures/kush.jpeg', {force: true}); // this is set to force due to the input being a hidden element
        cy.contains('button', 'OK',  { timeout: 10000 }).click();

        // Image Edit icon hover and verify if visible
        cy.get(selectors.productFormImagesSection.productImage).trigger('mousemove');
        cy.contains('span', 'edit').invoke("show").click({force: true});
        cy.contains('button', 'OK').click();

        // Image Delete icon hover and verify if visible
        cy.get(selectors.productFormImagesSection.productImage).trigger('mousemove');
        cy.contains('span', 'delete').invoke("show").click({force: true});
        cy.get(selectors.productFormImagesSection.productRemove).contains('Please confirm that you would like to remove this image.');
        cy.contains('button', 'Yes').click();
    });

    it('Upload 1 Product image only ', () => {
        cy.get(selectors.productFormImagesSection.fileUpload).first()
        .selectFile('cypress/fixtures/kush.jpeg', {force: true}); // this is set to force due to the input being a hidden element
        cy.get(selectors.productFormImagesSection.imageTitle, { timeout: 10000 }).eq(1).type(`ProductImage ${randomProductNumber}`);
        cy.get(selectors.productFormImagesSection.imageDescription).eq(0).type(`NewProductDescription ${randomProductNumber}`);
        cy.contains('button', 'OK').click();
        cy.get(selectors.productFormImagesSection.productImage).should('exist');
        cy.contains('button', 'Save And Next').click();
    });

    it('Upload Product and variant images', () => {
        cy.get(selectors.productFormImagesSection.fileUpload).first().selectFile('cypress/fixtures/kush.jpeg', {force: true}); // this is set to force due to the input being a hidden element
        cy.contains('button', 'OK').click();
        cy.contains('button', 'Save And Next').click();

        // Verify the Total amount text field is enabled
        cy.get(selectors.productFormVariantsSection.sizeAmount, { timeout: 10000 }).type(`${randomProductNumber}`);

        // Enter in variant name
        cy.get(selectors.productFormVariantsSection.nameInput).type(`Cypress Test ${randomProductNumber}`)

        // Enter SKU ,MG THC. MG CBD, THC PerDose , CBD PerDose & Doses
        cy.get(selectors.productFormVariantsSection.variantSku).type(`${randomProductNumber} ${randomAlphabets}`);
           cy.get(selectors.productFormVariantsSection.sizeAmount) 
        .invoke('val')
        .then((sizeAmountValue) => {
          cy.get(selectors.productFormVariantsSection.totalMgThc)
            .invoke('val') 
            .should('eq', sizeAmountValue);
        });
        cy.get(selectors.productFormVariantsSection.totalMgCbd).click().type('20');
        cy.get(selectors.productFormVariantsSection.doses).click().type('20');
        cy.get(selectors.productFormVariantsSection.thcPerDose).click().type('20');
        cy.get(selectors.productFormVariantsSection.cbdPerDose).click().type('20');
        cy.get(selectors.productFormVariantsSection.totalFlowerweight).click().type('12');
        cy.get(selectors.productFormVariantsSection.netWeight).click().type('12');
        cy.get(selectors.productFormVariantsSection.netWeightUom).click({force:true});
        cy.get('ul > li[data-value="GRAMS"]').click({force:true});
        
        // Upload Variant Image
        cy.get(selectors.productFormImagesSection.fileUpload).first().selectFile('cypress/fixtures/kush.jpeg', {force: true});
        cy.get(selectors.productFormImagesSection.imageTitle).type(`ProductImage ${randomProductNumber}`);
        cy.get(selectors.productFormImagesSection.imageDescription).type(`NewProductDescription ${randomProductNumber}`);
        cy.contains('button', 'OK').click();
        cy.get(selectors.productFormImagesSection.productImage).should('exist');
        cy.contains('button', 'Save And Next').click();
    });

    it('Delete Uploaded Product image and reupload', () => {
        cy.get(selectors.productFormImagesSection.fileUpload).first().selectFile('cypress/fixtures/kush.jpeg', {force: true}); // this is set to force due to the input being a hidden element
        cy.contains('button', 'OK').click();
        cy.get(selectors.productFormImagesSection.productImage).should('exist');

        // Verify the delete icon
        cy.get(selectors.productFormImagesSection.productImage).trigger('mousemove');
        cy.contains('span', 'delete').invoke("show").click({force: true});
        
        // Verify delete dialog displayed and delete image
        cy.get(selectors.productFormImagesSection.productImage).should('exist');
        cy.get(selectors.productFormImagesSection.productRemove).contains('Please confirm that you would like to remove this image.');
        cy.contains('button', 'Yes').should('exist');
        cy.contains('button', 'Yes').click();
        cy.get(selectors.productFormImagesSection.fileUpload).first().should('exist');
        cy.get(selectors.productFormImagesSection.fileUpload).first().selectFile('cypress/fixtures/kush.jpeg', {force: true}); // this is set to force due to the input being a hidden element
        cy.contains('button', 'OK').click();
        cy.contains('button', 'Save And Next').click();
    });
});