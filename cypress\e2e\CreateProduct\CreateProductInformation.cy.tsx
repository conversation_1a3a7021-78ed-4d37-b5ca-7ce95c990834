import { applicationPaths, selectors, validationMessage } from '../../support/constants';
import { useTestIdSelector, getRandomNumber, generateRandomAlphabets } from '../../support/helpers';

describe('Create product information test cases', () => {
    let randomProductNumber = getRandomNumber(1, 1000);
    let randomAlphabets = generateRandomAlphabets();
    beforeEach(() => {
        cy.clearCookies();
        cy.loginAs('admin');
        cy.visit(applicationPaths.homePage);
        cy.wait(1000);
        cy.contains('button', 'Add Product', { timeout: 10000 }).first().click();

        // Product information header
        cy.get(selectors.productFormProductInfoSectionSelectors.productInfo).contains('Parent Product Information');

        // Product sub header
        cy.get(selectors.productFormProductInfoSectionSelectors.productInfoSubHeader).contains('Product category *');
    });

    it('Should load new Product and fill out and submit product information', () => {
        cy.get("[class*='MuiStepLabel-iconContainer']").contains("circle with a dot in the middle icon");

        // Category
        cy.get(selectors.productFormProductInfoSectionSelectors.productCategoryBeverage).should('be.visible').click();

        // Product Name
        cy.get(selectors.productFormProductInfoSectionSelectors.productName).type(`Cypress Test ${randomProductNumber} ${randomAlphabets}`);

        // Brand
        cy.get(selectors.productFormProductInfoSectionSelectors.productFormBrandInput);
        cy.get(selectors.productFormProductInfoSectionSelectors.productFormBrandInput).click();
        cy.get(selectors.productFormProductInfoSectionSelectors.productFormBrandInput)
        .type("Test Brand").get("#select-with-search-option-0").click();

        // Sub Category
        cy.get(selectors.productFormProductInfoSectionSelectors.productFormSubCatLabel).contains("Subcategory *");
        cy.get(selectors.productFormProductInfoSectionSelectors.productFormSubCategoryInput).click().get('ul > li[tabindex="0"]').click(); 

        // Description
        cy.get(selectors.productFormProductInfoSectionSelectors.productFormDescriptionInput)
        .type("Cypress product create test description.");

        // Classification
        cy.get(selectors.productFormProductInfoSectionSelectors.productFormClassification).click().get('ul > li[data-value="Sativa"').click();

        // Strain
        cy.get(selectors.productFormProductInfoSectionSelectors.productFormStrainInput).type('12');

        // Cancel button
        cy.contains('button', 'Close').should('exist');
        cy.get(selectors.productFormAttributesSection.productInfoForm).should('exist'); 

        // Internal Tags attribute
        cy.get(selectors.productFormAttributesSection.attributeDropdown).eq(4).click()
        .then(() => { cy.get(selectors.productFormAttributesSection.selectFirstOption).click(); });
        
        // Submit
        cy.contains('button', 'Save And Next').click();
        cy.get(selectors.productFormVariantsSection.sizeAmount).type(`${randomProductNumber}`);

        // Save and Continue
        cy.contains('button', 'Save And Next').click();
    });

    // TODO: https://gitlab.com/treez-inc/engineering/back-of-house/product-control-mfe/-/issues/804
    // This test breaks with bulk subtypes
    it.skip('Should verify different Product category while creating Product', () => {
        // Category
        cy.get(selectors.productFormProductInfoSectionSelectors.productCategoryBeverage).click();

        // Product Name
        cy.get(selectors.productFormProductInfoSectionSelectors.productName).type(`Cypress Test ${randomProductNumber} ${randomAlphabets}`);

        // Change button validation
        cy.get(useTestIdSelector('product-form-category-button-change')).click();
        
        // Select different category
        cy.get(useTestIdSelector('category-button-Flower')).click();

        // Product Name
        cy.get(selectors.productFormProductInfoSectionSelectors.productName).type(`Cypress Test ${randomProductNumber} ${randomAlphabets}`);

        // Brand
        cy.get(selectors.productFormProductInfoSectionSelectors.productFormBrandInput);
        cy.get(selectors.productFormProductInfoSectionSelectors.productFormBrandInput).click();
        cy.get(selectors.productFormProductInfoSectionSelectors.productFormBrandInput)
        .type("Test Brand").get("#select-with-search-option-0").click();

        // Sub Category
        cy.get(selectors.productFormProductInfoSectionSelectors.productFormSubCategoryInput).click().get('ul > li[tabindex="0"]').click(); 

        // Description
        cy.get(selectors.productFormProductInfoSectionSelectors.productFormDescriptionInput)
        .type("Cypress product create test description.");

        // Classification
        cy.get(selectors.productFormProductInfoSectionSelectors.productFormClassification).click().get('ul > li[data-value="Sativa"]').click();

        // Strain
        cy.get(selectors.productFormProductInfoSectionSelectors.productFormStrainInput).type('12');

        //Cancel button
        cy.contains('button', 'Close').should('exist');

        // Aroma Attribute
        cy.get(selectors.productFormAttributesSection.attributeDropdown).eq(5).click()
        .then(() => { cy.get(selectors.productFormAttributesSection.selectFirstOption).click(); });

        // Submit
        cy.contains('button', 'Save And Next').click();

        // Total Amount 
        cy.get(selectors.productFormVariantsSection.sizeAmount).type(`1`)

        // Save and Continue
        cy.contains('button', 'Save And Next').click();
    });

    it('Should verify negative test cases for Product Name and Brand fields', () => {
        // Category
        cy.get(selectors.productFormProductInfoSectionSelectors.productCategoryBeverage).click();

        // Button should be disabled since nothing has a value yet
        cy.contains('button', 'Save And Next').should('be.disabled');

        // Sub Category
        cy.get(selectors.productFormProductInfoSectionSelectors.productFormSubCatLabel).contains("Subcategory *");
        cy.get(selectors.productFormProductInfoSectionSelectors.productFormSubCategoryInput).click().get('ul > li[tabindex="0"]').click(); 
        cy.contains('button', 'Save And Next').click();
        
        // Brand Modal should pop up.
        cy.get(selectors.productFormVariantsSection.productFormExitModal).should('be.visible');
        cy.get(selectors.priceByProduct.noButton).click();

        // Select Brand
        cy.get(selectors.productFormProductInfoSectionSelectors.productFormBrandInput);
        cy.get(selectors.productFormProductInfoSectionSelectors.productFormBrandInput).click();
        cy.get(selectors.productFormProductInfoSectionSelectors.productFormBrandInput)
        .type("Test Brand").get("#select-with-search-option-0").click();
        cy.contains('button', 'Save And Next').click();

        // Product Name error message validation
        cy.get(selectors.productFormProductInfoSectionSelectors.productName)
        .should('contain', validationMessage.productInfo.productName);

        // Input Product name
        cy.get(selectors.productFormProductInfoSectionSelectors.productName).type(`Cypress Test ${randomProductNumber} ${randomAlphabets}`);

        // Submit
        cy.contains('button', 'Save And Next').click();

        // Save and Continue
        cy.get(selectors.productFormVariantsSection.skuTotalAmountInput).type(`${randomProductNumber}`);
        cy.contains('button', 'Save And Next').click();
    });

     // TODO: https://gitlab.com/treez-inc/engineering/back-of-house/product-control-mfe/-/issues/804
    // This test breaks with bulk subtypes
    it.skip('Should verify negative test cases for Subcategory field ', () => {
        // Category
        cy.get(selectors.productFormProductInfoSectionSelectors.productCategoryBeverage).click();

        // Button should be disabled since nothing has a value yet
        cy.contains('button', 'Save And Next').should('be.disabled');

        // Select Brand
        cy.get(selectors.productFormProductInfoSectionSelectors.productFormBrandInput);
        cy.get(selectors.productFormProductInfoSectionSelectors.productFormBrandInput).click();
        cy.get(selectors.productFormProductInfoSectionSelectors.productFormBrandInput)
        .type("Test Brand").get("#select-with-search-option-0").click();

        // Input Product name
        cy.get(selectors.productFormProductInfoSectionSelectors.productName).type(`Cypress Test ${randomProductNumber} ${randomAlphabets}`);
        cy.contains('button', 'Save And Next').click();

        // Sub Category error validation 
        cy.get(useTestIdSelector('helper-input-productsubcategoryid')).should('contain', validationMessage.productInfo.subCategory);

        // Sub Category
        cy.get(selectors.productFormProductInfoSectionSelectors.productFormSubCatLabel).contains("Subcategory *");
        cy.get(selectors.productFormProductInfoSectionSelectors.productFormSubCategoryInput).click().get('ul > li[tabindex="0"]').click(); 
        cy.contains('button', 'Save And Next').click();

        // Save and Continue
        cy.get(selectors.productFormVariantsSection.skuTotalAmountInput).type(`${randomProductNumber}`);
        cy.contains('button', 'Save And Next').click();
    });
});



