import { applicationPaths, selectors, validationMessage } from '../../support/constants';
import { useTestIdSelector, getRandomNumber, generateRandomAlphabets, generateRandomSpecialCharacters } from '../../support/helpers';

describe('Variant screen UI test cases', () => {
   let randomProductNumber: number;
   let randomVariantSize: number;
   let randomAlphabets: string;
   let randomSpecialCharacter: string;

    //Duplicated Products
    const sizeAmount = `2`;
    const sharedThc = '20';
    const sharedCbd = '20';
    const sharedDoses = '20';
    const sharedFlowerWeight = '12';
    const sharedNetWeight = '12';

    beforeEach(() => {
      randomProductNumber = getRandomNumber(1, 1000);
      randomVariantSize = getRandomNumber(1, 30);
      randomAlphabets = generateRandomAlphabets();
      randomSpecialCharacter = generateRandomSpecialCharacters(5);
      
      cy.clearCookies();
      cy.loginAs('admin');
      cy.visit(applicationPaths.homePage);
      cy.contains('button', 'Add Product', { timeout: 10000 }).first().click();

      // Category
      cy.get(selectors.productFormProductInfoSectionSelectors.productCategoryBeverage).click();

      // Product Name
      cy.get(selectors.productFormProductInfoSectionSelectors.productName, { timeout: 10000 })
      .type(`Cypress Test ${randomProductNumber} ${randomAlphabets}`);

      // Brand
      cy.get(selectors.productFormProductInfoSectionSelectors.productFormBrandInput);
      cy.get(selectors.productFormProductInfoSectionSelectors.productFormBrandInput).click();
      cy.get(selectors.productFormProductInfoSectionSelectors.productFormBrandInput)
      .type("Test Brand").get("#select-with-search-option-0").click();

      // Sub Category
      cy.get(selectors.productFormProductInfoSectionSelectors.productFormSubCatLabel).contains("Subcategory *");
      cy.get(selectors.productFormProductInfoSectionSelectors.productFormSubCategoryInput)
      .click().get('ul > li[tabindex="0"]').click(); 

      // Description
      cy.get(selectors.productFormProductInfoSectionSelectors.productFormDescriptionInput)
      .type("Cypress product create test description.");

      // Classification
      cy.get(selectors.productFormProductInfoSectionSelectors.productFormClassification).click()
      .get('ul > li[data-value="Sativa"').click();

      // Strain
      cy.get(selectors.productFormProductInfoSectionSelectors.productFormStrainInput).type('12');
      cy.contains('button', 'Save And Next').click();
   });

   it('Should verify all the UI elements in Variant screen and create 1 Variant', () => {

      // Verify Variant header is visible
      cy.contains('h6', 'SKU', { timeout: 10000 }).should('be.visible');

      // Verify the Size text field is enabled
      cy.get(selectors.productFormVariantsSection.sizeAmount).should('not.be.disabled');

      // Create 1st Variant
      cy.get(selectors.productFormVariantsSection.sizeAmount).type(`${randomProductNumber}`);

      // Verify Variant header 
      cy.get(useTestIdSelector('variant-header')).contains("New");
      cy.get(selectors.productFormVariantsSection.variantSku).type(`${randomProductNumber} ${randomAlphabets}`);
      cy.get(selectors.productFormVariantsSection.totalMgCbd).click().type('20');
      cy.get(selectors.productFormVariantsSection.doses).click().type('20');
      cy.get(selectors.productFormVariantsSection.thcPerDose).click().type('20');
      cy.get(selectors.productFormVariantsSection.cbdPerDose).click().type('20');
      cy.get(selectors.productFormVariantsSection.totalFlowerweight).click().type('12');
      cy.get(selectors.productFormVariantsSection.netWeight).click().type('12');
      cy.get(selectors.productFormVariantsSection.netWeightUom).click({force:true});
      cy.get('ul > li[data-value="GRAMS"]').click({force:true});

      // Verify Use external ID Title 
      cy.get('label').contains('Use External ID').should('be.visible');

      // Verify eCommerce header title
      cy.contains('ECOMMERCE INFORMATION').should('be.visible');

      // verify Hide from Menu button and scroll
      cy.contains('span', 'Hide from menu').should('exist')
      ;
      // Menu title
      cy.get(selectors.productFormVariantsSection.menuTitle).click().type(`Cypress Test ${randomProductNumber} Menu title`);
      cy.contains('Use global description').should('be.visible');
      // Tick the Use Global description checkbox and verify if description field is disabled,verify the text
      cy.get(selectors.productFormVariantsSection.globalDescriptionCheckBox);
      
      //Description is enabled
      cy.get(selectors.productFormVariantsSection.descriptionTextField).should('not.be.disabled');
      cy.get(selectors.productFormVariantsSection.descriptionTextField).contains('Cypress product create test description.');

      // Verify Samples and Promos
      cy.contains('SAMPLES AND PROMOS').should('be.visible');
      cy.get(selectors.productFormVariantsSection.sampleCheckBox).should('exist');
      cy.get(selectors.productFormVariantsSection.promoCheckBox).should('exist');   
      cy.get(selectors.productFormImagesSection.previousButton).should('exist');

      // Save and Close button 
      cy.get(selectors.chevronLeftIcon).should('exist');

      // Verify Save and continue button
      cy.contains('button', 'Save And Next').click();
   });

   it('Should verify creating multiple variants and delete 1 variant ', () => {
      // Verify Variant header is visible
      cy.contains('h6', 'SKU', { timeout: 10000 }).should('be.visible');
   
      // Verify the Size text field is enabled
      cy.get(selectors.productFormVariantsSection.sizeAmount).should('not.be.disabled');

      // Create 1st Variant
      cy.get(selectors.productFormVariantsSection.sizeAmount).type(`${randomProductNumber}`);

      // Enter SKU ,MG THC. MG CBD, THC PerDose , CBD PerDose & Doses
      cy.get(selectors.productFormVariantsSection.variantSku).type(`${randomProductNumber} ${randomAlphabets}`);

      //cy.get(selectors.productFormVariantsSection.totalMgThc).click().type('20');
      cy.get(selectors.productFormVariantsSection.totalMgCbd).click().type('20');
      cy.get(selectors.productFormVariantsSection.doses).click().type('20');
      cy.get(selectors.productFormVariantsSection.thcPerDose).click().type('20');
      cy.get(selectors.productFormVariantsSection.cbdPerDose).click().type('20');
      cy.get(selectors.productFormVariantsSection.totalFlowerweight).click().type('12');
      cy.get(selectors.productFormVariantsSection.netWeight).click().type('12');
      cy.get(selectors.productFormVariantsSection.netWeightUom).click({force:true});
      cy.get('ul > li[data-value="GRAMS"]').click({force:true});

      // Menu title
      cy.get(selectors.productFormVariantsSection.menuTitle).click().type(`Cypress Test ${randomProductNumber} Menu title`);
      cy.contains('Use global description').should('be.visible');

      // Description is enabled
      cy.get(selectors.productFormVariantsSection.globalDescriptionCheckBox).should('exist');
      cy.get(selectors.productFormVariantsSection.descriptionTextField).contains('Cypress product create test description.');
      
      // Create 2nd Variant
      cy.get(selectors.productFormVariantsSection.addSize1).click();
      cy.get(selectors.productFormVariantsSection.sizeAmount1).type(`${randomVariantSize}`);

      // Enter SKU ,MG THC. MG CBD, THC PerDose , CBD PerDose & Doses
      cy.get(selectors.productFormVariantsSection.variantSku2).type(`${randomProductNumber} ${randomSpecialCharacter}`);
      cy.get(selectors.productFormVariantsSection.totalMgCbd1).click().type('10');
      cy.get(selectors.productFormVariantsSection.thcPerDose1).click().type('10');
      cy.get(selectors.productFormVariantsSection.cbdPerDose1).click().type('10');
      cy.get(selectors.productFormVariantsSection.doses1).click().type('10');
      cy.get(selectors.productFormVariantsSection.totalFlowerWeight1).click().type('12');
      cy.get(selectors.productFormVariantsSection.netWeight1).click().type('12');
      cy.get(selectors.productFormVariantsSection.netWeightUom1).eq(0).click({force:true});
      cy.get('ul > li[data-value="GRAMS"]').click({force:true});

      // Menu title
      cy.get(selectors.productFormVariantsSection.menuTitle1).click().type(`Cypress Test ${randomProductNumber} Menu title`);
      cy.get(useTestIdSelector('variant-header')+' [type="button"]').contains('Delete').click();
      cy.contains('button', 'Save And Next').click();
   });

   it('Should verify Negative test cases for fields', () => {
      // Global button for just this test case
      cy.contains('button', 'Save And Next').as('nextButton');

      // Verify Variant header is visible
      cy.contains('h6', 'SKU', { timeout: 10000 }).should('be.visible');

      // Verify the Size text field is enabled
      cy.get(selectors.productFormVariantsSection.sizeAmount).should('not.be.disabled');

      // Add invalid value -Alphabets in size field
      cy.get(selectors.productFormVariantsSection.sizeAmount).type('thisIsStringTest');
      cy.get('@nextButton').click();
      cy.get(selectors.productFormVariantsSection.sizeAmount).contains(validationMessage.productInfo.enterNumberMessage);
      cy.get(selectors.productFormVariantsSection.sizeAmount).clear();

      // Add invalid value - special characters in size field
      cy.get(selectors.productFormVariantsSection.sizeAmount).type('@#$%^&&&&******');
      cy.get('@nextButton').click();
      cy.get(selectors.productFormVariantsSection.sizeAmount).contains(validationMessage.productInfo.enterNumberMessage);
      cy.get(selectors.productFormVariantsSection.sizeAmount).clear().type('{selectall}{backspace}');

      // Click variant button 
      cy.get(selectors.productFormVariantsSection.sizeAmount).type("10");

      //Validate Total MG CBD field
      cy.get(selectors.productFormVariantsSection.totalMgCbd).click().type('.');
      cy.get('@nextButton').click();
      cy.get(selectors.productFormVariantsSection.totalMgCbd).contains(validationMessage.productInfo.mustBeANumberMessage);
      cy.get(selectors.productFormVariantsSection.totalMgCbd).clear().click().type('aabbccddeeffgg');
      cy.get('@nextButton').click();
      cy.get(selectors.productFormVariantsSection.totalMgCbd).contains(validationMessage.productInfo.mustBeANumberMessage);
      cy.get(selectors.productFormVariantsSection.totalMgCbd).clear().click().type('!@##$$%%^^&&**()');
      cy.get('@nextButton').click();
      cy.get(selectors.productFormVariantsSection.totalMgCbd).contains(validationMessage.productInfo.mustBeANumberMessage);
      cy.get(selectors.productFormVariantsSection.totalMgCbd).clear();
      cy.get(selectors.productFormVariantsSection.totalMgCbd).click().type('20');

      //Validate THC Per Dose field
      cy.get(selectors.productFormVariantsSection.thcPerDose).click().type('.');
      cy.get('@nextButton').click();
      cy.get(selectors.productFormVariantsSection.thcPerDose).contains(validationMessage.productInfo.mustBeANumberMessage);
      cy.get(selectors.productFormVariantsSection.thcPerDose).click().type('asdgh123');
      cy.get('@nextButton').click();
      cy.get(selectors.productFormVariantsSection.thcPerDose).contains(validationMessage.productInfo.mustBeANumberMessage);
      cy.get(selectors.productFormVariantsSection.thcPerDose).click().type('!@##$$%%^^&&*09&&^^%');
      cy.get('@nextButton').click();
      cy.get(selectors.productFormVariantsSection.thcPerDose).contains(validationMessage.productInfo.mustBeANumberMessage);
      cy.get(selectors.productFormVariantsSection.thcPerDose).clear();
      cy.get(selectors.productFormVariantsSection.thcPerDose).click().type('20');

      //Validate CBD Per Dose field
      cy.get(selectors.productFormVariantsSection.cbdPerDose).click().type('.');
      cy.get('@nextButton').click();
      cy.get(selectors.productFormVariantsSection.cbdPerDose).contains(validationMessage.productInfo.mustBeANumberMessage);
      cy.get(selectors.productFormVariantsSection.cbdPerDose).clear().click().type('aabbccdd1234');
      cy.get('@nextButton').click();
      cy.get(selectors.productFormVariantsSection.cbdPerDose).contains(validationMessage.productInfo.mustBeANumberMessage);
      cy.get(selectors.productFormVariantsSection.cbdPerDose).clear().click().type('!@#$%^&*9**&');
      cy.get('@nextButton').click();
      cy.get(selectors.productFormVariantsSection.cbdPerDose).contains(validationMessage.productInfo.mustBeANumberMessage);
      cy.get(selectors.productFormVariantsSection.cbdPerDose).clear();
      cy.get(selectors.productFormVariantsSection.cbdPerDose).click().type('20');

      //Validate Doses field
      cy.get(selectors.productFormVariantsSection.doses).click().type('.');
      cy.get('@nextButton').click();
      cy.get(selectors.productFormVariantsSection.doses).contains(validationMessage.productInfo.mustBeANumberMessage);
      cy.get(selectors.productFormVariantsSection.doses).clear().click().type('aabbssgfgghjjjj');
      cy.get('@nextButton').click();
      cy.get(selectors.productFormVariantsSection.doses).contains(validationMessage.productInfo.mustBeANumberMessage);
      cy.get(selectors.productFormVariantsSection.doses).clear().click().type('!@#$#%%^^&&*&&*(&^^%^^$%$%');
      cy.get('@nextButton').click();
      cy.get(selectors.productFormVariantsSection.doses).contains(validationMessage.productInfo.mustBeANumberMessage);
      cy.get(selectors.productFormVariantsSection.doses).clear();
      cy.get(selectors.productFormVariantsSection.doses).click().type('20');

      // Next
      cy.get('@nextButton').click();
   });

   it('Should add a new variant and verify delete functionality before saving the variant', () => {
      // Verify Variant header is visible
      cy.contains('h6', 'SKU', { timeout: 10000 }).should('be.visible');

      // Verify the Size text field is enabled
      cy.get(selectors.productFormVariantsSection.sizeAmount).should('not.be.disabled');
      
      // Create 1st Variant
      cy.get(selectors.productFormVariantsSection.sizeAmount).type(`${randomProductNumber}`);
         
      // Verify Variant header 
      cy.get(useTestIdSelector('variant-header')).contains("New");

      cy.get(selectors.productFormVariantsSection.variantSku).type(`${randomProductNumber} ${randomAlphabets}`);
      cy.get(selectors.productFormVariantsSection.totalMgCbd).click().type('20');
      cy.get(selectors.productFormVariantsSection.doses).click().type('20');
      cy.get(selectors.productFormVariantsSection.thcPerDose).click().type('20');
      cy.get(selectors.productFormVariantsSection.cbdPerDose).click().type('20');
      cy.get(selectors.productFormVariantsSection.totalFlowerweight).click().type('12');
      cy.get(selectors.productFormVariantsSection.netWeight).click().type('12');
      cy.get(selectors.productFormVariantsSection.netWeightUom).click({force:true});
      cy.get('ul > li[data-value="GRAMS"]').click({force:true});

      // Verify Use external ID Title 
      cy.get('label').contains('Use External ID').should('be.visible');
      
      // Verify eCommerce header title
      cy.contains('ECOMMERCE INFORMATION').should('be.visible');

      // verify Hide from Menu button and scroll
      cy.contains('span', 'Hide from menu').should('exist');

      // Menu title
      cy.get(selectors.productFormVariantsSection.menuTitle).click().type(`Cypress Test ${randomProductNumber} Menu title`);
      cy.contains('Use global description').should('be.visible');

      // Tick the Use Global description checkbox and verify if description field is disabled,verify the text
      cy.get(selectors.productFormVariantsSection.globalDescriptionCheckBox);

      //Description is enabled
      cy.get(selectors.productFormVariantsSection.descriptionTextField).should('not.be.disabled');
      cy.get(selectors.productFormVariantsSection.descriptionTextField).contains('Cypress product create test description.');

      // Verify Samples and Promos
      cy.contains('SAMPLES AND PROMOS').should('be.visible');
      cy.get(selectors.productFormVariantsSection.sampleCheckBox).should('exist');
      cy.get(selectors.productFormVariantsSection.promoCheckBox).should('exist');   
      cy.get(selectors.productFormImagesSection.previousButton).should('exist');

      // Verify Delete button functionality
      cy.get(useTestIdSelector('variant-header')+' [type="button"]').contains('Delete').click();

      // Verify Save and continue button
      cy.contains('button', 'Save And Next').click();
   });

   it('Verify Duplicate variant message', () => {
      // Verify Variant header is visible
      cy.contains('h6', 'SKU', { timeout: 10000 }).should('be.visible');

      // Verify Variant header 
      cy.get(useTestIdSelector('variant-header')).contains("New");

      // Create 1st Variant
      cy.get(selectors.productFormVariantsSection.sizeAmount).type(sizeAmount);
      cy.get(selectors.productFormVariantsSection.totalMgCbd).type(sharedCbd);
      cy.get(selectors.productFormVariantsSection.doses).type(sharedDoses);
      cy.get(selectors.productFormVariantsSection.thcPerDose).type(sharedThc);
      cy.get(selectors.productFormVariantsSection.cbdPerDose).type(sharedCbd);
      cy.get(selectors.productFormVariantsSection.totalFlowerweight).type(sharedFlowerWeight);
      cy.get(selectors.productFormVariantsSection.netWeight).type(sharedNetWeight);
      cy.get(selectors.productFormVariantsSection.netWeightUom).click({ force: true });
      cy.get('ul > li[data-value="GRAMS"]').click({ force: true });

      // Verify Use external ID Title 
      cy.get('label').contains('Use External ID').should('be.visible');

      // Verify eCommerce header title
      cy.contains('ECOMMERCE INFORMATION').should('be.visible');

      // Menu title
      cy.contains('Use global description').should('be.visible');
      // Tick the Use Global description checkbox and verify if description field is disabled,verify the text
      cy.get(selectors.productFormVariantsSection.globalDescriptionCheckBox);

      //Description is enabled
      cy.get(selectors.productFormVariantsSection.descriptionTextField).should('not.be.disabled');
      cy.get(selectors.productFormVariantsSection.descriptionTextField).contains('Cypress product create test description.');

      // Verify Samples and Promos
      cy.contains('SAMPLES AND PROMOS').should('be.visible');
      cy.get(selectors.productFormVariantsSection.sampleCheckBox).should('exist');
      cy.get(selectors.productFormVariantsSection.promoCheckBox).should('exist');   
      cy.get(selectors.productFormImagesSection.previousButton).should('exist');

      // Create 2nd Variant
      cy.get(selectors.productFormVariantsSection.addSize1).click();
      cy.get(selectors.productFormVariantsSection.sizeAmount1).type(sizeAmount);
      cy.get(selectors.productFormVariantsSection.totalMgCbd1).type(sharedCbd);
      cy.get(selectors.productFormVariantsSection.doses1).click().type(sharedDoses);
      cy.get(selectors.productFormVariantsSection.thcPerDose1).type(sharedThc);
      cy.get(selectors.productFormVariantsSection.cbdPerDose1).type(sharedCbd);
      cy.get(selectors.productFormVariantsSection.totalFlowerWeight1).type(sharedFlowerWeight);
      cy.get(selectors.productFormVariantsSection.netWeight1).type(sharedNetWeight);
      cy.get(selectors.productFormVariantsSection.netWeightUom1).click({ force: true });
      cy.get('ul > li[data-value="GRAMS"]').click({ force: true });

      // Verify Use external ID Title 
      cy.get('label').contains('Use External ID').should('be.visible');

      // Verify eCommerce header title
      cy.contains('ECOMMERCE INFORMATION').should('be.visible');

      // Menu title
      cy.contains('Use global description').should('be.visible');
      // Tick the Use Global description checkbox and verify if description field is disabled,verify the text
      cy.get(selectors.productFormVariantsSection.globalDescriptionCheckBox);

      //Description is enabled
      cy.get(selectors.productFormVariantsSection.descriptionTextField1).should('not.be.disabled');
      cy.get(selectors.productFormVariantsSection.descriptionTextField1).contains('Cypress product create test description.');

      // Verify Samples and Promos
      cy.contains('SAMPLES AND PROMOS').should('be.visible');
      cy.get(selectors.productFormVariantsSection.sampleCheckBox).should('exist');
      cy.get(selectors.productFormVariantsSection.promoCheckBox).should('exist');   
      cy.get(selectors.productFormImagesSection.previousButton).should('exist');

      // Next 
      cy.contains('button', 'Save And Next').click();

      // Error should be displayed
      cy.get(selectors.snackBar).contains("Duplicate variant found")
   });
});

describe('Activate the deactivated Variant', () => {
   let randomProductNumber;
   let randomVariantSize;
   let randomAlphabets;

    beforeEach(() => {
      randomProductNumber = getRandomNumber(1, 1000);
      randomVariantSize = getRandomNumber(1, 30);
      randomAlphabets = generateRandomAlphabets();
      cy.clearCookies();
      cy.loginAs('admin');
      cy.visit(applicationPaths.homePage);
      cy.contains('button', 'Add Product', { timeout: 10000 }).first().click();
      // Category
      cy.get(selectors.productFormProductInfoSectionSelectors.productCategoryBeverage, { timeout: 10000 }).click();
      
      // Product Name
      cy.get(selectors.productFormProductInfoSectionSelectors.productName, { timeout: 10000 }).type(`Cypress Test ${randomProductNumber} ${randomAlphabets}`);

      // Brand
      cy.get(selectors.productFormProductInfoSectionSelectors.productFormBrandInput);
      cy.get(selectors.productFormProductInfoSectionSelectors.productFormBrandInput).click();
      cy.get(selectors.productFormProductInfoSectionSelectors.productFormBrandInput)
      .type("Test Brand").get("#select-with-search-option-0").click();

      // Sub Category
      cy.get(selectors.productFormProductInfoSectionSelectors.productFormSubCatLabel).contains("Subcategory *");
      cy.get(selectors.productFormProductInfoSectionSelectors.productFormSubCategoryInput)
      .click().get('ul > li[tabindex="0"]').click();

      // Description
      cy.get(selectors.productFormProductInfoSectionSelectors.productFormDescriptionInput)
      .type("Cypress product create test description.");

      // Strain
      cy.get(selectors.productFormProductInfoSectionSelectors.productFormStrainInput).type('12');
      cy.contains('button', 'Save And Next').click({force: true});

      // Verify Variant header is visible
      cy.wait(1000);
      cy.contains('h6', 'SKU', { timeout: 10000 }).should('be.visible');

      // Verify the Size text field is enabled
      cy.get(selectors.productFormVariantsSection.sizeAmount).should('not.be.disabled');
      cy.get(selectors.productFormVariantsSection.sizeAmount).type(`${randomProductNumber}`);
      cy.get(useTestIdSelector('variant-header')).contains("New");

      // Verify Samples and Promos
      cy.contains('SAMPLES AND PROMOS').scrollIntoView().should('be.visible');;
      cy.get(selectors.productFormVariantsSection.sampleCheckBox).should('exist');
      cy.get(selectors.productFormVariantsSection.promoCheckBox).should('exist');   

      // Return to Product page
      cy.contains('button', 'Save And Next').click();

      // Skip filling out a price
      cy.contains('button', 'Finish').click();
      cy.contains('button', 'Skip for now').click();

      // Verify sample and promo labels in product control home page
      const searchTerm1 = `Cypress Test ${randomProductNumber} ${randomAlphabets}`;
      cy.get('input[placeholder="Search..."]').type('@#$@!#$%').type(searchTerm1);
      cy.get('input[placeholder="Search..."]').type('@#$@!#$%').type('{enter}');

      // Select the Newly created product
      cy.get(selectors.productControl.productName).first().click();

      // Select next to go to the SKU tab
      cy.contains('button', 'Next').click();
   });

   it('Activate Newly created SKU and save', () => {
      // Deactivated should be default and change to Activate
      cy.contains('button', 'Deactivate').click();
      cy.contains('Confirm Deactivation').should('be.visible');
      cy.contains('button', 'OK').click();
      cy.contains('Deactivated').should('exist');

      // save change
      cy.get(selectors.saveBtn).click()

      // Return to Product page
      cy.contains('button', 'Next').click();

      // Skip filling out a price
      cy.contains('button', 'Finish').click();
      cy.contains('button', 'Skip for now').click();
   });

   // TODO: https://gitlab.com/treez-inc/engineering/back-of-house/product-control-mfe/-/issues/804
   /** 
    *  This test breaks with the following error:
    *  AssertionError: Timed out retrying after 4000ms: expected '<div.MuiBox-root.css-1wx29tt>' to be 'visible'
    *
    *  This element `<div.MuiBox-root.css-1wx29tt>` is not visible because its content is being clipped by one of its parent elements, which has a CSS property *  of overflow: `hidden`, `scroll` or `auto`
    *
    *  Because this error occurred during a `before each` hook we are skipping the remaining tests in the current suite: `Activate the deactivated Va...`
    */
   it.skip('Select a Newly created SKU to Activate and then switch back to Deactivated', () => {
      // Deactivated should be default and change to Activate
      cy.contains('button', 'Deactivate').click();
      cy.contains('Confirm Deactivation').should('be.visible');
      cy.contains('button', 'OK').click();
      cy.contains('Deactivated').should('exist');

      // Select as Active and change back to Deactivated
      cy.contains('button', 'Activate').click();
      cy.contains('Confirm Activation').should('be.visible');
      cy.contains('button', 'OK').click();
      cy.contains('Activate').should('not.exist');

      // Return to Product page
      cy.contains('button', 'Next').click();

      // Skip filling out a price
      cy.contains('button', 'Finish').click();
      cy.contains('button', 'Skip for now').click();
   });
});
