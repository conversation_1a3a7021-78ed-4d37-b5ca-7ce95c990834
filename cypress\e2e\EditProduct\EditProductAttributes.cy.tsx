import { applicationPaths, selectors } from '../../support/constants';

describe('Edit product attribute', () => {
  beforeEach(() => {
    cy.clearCookies();
      cy.loginAs('admin');
      cy.visit(applicationPaths.homePage);
      cy.get(selectors.productControl.productName, { timeout: 10000 }).first().click({ force: true });
  });

  it('Should load the Product Attributes container with all fields', () => {
      cy.get(selectors.attributeTitleAttributes, { timeout: 10000 }).contains('Attributes').should('exist');
      cy.get(selectors.productFormAttributesSection.productAttributeCategory).first().should('exist');

      //Save and Close button 
      cy.get(selectors.chevronLeftIcon).should('exist');

      //Save and continue button
      cy.contains('button', 'Next').should('exist');
      cy.contains('button', 'Save').should('be.disabled');
  });

  it('Should add new attributes from options listed', () => {
      cy.get(selectors.productFormAttributesSection.productAttributeCategory, { timeout: 10000 }).first().should('exist');

      // Aroma attribute
      cy.get(selectors.productFormAttributesSection.attributeDropdown).eq(5).click()
      .then(() => { cy.get(selectors.productFormAttributesSection.selectFirstOption).click(); });

      // Effects Attribute
      cy.get(selectors.productFormAttributesSection.effectsAttributeDropdown).click()
      .then(() => { 
        cy.get(selectors.productFormAttributesSection.selectFirstOption).click();
        cy.get(selectors.productFormAttributesSection.effectsAttributeDropdown).click();
       });

      //Save and continue button
      cy.contains('button', 'Save').click();
   });

  it('Should remove the attributes listed', () => {
    cy.get(selectors.attributeTitleAttributes, { timeout: 10000 }).contains('Attributes').should('exist');
    cy.get(selectors.productFormAttributesSection.productAttributeCategory).first().should('exist');

    // Aroma attribute
    cy.get(selectors.productFormAttributesSection.attributeDropdown).eq(5).click()
    .then(() => {
      cy.get(selectors.productFormAttributesSection.attributeDropdown).eq(5).click().type('{Cmd}', { release: false });
      cy.get(selectors.productFormAttributesSection.selectFirstOption).click();
      cy.get(selectors.productFormAttributesSection.selectSecondOption).click();
      cy.get(selectors.productFormAttributesSection.attributeDropdown).eq(5).click();
    });

    //Save and continue button
    cy.contains('button', 'Save').click();
  });
});
