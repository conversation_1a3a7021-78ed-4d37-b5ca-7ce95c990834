import { applicationPaths, selectors } from '../../support/constants';
import { getRandomNumber, generateRandomAlphabets } from '../../support/helpers';

describe('Edit product details', () => { // no more Product detail screen 
    let randomAlphabets = generateRandomAlphabets();
    beforeEach(() => {
        cy.clearCookies();
        cy.loginAs('admin');
        cy.visit(applicationPaths.homePage);
    });

    it('Should load the Linear Stepper section with all fields', () => {
        cy.get(selectors.productControl.productName, { timeout: 10000 }).first().click({ force: true });
        cy.get(selectors.linearStepper).should('exist');
        cy.get(selectors.linearStepper).should('contain', 'Product Information').should('exist');
        cy.get(selectors.linearStepper).should('contain', 'SKU').should('exist');
        cy.get(selectors.linearStepper).should('contain', 'Pricing').should('exist');
    });

    // TODO: https://gitlab.com/treez-inc/engineering/back-of-house/product-control-mfe/-/issues/804
    // This test fails with the following error:  AssertionError: Timed out retrying after 4000ms: Expected to find content: 'Finish' within the selector: 'button' but never did.
    it.skip('Should verify the updated product name in product detail screen', () => {
        // Edit product button
        cy.get(selectors.productControl.productName).first().click({ force: true });
        cy.get(selectors.productFormProductInfoSectionSelectors.productInfo).contains('Parent Product Information');

        // Clear the existing product name and update the new one
        const randomProductNumber = getRandomNumber(1, 1000);
        const productNameUpdate = `Cypress Test Product Name Update ${randomProductNumber} ${randomAlphabets}`;
        cy.get(`${selectors.productFormProductInfoSectionSelectors.productName} input`).clear().type(productNameUpdate);

        // Save the new change and return back to product control home page  
        cy.contains('button', 'Next').click();
        cy.get(selectors.modalContainerProductInfo).should('be.visible');
        cy.contains('button', 'Yes').should('be.visible').click();
        cy.contains('button', 'Next').click();

        //Finish without adding a price
        cy.contains('button', 'Finish').click();  
        cy.contains('button', 'Skip for now').click(); 

        // Search product using search text box
        cy.get(selectors.productControl.searchProduct).type(productNameUpdate);
        cy.contains(selectors.productControl.productName, productNameUpdate).should('exist').click({force:true});
    });
});