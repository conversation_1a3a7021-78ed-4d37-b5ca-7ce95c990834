import { applicationPaths, selectors } from '../../support/constants';
import { generateRandomAlphabets, getRandomNumber, useTestIdSelector } from '../../support/helpers';

describe('Edit Product Images Page', () => { 
  beforeEach(() => {
        cy.clearCookies();
        cy.loginAs('admin');
        cy.visit(applicationPaths.homePage);

        // Wait for page to be fully loaded and interactive
        cy.get('body').should('not.have.class', 'loading');
  });

  it('Create a product for the testing the images', () => {
    let randomProductNumber = getRandomNumber(1, 1000);
    let randomAlphabets = generateRandomAlphabets();
    let productSize: any;
    let productSubCategoryList: string[] = [];
    let productSubCategory: any;

    cy.contains('button', 'Add Product', { timeout: 10000 }).first().click();
      
    // Category
    cy.get(selectors.productFormProductInfoSectionSelectors.productCategoryMerch, { timeout: 10000 }).click();
  
    // Product Name
    cy.get(selectors.productFormProductInfoSectionSelectors.productName, { timeout: 10000 })
    .type(`Cypress Merch Product Test ${randomProductNumber} ${randomAlphabets}`);
  
    // Brand
    cy.get(selectors.productFormProductInfoSectionSelectors.productFormBrandInput).click();
    cy.get(selectors.productFormProductInfoSectionSelectors.productFormBrandInput)
    .type("Test Brand").get(useTestIdSelector('autocomplete-option')).eq(0).click();
  
    // Sub Category
    cy.get(selectors.productFormProductInfoSectionSelectors.productFormSubCatLabel).contains("Subcategory *");
    cy.get(selectors.productFormProductInfoSectionSelectors.productFormSubCategoryInput).then((prodSubCat) => {
      productSubCategory = prodSubCat;
    });
    cy.get(selectors.productFormProductInfoSectionSelectors.productFormSubCategoryInput)
    .click().get('ul > li[tabindex="0"]').click();
    productSubCategoryList.push(productSubCategory);
  
    // Description
    cy.get(selectors.productFormProductInfoSectionSelectors.productFormDescriptionInput)
    .type("Cypress product create test description.");
    cy.contains('button', 'Save And Next').click();
  
    // Create SKU
    cy.contains('h6', 'SKU', { timeout: 10000 }).should('be.visible');
    cy.get(selectors.productFormVariantsSection.sizeMultiSelect).then((prodSize) => {
      productSize = prodSize;
    });
    cy.get(selectors.productFormVariantsSection.sizeMultiSelect).click().get('ul > li[tabindex="0"]').click();
    cy.get(selectors.productFormVariantsSection.nameInput).type('Example Name');
    cy.get(selectors.productFormVariantsSection.labelPrinter).type('Print Label');
  
    // Menu title
    cy.get(selectors.productFormVariantsSection.menuTitle).click().type(`Cypress Test ${randomProductNumber} Menu title`);
    cy.contains('button', 'Save And Next').click();
  
    // Validate pricing screen
    cy.contains('h6', 'Pricing').scrollIntoView().should('be.visible');
    cy.contains('button', 'Finish').click();
    cy.get(selectors.modalContainerProductInfo).contains('button', 'Skip for now').click();
  });

  it('Should verify the Edit Images UI and upload a file', () => {
    cy.get(selectors.productControl.productName, { timeout: 10000 }).first().click({ force: true });

    cy.get(selectors.chevronLeftIcon).should('exist');
    cy.get(selectors.productFormImagesSection.fileUpload, { timeout: 10000 }).first().should('exist');
    
    //Done button
    cy.contains('button', 'Next').should('exist');
    cy.get(selectors.productFormImagesSection.fileUpload).first()
    .selectFile('cypress/fixtures/kush.jpeg', {force: true}); // this is set to force due to the input being a hidden element

    // Save the image
    cy.contains('button', 'OK', { timeout: 10000 }).click();
    cy.get(selectors.productFormImagesSection.productImage).should('exist');
  });

  it('Verify clicking Save and Close button Navigates to Product control page', () => {
    cy.get(selectors.productControl.productName, { timeout: 10000 }).first().click({ force: true });

    cy.get(selectors.productFormImagesSection.fileUpload, { timeout: 10000 }).first()
    .selectFile('cypress/fixtures/kush.jpeg', {force: true}); // this is set to force due to the input being a hidden element
    cy.contains('button', 'OK', { timeout: 10000 }).click();
    cy.get(selectors.productFormImagesSection.productImage).should('exist');
    cy.contains('button', 'Next').should('exist').click();
    cy.get(selectors.modalContainerProductInfo).should('be.visible');
    cy.contains('button', 'Yes').should('be.visible').click();
    cy.contains('button', 'Next').should('exist');
  });

  it('Verify adding image to variant UI screen', () => { 
    cy.get(selectors.productControl.productName, { timeout: 10000 }).first().click({ force: true });

    cy.contains('button', 'Next', { timeout: 10000 }).click();
    cy.wait(1000);
    cy.get(selectors.productFormImagesSection.globalImageCheckbox).scrollIntoView().click({force: true});
    cy.get(selectors.productFormImagesSection.fileUpload).first()
    .selectFile('cypress/fixtures/kush.jpeg', {force: true}); // this is set to force due to the input being a hidden element
    cy.wait(1000);
    cy.contains('button', 'OK', { timeout: 10000 }).click();
    cy.get(selectors.productFormImagesSection.productImage).should('exist');
  });

  it('Should Edit the Uploaded image', () => {
    cy.get(selectors.productControl.productName, { timeout: 10000 }).first().click({ force: true });

    cy.get(selectors.productFormImagesSection.fileUpload, { timeout: 10000 }).first()
    .selectFile('cypress/fixtures/kush.jpeg', {force: true}); // this is set to force due to the input being a hidden element
    cy.contains('button', 'OK', { timeout: 10000 }).click();
    cy.get(selectors.productFormImagesSection.productImage).should('exist');

    //Image Edit icon hover and verify editing
    cy.get(selectors.productFormImagesSection.productImage)
      .should('have.length.gte', 1) // Ensure there is at least one element
      .then(($elements) => {
        if ($elements.length === 1) {
          // If there is a single element, click it
          cy.wrap($elements).click();
        } else {
          // If there are multiple elements, click the first one (eq(0))
          cy.get(selectors.productFormImagesSection.productImage).eq(0).click();
        }
    });

    cy.contains('span', 'edit').invoke("show").click({force: true});
    cy.contains('button', 'OK').click();
    cy.get(selectors.productFormImagesSection.productImage).should('exist');
    cy.contains('button', 'Next').click();

    // Save changed modal
    cy.contains('button', 'Yes').should('be.visible').click();
  });

  it('Delete Uploaded Product image', () => {
    cy.get(selectors.productControl.productName, { timeout: 10000 }).first().click({ force: true });

    cy.get(selectors.productFormImagesSection.fileUpload, { timeout: 10000 }).first()
    .selectFile('cypress/fixtures/kush.jpeg', {force: true}); // this is set to force due to the input being a hidden element
    cy.contains('button', 'OK', { timeout: 10000 }).click();
    cy.get(selectors.productFormImagesSection.productImage, { timeout: 10000 }).should('exist');

    //Verify the delete icon
    cy.get(selectors.productFormImagesSection.productImage, { timeout: 10000 })
      .should('have.length.gte', 1) // Ensure there is at least one element
      .then(($elements) => {
        if ($elements.length === 1) {
          // If there is a single element, click it
          cy.wrap($elements).click();
        } else {
          // If there are multiple elements, click the first one (eq(0))
          cy.get(selectors.productFormImagesSection.productImage, { timeout: 10000 }).eq(0).click();
        }
    });
    cy.contains('span', 'delete', { timeout: 10000 }).invoke("show").click({force: true});

    //Verify delete dialog displayed and delete image
    cy.contains('span', 'delete').invoke("show").click({force: true});
    cy.get(selectors.productFormImagesSection.productImage, { timeout: 10000 }).should('exist');
    cy.get(selectors.productFormImagesSection.productRemove, { timeout: 10000 }).contains('Please confirm that you would like to remove this image.');
    cy.contains('button', 'Yes').should('exist');
    cy.contains('button', 'Yes').click();
    cy.get(selectors.productFormImagesSection.fileUpload, { timeout: 10000 }).first().should('exist');
    cy.contains('button', 'Next').click();

    // Save changed modal
    cy.contains('button', 'Yes').should('be.visible').click();
  });
});