import { applicationPaths, selectors, validationMessage } from '../../support/constants';
import { useTestIdSelector, getRandomNumber, generateRandomAlphabets} from '../../support/helpers';
describe('Edit a product information', () => {
  let randomProductNumber = getRandomNumber(1, 1000);
  let productName = `Cypress Test ${randomProductNumber}`;
  let randomAlphabets = generateRandomAlphabets();

  beforeEach(() => {
    cy.clearCookies();
    cy.loginAs('admin');
    cy.visit(applicationPaths.homePage);
    cy.contains('button', 'Add Product', { timeout: 10000 }).first().click();
      
    // Product information header
    cy.get(selectors.productFormProductInfoSectionSelectors.productInfo, { timeout: 10000 }).contains('Parent Product Information');
      
    // Select the Category
    cy.get(selectors.productFormProductInfoSectionSelectors.productCategoryBeverage, { timeout: 10000 }).click({force: true});
      
    // Enter the Product Name
    cy.get(selectors.productFormProductInfoSectionSelectors.productName, { timeout: 10000 }).type(productName);
      
    // Brand
    cy.get(selectors.productFormProductInfoSectionSelectors.productFormBrandInput);
    cy.get(selectors.productFormProductInfoSectionSelectors.productFormBrandInput).click();
    cy.get(selectors.productFormProductInfoSectionSelectors.productFormBrandInput)
    .type("Test Brand").get(useTestIdSelector('autocomplete-option')).eq(0).click();
      
    // Sub Category
    cy.get(selectors.productFormProductInfoSectionSelectors.productFormSubCatLabel).contains("Subcategory *");
    cy.get(selectors.productFormProductInfoSectionSelectors.productFormSubCategoryInput).click().get('ul > li[tabindex="0"]').click(); 
      
    // Description
    cy.get(selectors.productFormProductInfoSectionSelectors.productFormDescriptionInput)
    .type("Cypress product create test description.");
      
    // Classification
    cy.get(selectors.productFormProductInfoSectionSelectors.productFormClassification).click().get('ul > li[data-value="Sativa"').click();
      
    // Strain
    cy.get(selectors.productFormProductInfoSectionSelectors.productFormStrainInput).type('12');
    cy.contains('button', 'Save And Next').click();
      
    // Sku
    cy.get(selectors.productFormVariantsSection.sizeAmount, { timeout: 10000 }).should('not.be.disabled');
    cy.get(selectors.productFormVariantsSection.sizeAmount).type(`${randomProductNumber}`);
    cy.contains('button', 'Save And Next').click();
    cy.contains('h6', 'Pricing', { timeout: 10000 }).scrollIntoView().should('be.visible');
      
    // Skip filling out pricing
    cy.contains('button', 'Finish').click();
    cy.contains('button', 'Skip for now').click()
  });

  it('Should update all fields within the product information form and check for a 200 response from PATCH /product', () => {
    // Search product using search text box
    cy.get('input[placeholder="Search..."]').type(productName);
    cy.contains(selectors.productControl.productName, productName, { timeout: 10000 }).should('exist').click({force:true});
    cy.get(selectors.productFormProductInfoSectionSelectors.productHeading, { timeout: 10000 }).should('contain', productName);
    cy.get(selectors.productFormProductInfoSectionSelectors.productInfo).contains('Parent Product Information');
      
    // Edit product name
    cy.get(`${selectors.productFormProductInfoSectionSelectors.productName} input`).clear().type(`Cypress Test Name Update ${randomProductNumber} ${randomAlphabets}`);
      
    // Edit brand details and add a new brand
    cy.get(selectors.productFormProductInfoSectionSelectors.productFormBrandInput).trigger('mouseover');;
    cy.get(selectors.productFormProductInfoSectionSelectors.productFormBrandInput).click().type(`Cypress New Brand ${randomProductNumber} ${randomAlphabets}`);
      
    // Edit sub category
    cy.get(selectors.productFormProductInfoSectionSelectors.productFormSubCatLabel).contains("Subcategory *");
    cy.get(selectors.productFormProductInfoSectionSelectors.productFormSubCategoryInput).click();
    cy.contains('li', 'Coffee').should('be.visible').click();
      
    // Edit description
    cy.get(selectors.productFormProductInfoSectionSelectors.productFormDescriptionInput)
    .type("Cypress edit product test description.");
      
    // Edit Classification
    cy.get(selectors.productFormProductInfoSectionSelectors.productFormClassification).click()
    .get('ul > li[data-value="Indica"]').click();
      
    // Strain
    cy.get(selectors.productFormProductInfoSectionSelectors.productFormStrainInput).type('15');

    // Confirm modal pop up
    cy.contains('button', 'Next').should('exist').click();
    cy.get(selectors.modalContainerProductInfo).should('be.visible');
    cy.contains('button', 'Yes').should('exist').click();
      
    // Return to product page
    cy.contains('button', 'Next').click();
      
    // Skip filling out a price
    cy.contains('button', 'Finish').click();
    cy.contains('button', 'Skip for now').click();

    // Verify Add Product button is visible
    cy.contains('button', 'Add Product', { timeout: 10000 }).should('exist');
  });

  it('Should verify the error messages while updating the product name information', () => {
    // Search product using search text box
    cy.get('input[placeholder="Search..."]').type(productName);
    cy.contains(selectors.productControl.productName, productName, { timeout: 10000 }).should('exist').click({force:true});
      
    // Edit Product information screen
    cy.get(selectors.productFormProductInfoSectionSelectors.productHeading, { timeout: 10000 }).should('contain', productName);
    cy.get(selectors.productFormProductInfoSectionSelectors.productInfo).contains('Parent Product Information');
      
    // Product name validation
    cy.get(`${selectors.productFormProductInfoSectionSelectors.productName} input`).clear()
    cy.contains('button', 'Next').click();
    cy.contains('button', 'Yes').should('exist').click();
    cy.get(selectors.productFormProductInfoSectionSelectors.productName).contains(validationMessage.productInfo.productName);
      
    // Re enter the product name
    cy.get(`${selectors.productFormProductInfoSectionSelectors.productName} input`).clear().type(productName);
    cy.contains('button', 'Next').click();
      
    // Continue to the Pricing section
    cy.contains('button', 'Next').click();

    // Skip filling out a price
    cy.contains('button', 'Finish').click();
    cy.contains('button', 'Skip for now').click();
  });
});
