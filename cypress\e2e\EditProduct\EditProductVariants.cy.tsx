import { applicationPaths, selectors } from '../../support/constants';
import { useTestIdSelector, getRandomNumber, generateRandomAlphabets } from '../../support/helpers';

describe('Edit Variant test cases', () => {
  let randomProductNumber = getRandomNumber(1, 1000);
  let randomAlphabets = generateRandomAlphabets();
  const productName = `Cypress Test ${randomProductNumber} ${randomAlphabets}`;
  beforeEach(() => {
    // Login
    cy.loginAs('admin');
    cy.visit(applicationPaths.homePage);
    cy.contains('button', 'Add Product', { timeout: 10000 }).first().click();

    // Wait for page to be fully loaded and interactive
    cy.get('body').should('not.have.class', 'loading');
        
    // Product information header
    cy.get(selectors.productFormProductInfoSectionSelectors.productInfo, { timeout: 10000 }).contains('Parent Product Information');
      
    // Select the Category
    cy.get(selectors.productFormProductInfoSectionSelectors.productCategoryBeverage, { timeout: 10000 }).click({force: true});
        
    // Enter the Product Name
    cy.get(selectors.productFormProductInfoSectionSelectors.productName, { timeout: 10000 }).type(productName);
        
    // Brand
    cy.get(selectors.productFormProductInfoSectionSelectors.productFormBrandInput, { timeout: 10000 });
    cy.get(selectors.productFormProductInfoSectionSelectors.productFormBrandInput).click({force: true});
    cy.get(selectors.productFormProductInfoSectionSelectors.productFormBrandInput)
    .type("Test Brand").get(useTestIdSelector('autocomplete-option')).eq(0).click();
        
    // Sub Category
    cy.get(selectors.productFormProductInfoSectionSelectors.productFormSubCatLabel).contains("Subcategory *");
    cy.get(selectors.productFormProductInfoSectionSelectors.productFormSubCategoryInput).click().get('ul > li[tabindex="0"]').click(); 
        
    // Description
    cy.get(selectors.productFormProductInfoSectionSelectors.productFormDescriptionInput)
    .type("Cypress product create test description.");
        
    // Classification
    cy.get(selectors.productFormProductInfoSectionSelectors.productFormClassification).click().get('ul > li[data-value="Sativa"').click();
        
    // Strain
    cy.get(selectors.productFormProductInfoSectionSelectors.productFormStrainInput).type('12');
    cy.contains('button', 'Save And Next').click();
      
    // Sku
    cy.get(selectors.productFormVariantsSection.sizeAmount).should('not.be.disabled');
    cy.get(selectors.productFormVariantsSection.sizeAmount).type(`${randomProductNumber}`);
        
    //Enter SKU ,MG THC. MG CBD, THC PerDose , CBD PerDose & Doses
    cy.get(selectors.productFormVariantsSection.totalFlowerweight).click().type('12');
    cy.get(selectors.productFormVariantsSection.totalMgCbd).click().type('20');
    cy.get(selectors.productFormVariantsSection.thcPerDose).click().type('20');
    cy.get(selectors.productFormVariantsSection.cbdPerDose).click().type('20');
    cy.get(selectors.productFormVariantsSection.doses).click().type('20');
    cy.get(selectors.productFormVariantsSection.netWeight).click().type('12');
    cy.get(selectors.productFormVariantsSection.netWeightUom).click({force:true});
    cy.get('ul > li[data-value="GRAMS"]').click({force:true});
        
    //Menu title
    cy.get(selectors.productFormVariantsSection.menuTitle).click().type(`${productName} Menu title`);
    cy.contains('Use global description').should('be.visible');
        
    //Description is enabled
    cy.get(selectors.productFormVariantsSection.descriptionTextField).should('not.be.disabled');
        
    //Tick the Use Global description checkbox and verify if description field is disabled,verify the text
    cy.contains('Use global description').should('be.visible').click();
    cy.get(selectors.productFormVariantsSection.descriptionTextField).should('exist');
    cy.contains('button', 'Save And Next').click();
        
    // Skip filling out pricing        
    cy.contains('h6', 'Pricing', { timeout: 10000 }).scrollIntoView().should('be.visible');        
    cy.contains('button', 'Finish').click();
    cy.contains('button', 'Skip for now').click()
        
    // Search product using search text box
    cy.get('input[placeholder="Search..."]').type(productName);
    cy.contains(selectors.productControl.productName, productName).should('exist').click({force:true});
    cy.get(selectors.productFormProductInfoSectionSelectors.productHeading).should('contain', productName);
    cy.get(selectors.productFormProductInfoSectionSelectors.productInfo).contains('Parent Product Information');
    cy.contains('button', 'Next').click();
        
    // Verify Variant header is visible
    cy.contains('h6', 'SKU', { timeout: 10000 }).should('be.visible');
  });

  it.skip('Edit Variant - navigate to other section after editing', () => {
    // Clear and type into other fields with new hardcoded numbers
    cy.get(`${selectors.productFormVariantsSection.totalFlowerweight} input`)
    .clear({ force: true }).type('25', { force: true });
    cy.get(`${selectors.productFormVariantsSection.totalMgCbd} input`).clear().type('45');
    cy.get(`${selectors.productFormVariantsSection.thcPerDose} input`).clear().type('10');
    cy.get(`${selectors.productFormVariantsSection.cbdPerDose} input`).clear().type('18');
    cy.get(`${selectors.productFormVariantsSection.doses} input`).clear().type('50');
    cy.get(`${selectors.productFormVariantsSection.netWeight} input`).clear().type('30');
    cy.get(selectors.productFormVariantsSection.netWeightUom).click({force:true});
    cy.get('ul > li[data-value="LITERS"]').click({force:true});
      
    // Menu title
    cy.contains('Use global description').should('be.visible');
      
    // Description is enabled
    cy.get(selectors.productFormVariantsSection.descriptionTextField).should('not.be.disabled');
      
    // Tick the Use Global description checkbox and verify if description field is disabled,verify the text
    cy.contains('Use global description').click();
    cy.get(selectors.productFormVariantsSection.descriptionTextField).should('exist');
      
    // Verify Save and continue button
    cy.contains('button', 'Next').click();
    cy.get(selectors.modalContainerProductInfo).should('be.visible');
    cy.contains('button', 'Yes').should('exist').click();
      
    // Skip filling out pricing        
    cy.contains('h6', 'Pricing', { timeout: 10000 }).scrollIntoView().should('be.visible');        
    cy.contains('button', 'Finish').click();
    cy.contains('button', 'Skip for now').click()
      
    // Type search term in the search box
    cy.get('input[placeholder="Search..."]').type(productName); 
      
    // Press the Enter key to initiate the search
    cy.get('input[placeholder="Search..."]').type('{enter}');
  });

  it('test for error message when changing active to deactivate', () => {
    // Verify Deactivate variant
    cy.contains('button', 'Deactivate').scrollIntoView().should('exist').click({force: true});
    cy.contains('button', 'Activate').should('not.exist');
    
    // Varaint deactivate confirmation
    cy.contains('button', 'OK').click();
    cy.contains('Deactivated').should('exist');
    
    // save change
    cy.contains('button', 'Save').click();
    cy.contains('button', 'Next').click();
    
    // Navigate to other section
    cy.contains('h6', 'Pricing', { timeout: 10000 }).scrollIntoView().should('be.visible');   
    
    // Navigate back to variant screen and activate the deactivated variant
    cy.get(selectors.linearStepper).contains('SKU').click();
    cy.contains('button', 'Activate').should('exist').click();
    cy.contains('button', 'OK').click();
    cy.contains('button', 'Activate').should('not.exist');
    cy.contains('button', 'Deactivate').should('exist');
    
    // Varaint activate confirmation
    cy.contains('button', 'Next').click();
    cy.get(selectors.modalContainerProductInfo).should('be.visible');
    cy.contains('button', 'Yes').should('exist').click();
    cy.get(selectors.modalContainerProductInfo)
    .contains("Variant can't be activated for a deactivated product. Activate the product first.");
  });

  it('Verify edit Variant screen - adding additional SKUs', () => {
    // Add an additional SKU
    cy.get(selectors.productFormVariantsSection.addSize1).click();
    cy.get(selectors.productFormVariantsSection.sizeAmount1).type('10');
    cy.get(selectors.productFormVariantsSection.totalMgCbd1).type('25');
    cy.get(selectors.productFormVariantsSection.doses1).click().type('5');
    cy.get(selectors.productFormVariantsSection.thcPerDose1).type('15');
    cy.get(selectors.productFormVariantsSection.cbdPerDose1).type('10');
    cy.get(selectors.productFormVariantsSection.totalFlowerWeight1).type('7');
    cy.get(selectors.productFormVariantsSection.netWeight1).type('3');
    cy.get(selectors.productFormVariantsSection.netWeightUom1).click({ force: true });
    cy.get('ul > li[data-value="GRAMS"]').click({ force: true });
    
    // Menu title
    cy.get(selectors.productFormVariantsSection.menuTitle1).click()
    .type(`Cypress Test ${randomProductNumber} Menu title ${randomAlphabets}`);
    cy.contains('Use global description').should('be.visible');
      
    // Verify Save and continue button
    cy.contains('button', 'Next').click();
    cy.get(selectors.modalContainerProductInfo).contains('button', 'Yes').click();
      
    // Skip filling out pricing        
    cy.contains('h6', 'Pricing', { timeout: 10000 }).scrollIntoView().should('be.visible');        
    cy.contains('button', 'Finish', { timeout: 10000 }).should('be.visible').click();
    cy.contains('button', 'Skip for now', { timeout: 10000 }).should('be.visible').click()
     
    // Type search term in the search box
    cy.get('input[placeholder="Search..."]').type(productName); 
      
    // Press the Enter key to initiate the search
    cy.get('input[placeholder="Search..."]').type('{enter}');
    cy.get(selectors.productControl.productName).eq(0).click();
  });

  it('Verify edit Variant screen - duplicate Variants not allowed', () => {
    // Enter SKU ,MG THC. MG CBD, THC PerDose , CBD PerDose & Doses
    cy.get(selectors.productFormVariantsSection.addSize1).click();
    cy.get(selectors.productFormVariantsSection.sizeAmount1).type(`${randomProductNumber}`);
    cy.get(selectors.productFormVariantsSection.totalMgCbd1).type('12');
    cy.get(selectors.productFormVariantsSection.doses1).click().type('20');
    cy.get(selectors.productFormVariantsSection.thcPerDose1).type('20');
    cy.get(selectors.productFormVariantsSection.cbdPerDose1).type('20');
    cy.get(selectors.productFormVariantsSection.totalFlowerWeight1).type('20');
    cy.get(selectors.productFormVariantsSection.netWeight1).type('12');
    cy.get(selectors.productFormVariantsSection.netWeightUom1).click({ force: true });
    cy.get('ul > li[data-value="GRAMS"]').click({ force: true });
      
    // Menu title
    cy.get(selectors.productFormVariantsSection.menuTitle1).click().type(`Cypress Test ${randomProductNumber} Menu title ${randomAlphabets}`);
    cy.contains('Use global description').should('be.visible');
      
    // Tick the Use Global description checkbox and verify if description field is disabled,verify the text
    cy.contains('button', 'Next').click();
    cy.get(selectors.modalContainerProductInfo).should('be.visible');
    cy.contains('button', 'Yes').should('exist').click();
      
    // Verify Duplicate Variant Error Message
    cy.get(selectors.productFormVariantsSection.errorMessageExitModal).should('exist');
    cy.get(selectors.productFormVariantsSection.errorMessageExitModal).contains('Duplicate variant found').should('exist');
  });


  it.skip('Verify edit Variant screen - variant with sample/promo, when size edited sample/promo also gets updated ', () => {
    let newTotalAmount = getRandomNumber(1, 100);

    cy.contains('SAMPLES AND PROMOS').should('exist');

    cy.get(selectors.productFormVariantsSection.sampleCheckBox)
      .scrollIntoView()
      .should('exist')
      .click({ force: true });
    cy.get(selectors.productFormVariantsSection.promoCheckBox)
      .scrollIntoView()
      .should('exist')
      .click({ force: true });
      
    // check it sample and promo have the correct amount in the title  
    // cy.get(selectors.productFormVariantsSection.variantTitle).eq(1).should('contain', randomProductNumber);
    // cy.get(selectors.productFormVariantsSection.variantTitle).eq(2).should('contain', randomProductNumber);

    // Change the total amount
    cy.get(`${selectors.productFormVariantsSection.sizeAmount} input`).clear().type(`${newTotalAmount}`);

    // Check if sample and promo have the updated amount on the title
    // cy.get(selectors.productFormVariantsSection.variantTitle).eq(1).should('contain', newTotalAmount);
    // cy.get(selectors.productFormVariantsSection.variantTitle).eq(2).should('contain', newTotalAmount);
  });
});