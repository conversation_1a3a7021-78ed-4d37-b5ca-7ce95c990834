import { applicationPaths, selectors } from '../../support/constants';

//Feature not available
describe('Product Detail screen UI verification test cases', () => {
    beforeEach(() => {
      cy.clearCookies();
        cy.loginAs('admin');
        cy.visit(applicationPaths.homePage);
    });
    
    it('Verify the product detail screen is navigated to product information screen on clicking on the product name', () => {
      cy.get(selectors.productControl.productName, { timeout: 10000 }).eq(0).click();
      cy.get(selectors.productFormProductInfoSectionSelectors.productInfo, { timeout: 10000 })
      .contains('Parent Product Information').should('exist');
      cy.get(selectors.linearStepper).should('exist');
      
      // Go to SKU using the stepper
      cy.get(selectors.linearStepper).contains('SKU').click({force: true});
      cy.contains('h6', 'SKU', { timeout: 10000 }).should('be.visible');
      
      // Go to Base Price using the stepper
      cy.get(selectors.linearStepper).contains('Pricing').click();
      cy.get(selectors.modalContainerProductInfo).contains('button', 'No').click();
      cy.contains('h6', 'Pricing', { timeout: 10000 }).scrollIntoView().should('be.visible');        
      
      // Skip feeling out the price
      cy.contains('button', 'Finish').click();
      cy.contains('button', 'Skip for now').click()
    });
});
    