import { applicationPaths } from '../support/constants';

describe('Home Page', () => {
  beforeEach(() => {
    cy.clearCookies();
    cy.loginAs('admin');
  });

  it('Should open homepage without errors', () => {
    cy.visit(applicationPaths.homePage);
  });

  it('Should show the product search component', () => {
    cy.visit(applicationPaths.homePage);
    cy.contains('Add Product').should('be.visible');
  });
});