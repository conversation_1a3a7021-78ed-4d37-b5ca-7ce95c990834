import { applicationPaths, selectors } from '../support/constants';
import { useFieldIdSelector, useTestIdSelector, getRandomNumber, generateRandomAlphabets } from '../support/helpers';

describe('Product Control Home Page', () => {
  beforeEach(() => {
    cy.clearCookies();
    cy.loginAs('admin');
    cy.visit(applicationPaths.homePage);
    
    // Wait for page to be fully loaded and interactive
    cy.get('body').should('not.have.class', 'loading');
  });

  it('Validate product control page', () => {
    
    // Open the home page
    cy.contains('button', 'Add Product', { timeout: 10000 }).should('be.visible');

    // User Icon validation
    // Assuming useTestIdSelector is a function that generates a selector
    cy.get(useTestIdSelector('top-nav-wrapper')).contains('person').click();

    cy.get(useTestIdSelector('tooltip')).contains('@treez.io');
    cy.get(useTestIdSelector('top-nav-profile-menu') + ' [role="menuitem"]').eq(0).should('contain','Manage account');
    cy.get(useTestIdSelector('top-nav-profile-menu') + ' [role="menuitem"]').eq(1).should('contain','Sign out');
    cy.get(useTestIdSelector('top-nav-profile-menu')).click();
        
    // Catalog icon validation
    cy.get(useTestIdSelector('listGroup-item')).eq(1).trigger('mouseover')
    .should('be.visible').trigger('mouseout');

    // Search text box validation
    cy.get('input[placeholder="Search..."]').type('@#$@!#$%');
    cy.get(useTestIdSelector('error-message-selector')).should('not.exist');
    cy.get(useTestIdSelector('nav-content-container')).should('contain', 'No products to display');

    // Signout button validation
    cy.get(useTestIdSelector('top-nav-wrapper') + ' [role="img"]').click(); 
    cy.get(useTestIdSelector('top-nav-profile-menu') + ' [role="menuitem"]').eq(1).click();
    cy.get(useTestIdSelector('styled-form-title')).should('contain', `You're now signed out`);
    cy.get(useTestIdSelector('styled-form-description')).should('contain', `We'll see you next time! Until then... stay groovy.`);
    cy.get(useTestIdSelector('styled-form-description')).should('contain', `Ready to sign back in?`);
  });
 
  
  it.skip('Verify columns and sort icon in product control screen', () => {
    // Column header validation
    cy.get(useFieldIdSelector('productName')).should('contain', 'Product');
    cy.get(useFieldIdSelector('allSizes')).should('contain', 'Size / Amount');
    cy.get(useFieldIdSelector('brandName')).should('contain', 'Brand');
    cy.get(useFieldIdSelector('productCategoryName')).should('contain', 'Category');
    cy.get(useFieldIdSelector('productSubCategoryName')).should('contain', 'Subcategory');
    cy.get(useFieldIdSelector('lastUpdated')).should('contain', 'Last Update');
    cy.get(useFieldIdSelector('status')).should('contain', 'Status');
        
    // Product count
    cy.get(useTestIdSelector('count-users')).should('be.visible');

    // Product sort icon validation
    cy.get(useFieldIdSelector('productName') + ' span').eq(0).trigger('mouseover');
    cy.get(useFieldIdSelector('productName') + ' [role="img"]').should('exist').click({force:true});
    cy.get(useFieldIdSelector('productName') + ' [role="img"]').click({force:true});

    // Sizes sort icon validation
    cy.get(useFieldIdSelector('allSizes') + ' span').eq(0).trigger('mouseover');
    cy.get(useFieldIdSelector('allSizes') + ' [role="img"]').should('exist').click({force:true});
    cy.get(useFieldIdSelector('allSizes') + ' [role="img"]').click({force:true});

    // Brand sort icon validation
    cy.get(useFieldIdSelector('brandName') + ' span').eq(0).trigger('mouseover');
    cy.get(useFieldIdSelector('brandName') + ' [role="img"]').should('exist').click({force:true});
    cy.get(useFieldIdSelector('brandName') + ' [role="img"]').click({force:true});

    // Product category sort icon validation
    cy.get(useFieldIdSelector('productCategoryName') + ' span').eq(0).trigger('mouseover');
    cy.get(useFieldIdSelector('productCategoryName') + ' [role="img"]').should('exist').click({force:true});
    cy.get(useFieldIdSelector('productCategoryName') + ' [role="img"]').click({force:true});

    // Product sub category sort icon validation
    cy.get(useFieldIdSelector('productSubCategoryName') + ' [role="img"]').should('exist').click({force:true});
    cy.get(useFieldIdSelector('productSubCategoryName') + ' [role="img"]').click({force:true});

    // Last Updated sort icon validation
    cy.get(useFieldIdSelector('lastUpdated') + ' span').eq(0).trigger('mouseover');
    cy.get(useFieldIdSelector('lastUpdated') + ' [role="img"]').should('exist').click({force:true});
    cy.get(useFieldIdSelector('lastUpdated') + ' [role="img"]').click({force:true});

    // Status sort icon validation
    cy.get(useFieldIdSelector('status') + ' span').eq(0).trigger('mouseover');
    cy.get(useFieldIdSelector('status') + ' [role="img"]').should('exist').click({force:true});
    cy.get(useFieldIdSelector('status') + ' [role="img"]').click({force:true});

    // Pricing tab validation
    cy.get(useTestIdSelector('secondary-nav')).should('contain', 'Pricing').should('not.be.selected'); 
  })
    
  it('Should be able to select a product for editing', () => {
    cy.get(selectors.productControl.productName).first().click();
    cy.contains('button', 'Close', { timeout: 10000 }).first().should('be.visible').click();
  });


  // Filter Icons will soon be removed
  it.skip('Should be able to select category filter button', () => {
    // Select Beverage category
    cy.get(selectors.productControl.categoryFilter).contains('Beverage').click({force:true});
        
    // Select Cartridge category
    cy.get(selectors.productControl.categoryFilter).contains('Cartridge').click({force:true});
        
    // Uncheck Bevergae category
    cy.get(selectors.productControl.categoryFilter).contains('Beverage').click({force:true});
        
    //Search for non existing products
    cy.get(selectors.productControl.searchProduct).type('!@##$%'); 
    cy.get(selectors.productControl.searchProduct).type('{enter}');
       
    // Validate response for no products
    cy.get(useTestIdSelector('nav-content-container')).should('contain', 'No products to display');
        
    // Category count
    cy.get(useTestIdSelector('category-filter-badge-counter')).should('not.exist');
  });
   
  it('Should be able to select status filter button', () => {
    // Status filter
    cy.get(selectors.productControl.statusFilter).should('contain', 'Status').click();
    cy.contains(selectors.productControl.statusFilterOptions, 'Active')
    .within(() => {
      cy.get('input[type="checkbox"]').should('be.checked');
    });
    cy.get(selectors.productControl.statusFilterOptions).contains('Deactivated')
        
    // Status validation for all the displayed products
    cy.get(selectors.productControl.filterChip).contains('Active').should('exist');
        
    // Select multiple status
    cy.contains(selectors.productControl.statusFilterOptions, 'Deactivated')
    .within(() => {
      cy.get('input[type="checkbox"]').click();
    });
    cy.get(selectors.productControl.filterChip).contains('Active').should('exist');   
    cy.get(selectors.productControl.filterChip).contains('Deactivated').should('exist');
        
    // No status is selected
    cy.contains(selectors.productControl.statusFilterOptions, 'Deactivated')
    .within(() => {
      cy.get('input[type="checkbox"]').click();
    });
    cy.contains(selectors.productControl.statusFilterOptions, 'Active')
      .within(() => {
        cy.get('input[type="checkbox"]').click();
      });
  });

  it('Dashboard and catalog icon validation', () => {
    // Dashboard validation
    cy.get(useTestIdSelector('listGroup-item')+ ' [aria-label="house icon"]')
    .trigger('mouseover').should('be.visible').click();
        
    cy.get(useTestIdSelector('listGroup-item') + ' [aria-label="folder icon"]')
    .trigger('mouseover').should('be.visible').click().trigger('mouseout');
  });

  it.skip('Should validate pagination', () => {
    // Pagination
    cy.get(useTestIdSelector('nav-content-container') +' [aria-label="pagination navigation"]' + ' [type="button"]').contains('1');
    cy.get(useTestIdSelector('NavigateNextIcon')).should('exist');

    // Validate the product count in each page
    cy.get(useTestIdSelector('product-control-product-table-product-name-container')).should('have.length', 20);
  })

  it('Should be able to perform Bulk actions Activate & Deactivate for 2 Products', () => {
    cy.get(useFieldIdSelector('__check__')).eq(1).click();
    cy.get(useFieldIdSelector('__check__')).eq(2).click();
        
    //Deactivate BULK action Bar
    cy.get(selectors.productControl.bulkActions).first().click();
    cy.get(selectors.productControl.confirmationDialogWindow).should('exist');
    cy.get(selectors.productControl.confirmButton).should('contain','Confirm').click();
        
    //Status Filter
    cy.get(selectors.productControl.statusFilter).click({force: true});
        
    // Select Active and Deactivated status
    cy.contains(selectors.productControl.statusFilterOptions, 'Deactivated')
    .within(() => {
      cy.get('input[type="checkbox"]').click({force: true});
    });
    cy.get(useFieldIdSelector('status') + ' p').eq(0).should('contain', 'Deactivated');
    cy.get(useFieldIdSelector('status') + ' p').eq(1).should('contain', 'Deactivated');
  });

  it('Should be able to perform Bulk actions Activate & Deactivate for more than 2 Products', () => {
    cy.get(useFieldIdSelector('__check__')).eq(1).click();
    cy.get(useFieldIdSelector('__check__')).eq(2).click();
    cy.get(useFieldIdSelector('__check__')).eq(3).click();
    cy.get(useFieldIdSelector('__check__')).eq(4).click();
        
    //Deactivate BULK action Bar
    cy.get(selectors.productControl.bulkActions).first().click();
    cy.get(selectors.productControl.confirmationDialogWindow).should('exist');
    cy.get(selectors.productControl.confirmButton).should('contain','Confirm').click();
        
    //Status Filter
    cy.get(selectors.productControl.statusFilter).click();
        
    // Select Active and Deactivated status
    cy.contains(selectors.productControl.statusFilterOptions, 'Deactivated')
    .within(() => {
      cy.get('input[type="checkbox"]').click();
    });
    cy.get(useFieldIdSelector('status') + ' p').eq(0).should('contain', 'Deactivated');
    cy.get(useFieldIdSelector('status') + ' p').eq(1).should('contain', 'Deactivated');
  });
});