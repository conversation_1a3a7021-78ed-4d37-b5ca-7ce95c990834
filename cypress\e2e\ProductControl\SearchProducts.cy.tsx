import { applicationPaths, selectors } from '../../support/constants';

describe('Search product test cases', () => {

  beforeEach(() => {
    cy.clearCookies();
      cy.loginAs('admin');
      cy.visit(applicationPaths.homePage);
  });

  it('should search for a product and show result in Product Control data grid', () => {
    // type search term in the search box
    const searchTerm = 'Test';
     cy.get('input[placeholder="Search..."]').type(searchTerm ); 
    
    // Press the Enter key to initiate the search
     cy.get('input[placeholder="Search..."]').type('{enter}');
    cy.get('.MuiDataGrid-main').then((body) => {
        const productNameContainer = body.find(selectors.productControl.productName);
        
        // Check if product is displayed in search result
        if (productNameContainer.length > 0) {
          cy.wrap(productNameContainer).should('be.visible').contains(searchTerm);
          cy.log(`Product with searchTerm '${searchTerm}' is displayed.`);
        } else {
          cy.get('[class*="MuiBox-root css-1er59a8"]').should('be.visible');
          cy.log('No products to display');
        }
    });
  });

  it('product category filter shows up the list after page refresh', () => {
    // type search term in the search box
    const searchTerm = 'Test';
     cy.get('input[placeholder="Search..."]').type(searchTerm ); 
    
    // Select Beverage category
    cy.get(selectors.productControl.categoryFilter).contains('Beverage').click({force:true});
    
    // Reload the page and verify category filter dropdown
    cy.reload();
    
    // Assert that the search term is still present in the search input
     cy.get('input[placeholder="Search..."]')
    .should('have.value', searchTerm);
    
    // Assert that the 'Beverage' category is still selected/active
    cy.get(selectors.productControl.categoryFilter)
    .contains('Beverage')
  });
});