/// <reference types="cypress" />

// ***********************************************
// This example commands.ts shows you how to
// create various custom commands and overwrite
// existing commands.
//
// For more comprehensive examples of custom
// commands please read more here:
// https://on.cypress.io/custom-commands
// ***********************************************
//
//
// -- This is a parent command --
// Cypress.Commands.add('login', (email, password) => { ... })
//
//
// -- This is a child command --
// Cypress.Commands.add('drag', { prevSubject: 'element'}, (subject, options) => { ... })
//
//
// -- This is a dual command --
// Cypress.Commands.add('dismiss', { prevSubject: 'optional'}, (subject, options) => { ... })
//
//
// -- This will overwrite an existing command --
// Cypress.Commands.overwrite('visit', (originalFn, url, options) => { ... })
//

Cypress.Commands.add('checkIfPricingGridIsVisible', () => {
    cy.get('.MuiDataGrid-main').then((body) => {
        if (body.find('.MuiDataGrid-row').length) {
            // check if store data with pricing loaded for selected product
            cy.get('.MuiDataGrid-row').first().should('be.visible');
        }
        else if(body.find('.MuiDataGrid-overlayWrapper').length) {
            // otherwise must show the html element that has 'No Results' text
            cy.get('[class*="MuiDataGrid-overlayWrapper"]').should('be.visible');
        }
    });
});
