const basePath = '/product-control';

export const selectors = {  
  saveBtn: `[data-testid="save-section-button"]`,
  productInfoSaveBtn: `[data-testid="product-info-form-next-section-button"]`,
  snackBar: '[data-testid="snackbar"]',
  productFormTitle: '[data-testid="product-form-section-header"]',
  attributeTitleAttributes: '[data-testid="attributeTitle-attributes"]',
  productFormNameInput: '[data-testid="input-name"]',
  linearStepper: '[data-testid="linear-stepper"]',
  pricingPageContent: '[data-testid="pricing-list-container"]',
  modalContainerProductInfo: '[data-testid="product-form-exit-modal"]',

  productControl: {
    menu: '[data-testid="product-table-dropdown-menu"]',
    menuEdit: '[data-testid="product-table-dropdown-menu"] > .material-symbols-rounded > span',
    menuEditProductOption: '[data-testid="product-table-dropdown-menu-item-edit-product"]',
    searchProduct: '[id="Find or Scan product"]',
    productName: '[data-testid="product-control-product-table-product-name-container"]',
    checkBox: '[class*="PrivateSwitchBase-input"]',
    expandRowButton: '[data-testid="expanddiscountrow-button"]',
    gridRowRole: '[role="row"]',
    allSize: '[data-testid="allSizes"]',
    categoryFilter: '[data-testid="product-category-filter"]',
    categoryFilterBeverageList: '[aria-label="Beverage"]',
    bulkActions: '[id="bulk-action-bar"] p',
    confirmationDialogWindow: '[data-testid="actions-confirmation-modal"]',
    confirmButton: '[data-testid="primaryButton-confirmation-modal"]',
    statusFilter: '[data-testid="Status-filter"]',
    statusFilterOptions: 'label.MuiFormControlLabel-root',
    filterChip: '.MuiChip-root .MuiChip-label',
    deactivatedFilter: '[id="category-checkbox"] [aria-label="Deactivated"]',
    productsTab: '[data-testid="secondary-nav"]',
    productsDisplay: '[data-testid="product-control-product-table-product-name-container"]',
    sizeAndAmountColumn: '[data-testid="multiplestore-chip"]',
    statusColumn: '[data-field="status"]',
    variantsDeactivateMenuList: '[data-testid="variant-table-dropdown-menu-item-Deactivate-variant"]',
    variantsActivateMenuList: '[data-testid="variant-table-dropdown-menu-item-activate-variant"]',
    editVariantMenuList: '[data-testid="variant-table-dropdown-menu-item-edit-variant"]',
    basePrice: '[data-field="basePrice"]',
    activateDropdown: 'div li[data-testid="variant-table-dropdown-menu-item-activate-variant"]',
    bulkActionBarButtons: `#bulk-action-bar`,
  },
  secondaryNavigation: '[data-testid="secondary-nav"]',
  chevronLeftIcon: '[data-testid="ChevronLeftIcon"]',
  editProduct: '[data-testid="primaryButton-product-details-drawer"]',
  productFormProductInfoSectionSelectors: {
    productInfo: '[data-testid="product-information-section-header"]',
    productInfoSubHeader: '[data-testid="product-information-section-sub-header"]',
    productName: '[data-testid="input-name"]',
    productCategoryBeverage: '[data-testid="category-button-Beverage"]',
    productCategoryCartridge: '[data-testid="category-button-Cartridge"]',
    productFormBrandInput: '[id="select-with-search"]',
    productFormSubCatLabel: '[id="productSubCategoryId-label"]',
    productFormSubCategoryInput: '[data-testid="input-productsubcategoryid"]',
    productFormDescriptionInput: '[data-testid="input-details.description"]',
    productFormUomInput: '[data-testid="input-uom"]',
    productFormClassification: '[data-testid="input-details.classification"]',
    productFormStrainInput: '[data-testid="input-strain"] > .MuiInputBase-root',
    productFromStatusInput: '[data-testid="input-status"]',
    productInfoSection: '[data-testid="product-form-stepper-step-0"]',
    productCategoryMerch: '[data-testid="category-button-Merch"]',
    productHeading: '[data-testid="product-heading"]',
  },
  productFormProductDetailsSection: {
    productNameDetailSection: ':nth-child(3) > .MuiTypography-root',
    globalDescription: ':nth-child(4) > .MuiBox-root > .MuiTypography-root',
    noAttributesPreview: '[data-testid="drawer-body-container"] > :nth-child(6) > .MuiTypography-root',
    variantPrice: '#variant-price-typo',
  },
  productFormVariantsSection: {
    expandSampleOrPromo: '[data-testid="variant-header"]',
    sampleAndPromoPrice: '[data-testid="sample-promo-price"]',
    variantTitle: '[data-testid="variant-title"]',
    skuTotalAmountInput:'[data-testid="input-variants\\.0\\.amount"]',
    variantSize: '[data-testid="input-variants.0.variantproperties.size"]',
    variantVerticalpointer: ':nth-child(3) > .MuiStepLabel-root > .MuiStepLabel-labelContainer > .MuiStepLabel-label',
    totalMgThc: '[data-testid="input-variants.0.details.totalmgthc"]',
    totalMgCbd: '[data-testid="input-variants.0.details.totalmgcbd"]',
    thcPerDose: '[data-testid="input-variants.0.details.thcperdose"]',
    cbdPerDose: '[data-testid="input-variants.0.details.cbdperdose"]',
    netWeight: '[data-testid="input-variants.0.details.netweight"]',
    nameInput: '[data-testid="input-variants.0.name"]',
    labelPrinter: '[data-testid="input-variants.0.labelprinter"]',
    netWeightUom: '[data-testid="input-variants.0.details.netweightuom"] > .MuiSelect-select',
    doses: '[data-testid="input-variants.0.details.doses"]',
    useExternalId: '[data-testid="checkbox-variants.0.usereferenceid"]',
    userExternalIdCheckBox: '[aria-label="Use external ID"]',
    menuTitle: '[data-testid="input-variants.0.details.menutitle"]',
    descriptionTextField: '[data-testid="input-variants.0.details.description"]',
    descriptionTextField1: '[data-testid="input-variants.1.details.description"]',
    globalDescriptionCheckBox: '[data-testid="ecommerce-container"] [class*="MuiCheckbox-colorPrimary"]',
    deleteVariantButton: ':nth-child(5) > .MuiPaper-root > .MuiAccordionSummary-root > .MuiAccordionSummary-content > [data-testid="variant-header"] > .MuiButtonBase-root',
    attributeVerticalPointer: ':nth-child(5) > .MuiStepLabel-root > .MuiStepLabel-labelContainer > .MuiStepLabel-label',
    variantSku: '[data-testid="input-variants.0.sku"]',
    variantAdditionalSku: '[data-testid="input-variants.0.additionalsku.0"]',
    variantInputSize: '[data-testid="input-size"]',
    sizeErrorMessage: '#Sizes-helper-text',
    sizeValidationErrorBox: '[data-testid="validation-error-box"]',
    validationError: '[data-testid="variant-size-validation-error-box"]',
    variantSkuValidationError: '[data-testid="variant-sku-validation-error-box"]',
    totalMgThcValidationError: '[data-testid="totalMgThc-validation-error-box"]',
    totalMgCbdValidationError: '[data-testid="totalMgCbd-validation-error-box"]',
    thcPerDoseValidationError: '[data-testid="thcPerDose-validation-error-box"]',
    cbdPerDoseValidationError: '[data-testid="cbdPerDose-validation-error-box"]',
    dosesValidationError: '[data-testid="doses-validation-error-box"]',
    totalFlowerweight: '[data-testid="input-variants.0.details.totalflowerweight"]',
    variantDetailsHeader: '[data-testid="variant-details-header"]',
    additionalSKU: '[data-testid="input-variants.0.additionalsku.0"]',
    additionalSKU1: '[data-testid="input-variants.0.additionalsku.1"]',
    additionalSKU2: '[data-testid="input-variants.0.additionalsku.2"]',
    additionalSKU3: '[data-testid="input-variants.0.additionalsku.3"]',
    additionalSKU4: '[data-testid="input-variants.0.additionalsku.4"]',
    productFormExitModal: '[data-testid="product-form-exit-modal"]',
    closeButton: 'div[class*="MuiDialog-container"] p',
    deactivated: '[data-testid="variant-header"] div span[class*="MuiChip-label MuiChip-labelMedium"]',
    alertMessage: '.MuiAlert-message',
    variantDeactivateCancelButton: '[data-testid="secondaryButton-variant-deactivate-modal"]',
    variantDeactivateButton: '[data-testid="primaryButton-variant-deactivate-modal"]',
    sampleCheckBox: '[aria-label="Create a Sample version of this SKU"]',
    promoCheckBox: '[aria-label="Create a Promo version of this SKU"]',
    deactivateButton: '[data-testid="variant-header"] > .css-d5aw3p > .MuiButtonBase-root',
    sizeMultiSelect: '.MuiSelect-select',
    duplicateSizeError: '[class*="MuiDialog-paperWidthSm"] h2',
    sizeAmount: '[data-testid="input-variants.0.amount"]',
    sizeAmount1: '[data-testid="input-variants.1.amount"]',
    sizeAmount2: '[data-testid="input-variants.2.amount"]',
    sizeLabel: '[data-testid="input-variants.0.variantproperties.sizelabel"] > .MuiSelect-select',
    sizeDetail2: '[data-testid="input-variants.1.variantproperties.size"] > .MuiInputBase-root',
    sizeDetail3: '[data-testid="input-variants.2.variantproperties.size"] > .MuiInputBase-root',
    errorMessage3: '[data-testid="input-variants.2.variantproperties.size"]',
    sizeLabel2: '[data-testid="input-variants.1.variantproperties.sizelabel"] > .MuiSelect-select',
    variantSku2: '[data-testid="input-variants.1.sku"] > .MuiInputBase-root',
    addSize1: '.MuiTabs-flexContainer > .MuiButton-root',
    addSize: '.MuiBox-root > .MuiTabs-root > .MuiTabs-scroller > .MuiTabs-flexContainer > .MuiButtonBase-root',
    totalFlowerWeight1: '[data-testid="input-variants.1.details.totalflowerweight"]',
    totalMgCbd1: '[data-testid="input-variants.1.details.totalmgcbd"] > .MuiInputBase-root',
    totalMgThc1: '[data-testid="input-variants.1.details.totalmgthc"] > .MuiInputBase-root',
    doses1: '[data-testid="input-variants.1.details.doses"] > .MuiInputBase-root',
    thcPerDose1: '[data-testid="input-variants.1.details.thcperdose"] > .MuiInputBase-root',
    cbdPerDose1: '[data-testid="input-variants.1.details.cbdperdose"] > .MuiInputBase-root',
    netWeight1: '[data-testid="input-variants.1.details.netweight"] > .MuiInputBase-root',
    netWeightUom1: '[data-testid="input-variants.1.details.netweightuom"] > .MuiSelect-select',
    menuTitle1: '[data-testid="input-variants.1.details.menutitle"] > .MuiInputBase-root',
    errorMessageExitModal: '[data-testid="alert-snackbar"]'

  },
  productFormAttributesSection: {
    removeAllOptions: '[data-testid="CloseIcon"]',
    attributesSection: '[data-testid="product-form-stepper-label-step-2"]',
    productAttributeCategoryContainer: '[data-testid="product-attribute-categories-container"]',
    productAttributeCategory: '[data-testid="product-attribute-category"]',
    attributeDropdown: '[data-testid="ArrowDropDownIcon"]',
    addNewAttribute: '.css-3mlzgg > .MuiButtonBase-root',
    addNewAttributeDialog: '.MuiDialogContent-root > .MuiBox-root',
    alertPop: '[data-testid="alert-snackbar"]',
    clearAttribute: ':nth-child(1) > .MuiAutocomplete-root > .MuiFormControl-root > .MuiInputBase-root > .MuiAutocomplete-endAdornment',
    closeAttributeIcon: '.MuiGrid-container > :nth-child(1) > .MuiAutocomplete-root > .MuiFormControl-root > .MuiInputBase-root > .MuiAutocomplete-endAdornment',
    cancelSingleAttribute: '[data-testid="CancelIcon"]',
    selectFirstOption: '#multi-select-with-search-option-0',
    selectSecondOption: '#multi-select-with-search-option-1',
    selectThirdOption: '#multi-select-with-search-option-2',
    productInfoForm: '[id="ProductInfoForm"]',
    flavourAttributeDropdown: ':nth-child(3) > .MuiAutocomplete-root > .MuiFormControl-root > .MuiInputBase-root',
    generalAttributeDropdown: ':nth-child(4) > .MuiAutocomplete-root > .MuiFormControl-root > .MuiInputBase-root',
    effectsAttributeDropdown: ':nth-child(5) > .MuiAutocomplete-root > .MuiFormControl-root > .MuiInputBase-root',
    ingredientsAttributeDropdown: ':nth-child(6) > .MuiAutocomplete-root > .MuiFormControl-root > .MuiInputBase-root',
  },
  productFormPricingSectionSelectors: {
    pricingSection: '[data-testid="product-form-stepper-label-step-3"]',
    pricingSectionContainer: '[data-testid="pricing-section-container"]',
    variantPricingContainer: '[data-testid="variant-pricing-container"]',
    variantPricingInput: '[data-testid*="input-variants"]',
    managePricingButton: '[data-testid="manage-pricing-button"]',
    createPricingVerticalHeader: ':nth-child(7) > .MuiStepLabel-root > .MuiStepLabel-labelContainer > .MuiStepLabel-label',
    basePriceTextBox: '[data-testid="input-variants.0.defaultprices.base"]',
  },
  productFormImagesSection: {
    imagesSection: '[data-testid="product-form-stepper-label-step-4"]',
    globalImageSection: '[data-testid="product-form-images-header"]',
    previousButton: '[data-testid="previous-section-button"]',
    fileUploadComponent: '[data-testid="file-upload-component"]',
    saveButton: '.MuiButtonBase-root',
    doneButton: '[data-testid="done-button"]',
    fileUpload: '[data-testid="file-upload-component"] input',
    imageTitle: '[data-testid="input-name"]',
    imageDescription: '[data-testid="input-description"]',
    variantImage: '[data-testid="variant-image-0"]',
    productImage: '[data-testid="product-image"]', // This also equals as ImageDisplay
    productRemove:'[data-testid="delete-product-image"]',
    imageText: '[id="ProductInfoForm"] p',
    globalImageCheckbox: '.css-dqgz5c > .MuiBox-root > .MuiFormControl-root > .MuiFormControlLabel-root > .MuiTypography-root',
  },
  priceByProduct: {
    productSelectorResult: '[data-testid="product-selector-result"]',
    productSelectorInfo: '[data-testid="product-selector-info"]',
    productSelectorShowDropdown: '[data-testid="product-selector-down-button"]',
    productSelectorHideDropdown: '[data-testid="product-selector-up-button"]',
    pricingTab: '[data-testid="secondary-nav"]',
    productSearch: '.MuiInputBase-root',
    invalidProductSearchText1: '[data-testid="product-selector-container"] div[class*="MuiBox-root"] p[class*="MuiTypography-root MuiTypography-body1 css-hw8a7j"]',
    invalidProductSearchText: '[data-testId="no-results-container"]',
    productCount: '[data-testId="product-count"]',
    productSelectedInfo: '[data-testid="product-selector-info"] p[class*="MuiTypography-body1"]',
    details: `[data-testId="detail"]`,
    productFormExitModal: '[data-testid="actions-product-form-exit-modal"]',
    yesButton: '[data-testid="primaryButton-product-form-exit-modal"]',
    noButton: '[data-testid="secondaryButton-product-form-exit-modal"]',
    searchBar: '#:r1p:',
    storeNamesContent: '.MuiDataGrid-virtualScrollerContent',
    storeNameValues: '.MuiDataGrid-virtualScrollerRenderZone',
    noResultsForStore: 'div[class*="MuiDataGrid-virtualScroller"] div[class="MuiDataGrid-overlay css-14349d1"]',
    storeEditable: '.MuiDataGrid-cell--editable',
    storeEditField: 'div[class*="MuiDataGrid-cell--editable"] input',
    basePriceSavingError: '[data-testid="alert-snackbar"]',
    basePriceSaveErrorMessage: '.MuiAlert-message',
    searchStoreName: '.MuiInputBase-root',
    storeSearch: '.MuiFormControl-root > .MuiInputBase-root',
    columnHeaderTitleContainer: '.MuiDataGrid-columnHeaderTitleContainer',
    sortIcon1: 'div[class="MuiDataGrid-columnHeaderTitleContainer"] span span',
    pricingEdit: '.row-massPriceUpdate > .MuiDataGrid-cell--editable > .css-1hc9zz8 > .MuiBox-root > .MuiTypography-root',
    priceEnterValue: '.MuiDataGrid-cell--editing > .MuiInputBase-root > .MuiInputBase-input',
    sortNameIcon: '.MuiDataGrid-columnHeaderTitleContainer > .MuiDataGrid-iconButtonContainer > .MuiButtonBase-root > .material-symbols-rounded > span',
    sortPriceIcon: '.MuiDataGrid-columnHeaderTitleContainer > .MuiDataGrid-iconButtonContainer > .MuiButtonBase-root > .material-symbols-rounded > span',
  },
  mergeProduct: {
    toolBar: '[data-testid="data-grid-pro-toolbar"]',
    productContainer: '[data-testid="drawer-body-container"]',
    productContainerLength: '[data-testid="merge-product-item"]',
    mergeProductHeader: '[data-testid="drawer-header"]',
    closeButton: '[data-testid="drawer-close-button"]',
    cancelButton: '[data-testid="secondaryButton-merge-product-details-drawer"]',
    mergeButtonModal: '[data-testid="merge-product-modal"]',
    mergeButton: '[data-testid="primaryButton-merge-product-details-drawer"]',
    expandIcon: '[data-testid="ExpandMoreIcon"]',
    alertCancelButton: '[data-testid="secondaryButton-merge-product-modal"]',
    alertMergeButton: '[data-testid="primaryButton-merge-product-modal"]',
    homePage: '[data-testid="nav-content-container"]',
    deactivate: '[data-testid="data-grid-pro-toolbar"] div[id="bulk-action-bar"] button',
    mergedChildCheckbox: '[data-field="__check__"] span input[class*="PrivateSwitchBase-input"]',
  },
} as const;

export const validationMessage = {
  productInfo:{
    productName : "Product Name is required.",
    brandName : "Brand is required.",
    subCategory : "Subcategory is required.",
    footerError : "There are items above that require your attention",
    enterNumberMessage: "Must be a number",
    mustBeANumberMessage: "Must be a number",
    pricingValidation: "Must be a number",
    brandNameString: `"brandId" must be a string`,
    skuValidationMessage: "Special characters not permitted for SKU",
    sizePositiveNumber: "Must be a positive number",
  },
} as const;

export const applicationPaths = {
  homePage: `${basePath}`,
  createProductPage: `${basePath}/create`,
  priceByProductPage: `${basePath}/pricing`
};

