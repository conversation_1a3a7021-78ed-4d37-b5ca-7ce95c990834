// See https://im360us.atlassian.net/wiki/spaces/development/pages/2614755627/Meet+The+QA+Team
// These are the role IDs for user 'qa-tester+<subdomain>-<EMAIL>'
// Login credentials are stored in SSM
const defaultNonAdminRoleId = {
  sandbox: '00000004-4305-40ac-bd58-d69566ef04a7',
  build: '68340376-747f-4469-9590-cc7534df8240',
} as const;

export const getCypressEnv = (env: 'sandbox' | 'build') => {
  const subdomain = env === 'build' ? 'dev' : 'sandbox' as const;
  return {
    authenticationApiUrl:
      process.env.CYPRESS_AUTHN_API_URL ||
      `https://api.${subdomain}.treez.io/auth-v2`,
    roleApiUrl:
      process.env.CYPRESS_ROLE_API_URL || `https://api.${subdomain}.treez.io/role/v1`,
    users: {
      admin: {
        username:
          process.env.CYPRESS_ADMIN_USERNAME || 
          `qa-tester+${subdomain}@treez.io`,
      },
      nonAdmin: {
        username:
          process.env.CYPRESS_NON_ADMIN_USERNAME ||
          `qa-tester+${subdomain}-<EMAIL>`,
        roleId:
          process.env.CYPRESS_NON_ADMIN_ROLE_ID || 
          defaultNonAdminRoleId[env],
      },
    },
    // set this to 'true' if you want to use your locally running version of the product-control MFE
    // note that Cypress hot reloading does not seem to play nicely with Single SPA...
    useImportMapOverride: process.env.CYPRESS_USE_IMPORT_MAP_OVERRIDE === 'true'
  };
};
