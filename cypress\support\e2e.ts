import { TUser } from './types';
import ITokens from '../../src/interfaces/tokens';

const { authenticationApiUrl, roleApiUrl, users, useImportMapOverride } = Cypress.env(
  Cypress.env('stage'),
);

export const nonAdminRoleId = users.nonAdmin.roleId;
export { roleApiUrl };

export const getTokens = () => {
  const tokens = window.localStorage.getItem('tz-tokens');
  if (tokens === null) {
    throw new Error('No tokens found');
  }
  return JSON.parse(tokens) as ITokens;
};

beforeEach(() => {
    if (useImportMapOverride) {
        window.localStorage.setItem('import-map-override:@treez/product-control', 'https://localhost:3001/treez-product-control.js');
    }
})

Cypress.on('uncaught:exception', (e) => {
  if (
    e.message.includes(`Cannot read properties of null (reading 'accessToken')`)
  ) {
    // we expected this error from the ui core, so let's ignore it
    // and let the test continue
    return false;
  }
  // on any other error message the test fails
});

Cypress.Commands.add('login', (username, password) => {
  return cy.request({
    method: 'POST',
    url: `${authenticationApiUrl}/login`,
    body: Buffer.from(
      JSON.stringify({
        username,
        password,
      }),
    ).toString('base64'),
    headers: {
      'x-application': 'MSO',
    },
  })
    .its('body')
    .then((body) => {
      window.localStorage.setItem('tz-tokens', JSON.stringify(body.tokens));
    });
});

Cypress.Commands.add('loginAs', (userType: TUser) => {
  const username = users[userType].username;
  cy.task('getPassword', username).then(({ error, password }: any) => {
    if (error !== null) {
      cy.log(`Error getting password: ${JSON.stringify(error)}`);
    } else {
      cy.login(username, password);
    }
  });
});

Cypress.Commands.add('clearCache', () => {
  cy.clearLocalStorage();
  cy.clearCookies();
});
