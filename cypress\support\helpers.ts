export function useTestIdSelector(testId: string) {
  return `[data-testid="${testId}"]`;
}

export function useFieldIdSelector(testId: string) {
  return `[data-field="${testId}"]`;
}

export function getRandomNumber(min: number, max: number): number {
  return Math.floor(Math.random() * (max - min +1) + min);
}

export function generateRandomAlphabets(){
  const alphabet = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
  let randomAlphabets = '';

  for (let i = 0; i < 3; i++) {
    const randomIndex = Math.floor(Math.random() * alphabet.length);
    randomAlphabets += alphabet.charAt(randomIndex);
  }
  
  return randomAlphabets;
}

export function generateRandomSpecialCharacters(length: number) {
  const specialCharacters = '!@#$%^&*()-_=+[{]}|;:,<.>/?';
  let result = '';
  for (let i = 0; i < length; i++) {
      const randomIndex = Math.floor(Math.random() * specialCharacters.length);
      result += specialCharacters[randomIndex];
  }
  return result;
}