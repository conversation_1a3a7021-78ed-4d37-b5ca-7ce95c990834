{"name": "product-control-mfe", "description": "Product Control MFE (MSO Micro Front-End)", "scripts": {"analyze": "webpack --mode=production --env analyze", "checks": "{ yarn format || yarn format:fix > /dev/null; } && yarn lint && yarn compile", "check:commit": "yarn build:types && yarn lint:fix && yarn format:fix && yarn test", "compile": "tsc", "cypress": "cypress open", "build": "webpack --mode=production", "build:dev": "webpack --mode=production --env stage=dev", "build:build": "webpack --mode=production --env stage=build", "build:prod": "webpack --mode=production --env stage=prod", "build:types": "tsc", "format": "prettier --check ./src", "format:fix": "prettier --write ./src", "knip:unimported": "yarn knip -c ./knip.json --files --include exports,types,nsExports,nsTypes --exclude dependencies", "knip:debug": "yarn knip -c ./knip.json --debug --no-exit-code", "lint": "eslint ./src", "lint:fix": "eslint ./src --fix", "start:standalone": "webpack serve --port 3001 --env standalone", "start:local": "webpack serve --port 3001 --env stage=local", "start:local:https": "webpack serve --port 3001 --env stage=local https=true", "start:sandbox": "webpack serve --port 3001 --env stage=dev https=true", "start:build": "webpack serve --port 3001 --env stage=build https=true", "test": "jest --config ./jest.config.ts", "test:coverage": "jest --coverage --config ./jest.config.ts", "cypress:run:sandbox": "CYPRESS_BASE_URL=https://app.sandbox.treez.io CYPRESS_ADMIN_USERNAME=<EMAIL> cypress run --browser=chrome --env stage=sandbox", "cypress:run:dev": "yarn cypress:run:sandbox", "cypress:open:sandbox": "CYPRESS_BASE_URL=https://app.sandbox.treez.io CYPRESS_ADMIN_USERNAME=<EMAIL> cypress open --env stage=sandbox", "cypress:open:sandbox:override": "CYPRESS_USE_IMPORT_MAP_OVERRIDE=true CYPRESS_BASE_URL=https://app.sandbox.treez.io CYPRESS_ADMIN_USERNAME=<EMAIL> cypress open --env stage=sandbox", "cypress:open:build": "CYPRESS_BASE_URL=https://app.dev.treez.io CYPRESS_ADMIN_USERNAME=<EMAIL> cypress open --env stage=build", "cypress:run:build": "CYPRESS_BASE_URL=https://app.dev.treez.io CYPRESS_ADMIN_USERNAME=<EMAIL>  cypress run --browser=chrome --env stage=build"}, "version": "3.47.0", "private": true, "devDependencies": {"@babel/core": "^7.15.0", "@babel/eslint-parser": "^7.15.0", "@babel/plugin-transform-runtime": "^7.15.0", "@babel/preset-env": "^7.15.0", "@babel/preset-react": "^7.14.5", "@babel/preset-typescript": "^7.15.0", "@babel/runtime": "^7.15.3", "@faker-js/faker": "^7.6.0", "@testing-library/dom": "^8.19.0", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.3.0", "@testing-library/user-event": "^14.4.3", "@treez-inc/file-management": "^1.0.1", "@types/jest": "^28.1.7", "@types/lodash": "^4.17.9", "@types/node": "^18.11.7", "@types/react": "^18.0.21", "@types/react-dom": "^18.0.6", "@types/systemjs": "^6.1.1", "@types/uuid": "^9.0.8", "@types/webpack-env": "^1.16.2", "@typescript-eslint/eslint-plugin": "^5.33.0", "@typescript-eslint/parser": "^5.33.0", "babel-jest": "^27.0.6", "concurrently": "^6.2.1", "cypress": "^13.3.1", "dotenv-webpack": "^8.0.1", "eslint": "^7.32.0", "eslint-config-airbnb": "19.0.4", "eslint-config-airbnb-typescript": "^17.0.0", "eslint-config-prettier": "^8.3.0", "eslint-config-ts-react-important-stuff": "^3.0.0", "eslint-plugin-cypress": "^2.13.2", "eslint-plugin-import": "^2.25.3", "eslint-plugin-prettier": "^3.4.1", "eslint-plugin-react": "^7.28.0", "faker": "^6.6.6", "file-loader": "^6.2.0", "identity-obj-proxy": "^3.0.0", "jest": "^27.0.6", "jest-cli": "^27.0.6", "jest-transform-stub": "^2.0.0", "knip": "^5.55.1", "prettier": "^2.3.2", "ts-config-single-spa": "^3.0.0", "ts-node": "^10.9.1", "typescript": "^4.3.5", "webpack": "^5.51.1", "webpack-cli": "^4.8.0", "webpack-config-single-spa-react": "^4.0.0", "webpack-config-single-spa-react-ts": "^4.0.0", "webpack-config-single-spa-ts": "^4.0.0", "webpack-dev-server": "^4.0.0", "webpack-merge": "^5.8.0"}, "dependencies": {"@aws-sdk/client-ssm": "^3.303.0", "@emotion/react": "11.10.0", "@emotion/styled": "11.10.0", "@fontsource/roboto": "^4.5.8", "@hookform/resolvers": "^2.9.10", "@lukemorales/query-key-factory": "^1.0.3", "@mui/icons-material": "^5.11.0", "@mui/material": "^5.10.2", "@mui/x-data-grid-pro": "^6.0.0", "@treez-inc/component-library": "^6.4.2", "axios": "^1.3.2", "dayjs": "^1.11.8", "immer": "^10.1.1", "joi": "^17.7.0", "jwt-decode": "^3.1.2", "react": "18.2.0", "react-dom": "18.2.0", "react-hook-form": "^7.39.5", "react-query": "^3.39.2", "react-router-dom": "^6.4.1", "single-spa": "5.9.3", "single-spa-react": "^5.0.0", "use-debounce": "^9.0.4", "uuid": "^9.0.1"}, "types": "dist/treez-test.d.ts", "engines": {"node": ">=18"}}