import { isEmpty } from '../utils/common';
import { QueryKeyConfig } from './types';

const entityPriceApiKeyStore = {
    getEntityPrices: (variantIds?: string[]): QueryKeyConfig => ({
        queryKey: ['GET_ENTITY_PRICES', variantIds],
        route: `entity-price?${variantIds?.map((id) => `variantIds=${id}`).join('&')}`,
        isEnabled: !isEmpty(variantIds),
        refetchOnMount: false,
        refetchOnWindowFocus: false,
    }),
};

export default entityPriceApiKeyStore;
