import axios from 'axios';
import qs from 'qs';
import constants from '../utils/constants';
import AuthTokens from '../interfaces/tokens';

// NOTE: This function will be re-set before it's used in the accessor functions below
let getAuthTokens = (): AuthTokens => {
    // eslint-disable-next-line no-console
    console.warn('Calling dummy function to retrieve auth tokens');
    return {
        accessToken: '',
        refreshToken: '',
        expiresIn: 0,
        idToken: '',
    };
};

export const setAuthTokensAccessor = (getTokensFunc: () => AuthTokens) => {
    getAuthTokens = getTokensFunc;
};

export const getAccessToken = (): string => getAuthTokens().accessToken;

const getAuthHeadersConfig = () => {
    const authTokens = getAuthTokens();
    if (!authTokens?.accessToken) {
        // eslint-disable-next-line no-console
        console.error('Missing the needed accessToken!');
    }

    return {
        headers: {
            Authorization: `Bearer ${authTokens?.accessToken}`,
        },
    };
};

export const getDataByRoute = async (route: string, params: any) => {
    const result = await axios.get(`${route}`, {
        params,
        paramsSerializer: {
            serialize: (paramValues) => qs.stringify(paramValues, { arrayFormat: 'repeat' }),
        },
        ...getAuthHeadersConfig(),
    });
    return result.data;
};

export const getData = async (entity: string, params: any) => {
    const result = await axios.get(`${constants.PRODUCT_MANAGEMENT_SERVICE_API_URL}/${entity}`, {
        params,
        paramsSerializer: {
            serialize: (paramValues) => qs.stringify(paramValues, { arrayFormat: 'repeat' }),
        },
        ...getAuthHeadersConfig(),
    });
    return result.data;
};

export const createData = async (entity: string, data: any) => {
    const result = await axios.post(
        `${constants.PRODUCT_MANAGEMENT_SERVICE_API_URL}/${entity}`,
        data,
        getAuthHeadersConfig(),
    );
    return result.data;
};

export const updateData = async (entity: string, data: any) => {
    const result = await axios.patch(
        `${constants.PRODUCT_MANAGEMENT_SERVICE_API_URL}/${entity}`,
        data,
        getAuthHeadersConfig(),
    );
    return result.data;
};

export const deleteData = async (entity: string, data: any) => {
    const result = await axios.delete(`${constants.PRODUCT_MANAGEMENT_SERVICE_API_URL}/${entity}`, {
        data,
        ...getAuthHeadersConfig(),
    });
    return result.data;
};
