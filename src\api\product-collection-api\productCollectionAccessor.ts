import axios from 'axios';
import qs from 'qs';
import constants from '../../utils/constants';
import AuthTokens from '../../interfaces/tokens';
import { NEW_COLLECTION_PREFIX } from '../../views/ProductControl/components/ProductCollectionsDropdown';

// NOTE: This function will be re-set before it's used in the accessor functions below
let getProductCollectionAuthTokens = (): AuthTokens => {
    // eslint-disable-next-line no-console
    console.warn('Calling dummy function to retrieve auth tokens');
    return {
        accessToken: '',
        refreshToken: '',
        expiresIn: 0,
        idToken: '',
    };
};

export const setAuthTokensProductCollectionAccessor = (getTokensFunc: () => AuthTokens) => {
    getProductCollectionAuthTokens = getTokensFunc;
};

export const getAccessToken = (): string => getProductCollectionAuthTokens().accessToken;

const getProductCollectionAuthHeadersConfig = () => {
    const authTokens = getProductCollectionAuthTokens();
    if (!authTokens?.accessToken) {
        // eslint-disable-next-line no-console
        console.error('Missing the needed accessToken!');
    }

    return {
        headers: {
            Authorization: `Bearer ${authTokens?.accessToken}`,
        },
    };
};

export const getProductCollectionData = async (productCollectionId: string | '') => {
    if (productCollectionId.indexOf(NEW_COLLECTION_PREFIX) !== -1) {
        return undefined;
    }

    const result = await axios.get(`${constants.PRODUCT_COLLECTION_SERVICE_API_URL}/${productCollectionId}`, {
        paramsSerializer: {
            serialize: (paramValues) => qs.stringify(paramValues, { arrayFormat: 'repeat' }),
        },
        ...getProductCollectionAuthHeadersConfig(),
    });
    return result.data;
};

export const updateProductCollectionData = async (productCollectionId: string | undefined, data: any) => {
    const result = await axios.put(
        `${constants.PRODUCT_COLLECTION_SERVICE_API_URL}/${productCollectionId}`,
        data,
        getProductCollectionAuthHeadersConfig(),
    );
    return result.data;
};

export const createProductCollection = async (data: any) => {
    const result = await axios.post(
        `${constants.PRODUCT_COLLECTION_SERVICE_API_URL}/`,
        data,
        getProductCollectionAuthHeadersConfig(),
    );
    return result.data;
};
