import { MutationKeyConfig, QueryKeyConfig } from '../types';

const productCollectionApiKeyStore = {
    getProductCollections: (): QueryKeyConfig => ({
        queryKey: ['GET_PRODUCT_COLLECTIONS'],
        route: ``,
    }),
    getProductCollectionDetails: (collectionId?: string): QueryKeyConfig => ({
        queryKey: ['GET_PRODUCT_COLLECTION_DETAILS', collectionId],
        route: `${collectionId}`,
        isEnabled: !!collectionId,
    }),
    updateProductCollectionDetails: (collectionId?: string): MutationKeyConfig => ({
        mutationKey: ['UPDATE_PRODUCT_COLLECTION_DETAILS'],
        route: `${collectionId}`,
    }),
    createProductCollection: (): MutationKeyConfig => ({
        mutationKey: ['CREATE_PRODUCT_COLLECTION'],
        route: ``,
    }),
};

export default productCollectionApiKeyStore;
