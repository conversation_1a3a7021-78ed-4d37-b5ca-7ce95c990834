import { MutationKeyConfig, QueryKeyConfig } from './types';

const productApiKeyStore = {
    getProductDetails: (productId?: string): QueryKeyConfig => ({
        queryKey: ['GET_PRODUCT_DETAILS', productId],
        route: `product/${productId}`,
        isEnabled: !!productId,
        refetchOnMount: false,
        refetchOnWindowFocus: false,
    }),
    saveProductDetails: (): MutationKeyConfig => ({
        mutationKey: ['SAVE_PRODUCT_DETAILS'],
        route: `product`,
    }),
};

export default productApiKeyStore;
