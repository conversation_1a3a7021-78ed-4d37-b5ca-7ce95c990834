import { QueryKeyConfig } from './types';

const productCategoryApiKeyStore = {
    getAllCategories: (): QueryKeyConfig => ({
        queryKey: ['GET_PRODUCT_CATEGORIES'],
        route: `product-category`,
        cacheTime: Infinity,
        staleTime: Infinity,
    }),
    getAllSubCategories: (): QueryKeyConfig => ({
        queryKey: ['GET_PRODUCT_SUB_CATEGORIES'],
        route: `product-subcategory`,
        cacheTime: Infinity,
        staleTime: Infinity,
    }),
};

export default productCategoryApiKeyStore;
