import React from 'react';
import { Box, ButtonBase, styled } from '@mui/material/';
import { Add } from '@mui/icons-material';
import { convertPxToRem } from '@treez-inc/component-library';
import PermissionsGuard from './PermissionsGuard';

export const NoOptions = styled(Box)({
    alignItems: 'center',
    color: '#595959', // grey08
    fontWeight: 400,
    fontSize: convertPxToRem(15),
    lineHeight: convertPxToRem(24),
    paddingTop: convertPxToRem(10),
    paddingBottom: convertPxToRem(15),
});

export const NewDropdownOption = styled(Box)({
    color: '#1A1A1A', // primary black
    fontStyle: 'normal',
    fontWeight: 400,
    fontSize: convertPxToRem(15),
    lineHeight: convertPxToRem(24),
    overflow: 'hidden',
    paddingLeft: convertPxToRem(10),
    paddingRight: convertPxToRem(10),
    textOverflow: 'ellipsis',
    whiteSpace: 'nowrap',
});

export const CreateOptionContainer = styled(Box)({
    display: 'inline-flex',
    paddingBottom: convertPxToRem(15),
    width: '100%',
});

const CreateOption = styled(ButtonBase)({
    alignItems: 'center',
    background: '#A9E079', // sativa green
    borderRadius: convertPxToRem(99),
    gap: convertPxToRem(7.5),
    height: convertPxToRem(24),
    flexDirection: 'row',
    justifyContent: 'center',
    padding: convertPxToRem(6),
    width: convertPxToRem(24),
});

const CreateOptionIcon = styled(Add)(() => ({
    boxSizing: 'border-box',
    height: '0.75em',
}));

interface AddNewOptionProps {
    newSelectValue: string;
    permissions: string[];
    showConfirmation: () => void;
}

const AddNewOption = ({ newSelectValue, permissions, showConfirmation }: AddNewOptionProps) => (
    <PermissionsGuard permissions={permissions}>
        <CreateOptionContainer>
            <CreateOption data-testid="create-option" onClick={() => showConfirmation()}>
                <CreateOptionIcon />
            </CreateOption>
            <NewDropdownOption>{newSelectValue}</NewDropdownOption>
        </CreateOptionContainer>
    </PermissionsGuard>
);

export default AddNewOption;
