import React, { useState } from 'react';
import { Button, Modal } from '@treez-inc/component-library';
import { IconName } from '@treez-inc/component-library/dist/components/Icon/types';

interface ButtonWithConfirmationProps {
    disabled?: boolean;
    iconName?: IconName;
    label: string;
    small?: boolean;
    confirmModel: {
        title: string;
        content: any;
    };
    onConfirm: () => void;
}

const ButtonWithConfirmation = ({
    disabled = false,
    onConfirm,
    label,
    iconName,
    small,
    confirmModel,
}: ButtonWithConfirmationProps) => {
    const [showConfirmDialog, setShowConfirmDialog] = useState<boolean>(false);

    return (
        <>
            <Modal
                title={confirmModel.title}
                open={showConfirmDialog}
                onClose={() => setShowConfirmDialog(false)}
                content={<>{confirmModel.content}</>}
                primaryButton={{
                    label: 'OK',
                    onClick: (e) => {
                        e.stopPropagation();
                        onConfirm();
                    },
                }}
                secondaryButton={{
                    label: 'Cancel',
                    onClick: (e) => {
                        e.stopPropagation();
                        setShowConfirmDialog(false);
                    },
                }}
            />
            <Button
                disabled={disabled}
                iconName={iconName}
                small={small}
                label={label}
                onClick={(e) => {
                    e.stopPropagation();
                    setShowConfirmDialog(true);
                }}
                variant="secondary"
            />
        </>
    );
};

export default ButtonWithConfirmation;
