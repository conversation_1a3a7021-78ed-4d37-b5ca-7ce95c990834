import React from 'react';
import { styled, Box } from '@mui/material';
import { Checkbox, Tooltip, Icon } from '@treez-inc/component-library';
import convertPxToRem from '@treez-inc/component-library/dist/util-functions/convertPxToRem';
import { isEmpty } from '../utils/common';

interface HookFormCheckboxProps {
    value: boolean;
    label: string;
    toolTipText?: string;
    disabled?: boolean;
    onChange?: (checked?: boolean) => void;
}

const StyledHookContainer = styled(Box)({
    display: 'flex',
    alignItems: 'center',
    flexDirection: 'row',
    gap: convertPxToRem(8),
});
const StyledIconContainer = styled(Box)(() => ({
    display: 'flex',
}));

const CheckboxInput = ({ value, label, toolTipText = '', disabled = false, onChange }: HookFormCheckboxProps) => (
    <StyledHookContainer>
        <Checkbox value={value} label={label} checked={value} onChange={onChange} disabled={disabled} />
        {!isEmpty(toolTipText) && (
            <Tooltip
                title={toolTipText}
                variant="multiRow"
                testId={`${label?.toLowerCase().replaceAll(' ', '-')}-tooltip`}
            >
                <StyledIconContainer>
                    <Icon iconName="InfoOutlined" color="primaryBlack" />
                </StyledIconContainer>
            </Tooltip>
        )}
    </StyledHookContainer>
);

export default CheckboxInput;
