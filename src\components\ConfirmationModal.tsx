import React from 'react';
import { Box } from '@mui/material/';
import { Modal } from '@treez-inc/component-library';

interface ConfirmationModalProps {
    displayValue: string;
    label?: string;
    open: any;
    handleClose: any;
    handleSubmit: any;
    onSelectChange?: any;
}

const ConfirmationModal = ({
    displayValue,
    label,
    open,
    handleClose,
    handleSubmit,
    onSelectChange,
}: ConfirmationModalProps) => (
    <Modal
        title={`Add a new ${label}`}
        open={open}
        onClose={handleClose}
        content={
            <Box>
                Do you want to add the {label}:{' '}
                <span style={{ fontWeight: 'bold', fontSize: 'medium' }}>{displayValue}</span>?
            </Box>
        }
        primaryButton={{
            label: 'Add',
            onClick: () => {
                handleSubmit(onSelectChange);
            },
        }}
        secondaryButton={{
            label: 'Cancel',
            onClick: () => {
                handleClose();
            },
        }}
    />
);

export default ConfirmationModal;
