import React, { SyntheticEvent, useState } from 'react';
import { styled } from '@mui/material';
import { Button, ButtonProps, IconButton, Menu, TextMenuItem, convertPxToRem } from '@treez-inc/component-library';
import { IconName } from '@treez-inc/component-library/dist/components/Icon/types';

export interface BulkActionBarProps {
    /** Props to build text Buttons in order of display, omitting small and variant. */
    buttonProps: Array<Omit<ButtonProps, 'small' | 'variant'>>;
    /** Test id that can be used for targeting elements in tests: data-testid={testId}. */
    testId?: string;
    /** Applies color variation styles. */
    variant?: 'primary' | 'secondary';
}

const StyledButtonContainer = styled('div', {
    shouldForwardProp: (prop) => prop !== 'variant',
})<{ variant: any }>(({ theme, variant }) => ({
    padding: `${convertPxToRem(4)} ${convertPxToRem(16)}`,
    backgroundColor: variant === 'primary' ? theme.palette.treezGrey[3] : theme.palette.primaryWhite.main,
    boxSizing: 'border-box',
    display: 'flex',
    height: convertPxToRem(40),
    flexWrap: 'nowrap',
    gap: convertPxToRem(8),
    borderRadius: convertPxToRem(12),

    '.MuiIconButton-root': {
        padding: 0,
    },
}));

const BulkActionBar = ({ buttonProps, testId, variant = 'primary' }: BulkActionBarProps) => {
    const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);

    const open = Boolean(anchorEl);

    const handleClickMenu = (event: SyntheticEvent<HTMLElement>) => setAnchorEl(event.currentTarget);

    const handleClickMenuItem = () => setAnchorEl(null);

    const handleClose = () => setAnchorEl(null);

    return (
        <StyledButtonContainer id="bulk-action-bar" variant={variant} data-testid={testId}>
            {buttonProps.slice(0, 5).map((button) => (
                <Button key={button.label} small {...button} variant="text" />
            ))}
            {buttonProps.length > 5 && (
                <>
                    <IconButton
                        iconName="More"
                        size="small"
                        onClick={handleClickMenu}
                        variant="secondary"
                        aria-controls={open ? 'default-menu' : undefined}
                        aria-haspopup="true"
                        aria-expanded={open ? 'true' : undefined}
                        testId={testId && `${testId}-bulk-action-bar-menu-button`}
                    />
                    <Menu
                        menuId="example-menu"
                        anchorEl={anchorEl}
                        open={open}
                        onClose={handleClose}
                        MenuListProps={{
                            'aria-labelledby': 'menu-button',
                        }}
                        testId={testId && `${testId}-bulk-action-bar-menu`}
                    >
                        {buttonProps.slice(5).map((button, index) => (
                            <TextMenuItem
                                key={button.label}
                                color="green10"
                                displayName={button.label}
                                displayValue={button.label}
                                onClick={(event) => {
                                    button.onClick!(event as unknown as React.MouseEvent<HTMLButtonElement>);
                                    handleClickMenuItem();
                                }}
                                iconProps={{
                                    iconName: button.iconName as IconName,
                                    color: 'green10',
                                }}
                                testId={testId && `${testId}-bulk-action-bar-menu-item-${index}`}
                            />
                        ))}
                    </Menu>
                </>
            )}
        </StyledButtonContainer>
    );
};

export default BulkActionBar;
