import React from 'react';
import { styled, LinearProgress } from '@mui/material';
import { convertPxToRem } from '@treez-inc/component-library';

export const StyledLinearProgress = styled(LinearProgress)(({ theme }) => ({
    backgroundColor: theme.palette.green03.main,
    '& .MuiLinearProgress-bar': {
        backgroundColor: theme.palette.green06.main,
        borderRadius: convertPxToRem(99),
    },
}));

export const DataGridProLoadingOverlay = () => <StyledLinearProgress />;
