import React from 'react';
import { TablePaginationProps } from '@mui/material/TablePagination';
import {
    gridFilteredTopLevelRowCountSelector,
    gridPageSizeSelector,
    GridPagination,
    GridRenderPaginationProps,
    useGridApiContext,
    useGridRootProps,
    useGridSelector,
} from '@mui/x-data-grid-pro';
import MuiPagination from '@mui/material/Pagination';

// note: this is a workaround suggested by the MUI maintainers until they fix this bug:
// const pageCount = useGridSelector(apiRef, gridPageCountSelector); // always return 1
// workaround: https://github.com/mui/mui-x/issues/8450#issuecomment-1513570353
const getPageCount = (rowCount: number, pageSize: number): number => {
    if (pageSize > 0 && rowCount > 0) {
        return Math.ceil(rowCount / pageSize);
    }
    return 0;
};

function Pagination({ page, className }: Pick<TablePaginationProps, 'page' | 'className'>) {
    const apiRef = useGridApiContext();
    const pageSize = useGridSelector(apiRef, gridPageSizeSelector);
    const visibleTopLevelRowCount = useGridSelector(apiRef, gridFilteredTopLevelRowCountSelector);
    const rootProps = useGridRootProps();

    // This calculation only works for server side pagination only.
    // Since we are using server side pagination in our project, we are good here.
    const pageCount = getPageCount(rootProps.rowCount ?? visibleTopLevelRowCount, pageSize);

    return (
        <MuiPagination
            className={className}
            count={pageCount}
            page={page + 1}
            onChange={(event, value) => apiRef.current.setPage(value - 1)}
        />
    );
}

export default function DataGridProPagination(props: GridRenderPaginationProps) {
    return <GridPagination ActionsComponent={Pagination} {...props} />;
}
