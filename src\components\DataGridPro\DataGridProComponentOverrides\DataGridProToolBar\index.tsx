import React from 'react';
import { Box, Typography, styled } from '@mui/material';
import { convertPxToRem } from '@treez-inc/component-library';
import { GridToolbarQuickFilterProps } from '@mui/x-data-grid-pro';
import BulkActionBar, { BulkActionBarProps } from '../BulkActionBar';
import { ProductSearchResult } from '../../../../queries/useProductSearchQuery';
import SearchBarFilterV2 from '../../../FilterComponent/SearchBarFilterV2';
import useQueryParams from '../../../FilterComponent/hooks/useQueryParams';

interface DataGridProToolBarProps {
    /** Props to build the bulk action bar, displayed to the left of the search bar. */
    bulkActionBarProps?: BulkActionBarProps;
    /** Props to build the quick filter search bar. */
    quickFilterProps?: GridToolbarQuickFilterProps;
    productData: ProductSearchResult;
    selectedItemCount: number;
}

const StyledToolbar = styled('div', {
    shouldForwardProp: (prop) => prop !== 'showBulkActionBar',
})<{ showBulkActionBar: boolean }>(({ showBulkActionBar, theme }) => ({
    display: 'flex',
    justifyContent: showBulkActionBar ? 'space-between' : 'flex-end',
    gap: convertPxToRem(12),
    padding: convertPxToRem(12),

    '& .MuiInputBase-input::placeholder': {
        textAlign: 'center',
    },

    '& .MuiInput-root:hover:not(.Mui-disabled):before': {
        borderBottom: 'none',
    },
    '& .data-grid-pro-search-field': {
        flexShrink: 0,
        minWidth: convertPxToRem(257),
        marginLeft: convertPxToRem(12),
        display: 'flex',
        justifyContent: 'center',
        backgroundColor: theme.palette.treezGrey[1],
        padding: `0 ${convertPxToRem(12)}`,
        borderRadius: convertPxToRem(15),
        span: {
            marginRight: convertPxToRem(8),
        },
        boxSizing: 'border-box',
        height: convertPxToRem(40),
        '.MuiInput-underline:before': {
            borderBottom: 'none',
        },
        '.MuiInput-root': {
            '&:after': {
                borderBottom: 'none',
            },
            '&:hover': {
                borderBottom: 'none',
            },
        },
    },
}));

const StyledWrapper = styled(Box)`
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: center;
    gap: 1em;

    p {
        font-size: 0.7rem;
    }
`;

const StyledSearchBox = styled(Box)`
    display: flex;
    justify-content: space-between;
    width: 100%;
    align-items: center;
    margin-top: 1em;
`;

// TODO: add custom filtering components & custom search bar to this component
const DataGridProToolBar = (props: DataGridProToolBarProps) => {
    const { bulkActionBarProps, selectedItemCount, productData } = props;
    const { searchParams, updateSearchParams } = useQueryParams();
    const search = [searchParams.get('search') || ''];

    return (
        <StyledWrapper>
            <StyledSearchBox>
                <StyledToolbar showBulkActionBar={Boolean(bulkActionBarProps)} data-testid="data-grid-pro-toolbar">
                    <Typography variant="largeTextStrong" data-testid="count-users">
                        {selectedItemCount || (productData as any).totalRecords}
                        {selectedItemCount ? ' Selected ' : ' Results '}
                    </Typography>
                    {bulkActionBarProps && <BulkActionBar {...bulkActionBarProps} />}
                </StyledToolbar>
                <SearchBarFilterV2 search={search} updateSearchParams={updateSearchParams} />
            </StyledSearchBox>
        </StyledWrapper>
    );
};

export default DataGridProToolBar;
