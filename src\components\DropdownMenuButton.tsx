import React from 'react';
import { FormControl, InputLabel, MenuItem, Select } from '@mui/material';

interface DropdownMenuButtonProps {
    label: string;
    handleChange: (value: unknown) => void;
    menuItems: { label: string; value: string }[];
}

const DropdownMenuButton = ({ label, handleChange, menuItems }: DropdownMenuButtonProps) => (
    <FormControl fullWidth>
        <InputLabel id={`dropdown-button-label-${label}`}>{label}</InputLabel>
        <Select
            labelId={`dropdown-button-label-${label}`}
            id={`dropdown-button-id-${label}`}
            label={label}
            onChange={(event) => handleChange(event.target.value)}
        >
            {menuItems.map((menuItem) => (
                <MenuItem key={`${menuItem.label}-${menuItem.value}`} value={menuItem.value}>
                    {menuItem.label}
                </MenuItem>
            ))}
        </Select>
    </FormControl>
);

export default DropdownMenuButton;
