import React, { useEffect, useRef, useState } from 'react';
import { styled, Box, Typography, GlobalStyles, css, TextField } from '@mui/material';
import { DropdownChip, Menu, MenuItemCheckboxProps } from '@treez-inc/component-library';
import useMenu from './hooks/useMenu';
import { IFilterDropdown } from './types';
import removeDuplicates from '../../utils/removeDuplicates';

// These determine the height of the drop down content
const ITEM_HEIGHT = 36; // height of one checkbox item in px
const VISIBLE_ITEMS = 8; // how many checkboxes to render at once (height of scroll window) 8 is (Default Height)

const ScrollContainer = styled(Box, {
    shouldForwardProp: (prop) => prop !== 'itemCount',
})<{ itemCount: number }>(({ itemCount }) => ({
    height: ITEM_HEIGHT * itemCount,
    overflowY: 'auto',
    position: 'relative',
    width: 300,
}));

const StyledTextField = styled(TextField)(() => ({
    width: '100%',
    borderColor: '#f5f5f5',

    '& .MuiInputBase-root': {
        paddingLeft: '1em',
        paddingRight: '1em',
        paddingTop: '0.1em',
        paddingBottom: '0.1em',
        fontSize: '0.875rem',
    },

    '& .MuiInputLabel-root': {
        paddingLeft: '1em',
        paddingRight: '1em',
        fontSize: '0.875rem',
    },

    '& .MuiInputLabel-root.Mui-focused': {
        color: 'black',
    },

    '& .MuiInput-underline:before': {
        borderBottom: '3px solid #f5f5f5 !important',
    },
    '& .MuiInput-underline:hover:before': {
        borderBottom: '3px solid #f5f5f5 !important',
    },
    '& .MuiInput-underline:after': {
        borderBottom: '3px solid #f5f5f5 !important',
    },
}));

const InnerContainer = styled(Box)(() => ({
    position: 'relative',
}));

const CheckboxItem = styled(Box)(() => ({
    position: 'absolute',
    height: ITEM_HEIGHT,
    width: '100%',
    display: 'flex',
    alignItems: 'center',
    paddingLeft: 8,
    boxSizing: 'border-box',
}));

const CheckboxLabel = styled('label')(() => ({
    display: 'flex',
    alignItems: 'center',
    width: '100%',
}));

const StyledCheckbox = styled('input')(() => ({
    appearance: 'none',
    width: 18,
    height: 18,
    marginRight: 8,
    borderRadius: 4,
    border: '1px solid #c4c4c4',
    backgroundColor: '#fff',
    cursor: 'pointer',
    position: 'relative',
    display: 'inline-block',
    verticalAlign: 'middle',
    transition: 'all 0.2s ease',
    fontFamily: 'Roboto Helvetica Arial',

    '&:checked': {
        backgroundColor: '#8ccc52',
        borderColor: '#8ccc52',
    },

    '&:checked::after': {
        content: '""',
        position: 'absolute',
        left: '6px',
        top: '3px',
        width: '3px',
        height: '7px',
        border: 'solid black',
        borderWidth: '0 1px 1px 0',
        transform: 'rotate(45deg)',
    },
}));

const FilterNotFound = styled(Typography)(() => ({
    textAlign: 'center',
    color: 'gray',
    fontStyle: 'italic',
    marginTop: '.5em',
}));

const FilterDropdown = ({
    filterState,
    updateSearchParams,
    filterKey,
    menuId,
    chipId,
    label,
    values,
    searchField = true,
}: IFilterDropdown) => {
    const selectedFiltersInState: MenuItemCheckboxProps[] | undefined =
        filterState && filterState[filterKey] ? filterState[filterKey] : [];

    const displayCorrectFiltersSelected = removeDuplicates(selectedFiltersInState || []);

    const [scrollTop, setScrollTop] = useState(0);
    const [searchFilter, setSearchFilter] = useState<string>('');
    const [uniqueFilteredValues, setUniqueFilteredValues] = useState<MenuItemCheckboxProps[]>([]);

    const containerRef = useRef<HTMLDivElement>(null);

    const totalItems = uniqueFilteredValues.length;
    const startIndex = Math.floor(scrollTop / ITEM_HEIGHT);
    const endIndex = Math.min(startIndex + VISIBLE_ITEMS, totalItems);

    const visibleValues = uniqueFilteredValues.slice(startIndex, endIndex);

    const handleCheckboxChange = (checkboxKey: string | number, isChecked: boolean) => {
        const currentCheckbox: MenuItemCheckboxProps | undefined = values.find(
            (v: MenuItemCheckboxProps) => v.key === checkboxKey,
        );

        if (isChecked) {
            if (currentCheckbox) {
                // Find all checkboxes with the same label
                const checkboxesWithSameLabel = values.filter(
                    (v: MenuItemCheckboxProps) => v.label.toLowerCase() === currentCheckbox.label.toLowerCase(),
                );

                const newFiltersState: MenuItemCheckboxProps[] = selectedFiltersInState
                    ? [...selectedFiltersInState, ...checkboxesWithSameLabel]
                    : [...checkboxesWithSameLabel];

                // Remove duplicates from the state
                const uniqueFiltersState = Array.from(
                    new Map(newFiltersState.map((item) => [item.key, item])).values(),
                );

                const newFilterIdsState: string[] = uniqueFiltersState.map((f: MenuItemCheckboxProps) => f.value);
                updateSearchParams(filterKey, newFilterIdsState);
            }
        } else if (currentCheckbox) {
            // Remove all checkboxes with the same label
            const newFiltersState =
                selectedFiltersInState?.filter(
                    (f: MenuItemCheckboxProps) => f.label.toLowerCase() !== currentCheckbox.label.toLowerCase(),
                ) || [];
            const newFilterIdsState: string[] = newFiltersState.map((f: MenuItemCheckboxProps) => f.value);
            updateSearchParams(filterKey, newFilterIdsState);
        }
    };

    // Build checkboxes with checked flag, but only for visible items
    const visibleCheckboxes = visibleValues.map((v) => {
        const checked = selectedFiltersInState?.some((s) => s.key === v.key) ?? false;
        return {
            ...v,
            checked,
            onChange: (key: string | number, isChecked: boolean) => handleCheckboxChange(key, isChecked),
        };
    });

    useEffect(() => {
        const uniqueValues = removeDuplicates(values);

        const filtered = uniqueValues.filter((data) => data.label.toLowerCase().includes(searchFilter.toLowerCase()));
        setUniqueFilteredValues(filtered);
    }, [searchFilter, values]);

    const menu = useMenu();

    const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
        setScrollTop(e.currentTarget.scrollTop);
    };

    if (values.length === 0) {
        return null;
    }

    return (
        <div>
            <GlobalStyles
                styles={css`
                    #${chipId} {
                        height: 2.65em !important;
                        overflow: hidden !important;
                        padding: 0 1em !important;
                    }
                `}
            />
            <DropdownChip
                badgeContent={displayCorrectFiltersSelected?.length || 0}
                testId={`${label}-filter`}
                label={label}
                id={chipId}
                menuId={menuId}
                onClick={menu.handleClick}
                open={menu.open}
            />
            <Menu menuId={menuId} anchorEl={menu.anchorEl} open={menu.open} onClose={menu.handleClose}>
                {searchField && (
                    <StyledTextField
                        id="standard-basic"
                        label="Search..."
                        variant="standard"
                        onChange={(e) => setSearchFilter(e.target.value)}
                        value={searchFilter}
                    />
                )}
                <ScrollContainer
                    itemCount={visibleCheckboxes.length > 0 ? visibleCheckboxes?.length || VISIBLE_ITEMS : 1}
                    ref={containerRef}
                    onScroll={handleScroll}
                >
                    <InnerContainer sx={{ height: totalItems * ITEM_HEIGHT }}>
                        {visibleCheckboxes.length > 0 ? (
                            visibleCheckboxes.map((checkbox, index) => {
                                const itemIndex = startIndex + index;
                                return (
                                    <CheckboxItem key={checkbox.key} sx={{ top: itemIndex * ITEM_HEIGHT }}>
                                        <CheckboxLabel>
                                            <StyledCheckbox
                                                type="checkbox"
                                                checked={checkbox.checked}
                                                onChange={(e) => checkbox.onChange(checkbox.key, e.target.checked)}
                                            />
                                            <Typography>{checkbox.label}</Typography>
                                        </CheckboxLabel>
                                    </CheckboxItem>
                                );
                            })
                        ) : (
                            <FilterNotFound>{`${label} Not Found`}</FilterNotFound>
                        )}
                    </InnerContainer>
                </ScrollContainer>
            </Menu>
        </div>
    );
};

export default FilterDropdown;
