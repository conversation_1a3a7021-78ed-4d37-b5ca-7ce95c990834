import React from 'react';
import { Box, styled, Button } from '@mui/material/';
import { Icon, MenuItemCheckboxProps } from '@treez-inc/component-library';
import { IFilter, IFilterState, IProductCategoryFilter } from './types';
import { PRODUCT_CATEGORIES_ICONS } from '../../utils/product-categories-data';
import './ProductCategoryFilter.override.css';

const IconBox = styled(Box)({
    alignItems: 'center',
    display: 'flex',
    flexDirection: 'column',
    fontSize: '1rem',
    justifyContent: 'center',
    width: '4.375em',
    height: '4.375em',
    fontFamily: 'Roboto',
    color: 'rgb(128, 128, 128)',
    padding: '0.625em 0.3125em 0em 0.3125em',
    margin: 0,
});

interface ProductCategoryFilterProps {
    categoryData: MenuItemCheckboxProps[];
    filterState: IFilterState;
    updateSearchParams: (filterKey: keyof IFilter, value: string[] | undefined) => void;
}

const getCategoryIcon = (catName: string) => PRODUCT_CATEGORIES_ICONS[catName]?.iconName;

const getCategoryItems = (data: MenuItemCheckboxProps[]): IProductCategoryFilter[] =>
    data
        ?.filter((cat) => Object.keys(PRODUCT_CATEGORIES_ICONS).includes(cat.label))
        ?.map((cat: MenuItemCheckboxProps) => ({
            iconName: getCategoryIcon(cat.label) || 'Release',
            displayName: cat.label,
            categoryId: cat.value,
        }));

const ProductCategoryFilter: React.FC<ProductCategoryFilterProps> = (props) => {
    const { categoryData, filterState, updateSearchParams } = props;
    const selectedCatsInState: MenuItemCheckboxProps[] | undefined =
        filterState && filterState.category ? filterState.category : [];
    const selectedCatIdsInState: string[] = selectedCatsInState.map((cat: MenuItemCheckboxProps) => cat.value);

    const isCategorySelected = (cat: IProductCategoryFilter) => selectedCatIdsInState.includes(cat.categoryId);

    const handleCategories = (cat: IProductCategoryFilter) => {
        if (isCategorySelected(cat)) {
            const newCatState = selectedCatIdsInState.filter((catId: string) => catId !== cat.categoryId);
            updateSearchParams('category', newCatState);
        } else {
            const newCatState = [...selectedCatIdsInState, cat.categoryId];
            updateSearchParams('category', newCatState);
        }
    };

    return (
        <Box>
            <Box sx={{ display: 'flex', justifyContent: 'center' }}>
                {getCategoryItems(categoryData)
                    .sort((a, b) => a.displayName.localeCompare(b.displayName))
                    .map((cat: IProductCategoryFilter) => (
                        <Button
                            data-testid='product-category-filter'
                            key={cat.categoryId}
                            style={{
                                borderRadius: 24,
                                color: 'grey',
                                backgroundColor: isCategorySelected(cat) ? '#DBF5B3' : 'inherit',
                                transition: 'background-color 0.3s ease',
                                padding: 0,
                                margin: '0em 0.8em 1.25em 0em',
                            }}
                            sx={{
                                '&:hover': {
                                    backgroundColor: '#A9E079',
                                    borderRadius: 4,
                                },
                            }}
                            onClick={() => handleCategories(cat)}
                        >
                            <IconBox>
                                <Icon iconName={cat.iconName} fontSize="extraLarge" color="treezGreen" />
                                <p style={{ margin: '0.3125em 0 0 0', padding: 0 }}>{cat.displayName}</p>
                            </IconBox>
                        </Button>
                    ))}
            </Box>
        </Box>
    );
};

export default ProductCategoryFilter;
