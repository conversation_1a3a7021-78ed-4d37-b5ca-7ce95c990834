import React from 'react';
import { Box, styled } from '@mui/material';
import { Input } from '@treez-inc/component-library';
import { IFilter } from './types';

interface SearchBarFilterProps {
    search?: string[];
    updateSearchParams: (filterKey: keyof IFilter, value: string[] | undefined) => void;
}

const SearchBarFilterBox = styled(Box)({
    alignSelf: 'center',
    width: '31em',
    marginBottom: '2em',
});

const SearchBarFilter: React.FC<SearchBarFilterProps> = ({ search, updateSearchParams }: SearchBarFilterProps) => {
    const searchTerm: string = search?.join(' ') ?? '';
    const [inputValue, setInputValue] = React.useState(searchTerm);

    const onSearchChange = ({ target: { value } }: React.ChangeEvent<HTMLInputElement>) => {
        setInputValue(value);
        updateSearchParams('search', value.split(' '));
    };

    return (
        <SearchBarFilterBox>
            <Input label="Find or Scan product" startAdornment="Search" onChange={onSearchChange} value={inputValue} />
        </SearchBarFilterBox>
    );
};

export default SearchBarFilter;
