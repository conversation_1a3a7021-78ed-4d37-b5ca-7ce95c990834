import React from 'react';
import { Box, InputAdornment, styled, TextField } from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import { IFilter } from './types';

interface SearchBarFilterV2Props {
    search?: string[];
    updateSearchParams: (filterKey: keyof IFilter, value: string[] | undefined) => void;
}

const SearchBarFilterBox = styled(Box)({
    width: '18em',
});

const StyledSearchField = styled(TextField)({
    '& .MuiFilledInput-root': {
        borderRadius: '12px',
        backgroundColor: '#f7f7f7',
        height: 40,
        fontSize: '1.1em',
    },
    '& .MuiFilledInput-root:hover': {
        backgroundColor: '#f7f7f7',
    },
    '& .MuiFilledInput-root.Mui-focused': {
        backgroundColor: '#f7f7f7',
    },
    '& .MuiFilledInput-root:active': {
        backgroundColor: '#f7f7f7',
    },
    '& .MuiFilledInput-underline:before, & .MuiFilledInput-underline:after': {
        display: 'none',
    },
    '& input': {
        padding: '8px 0',
    },
});

const searchIconStyle = {
    marginBottom: '.75em',
    fontSize: 18,
    color: '#888',
};

const SearchBarFilterV2: React.FC<SearchBarFilterV2Props> = ({
    search,
    updateSearchParams,
}: SearchBarFilterV2Props) => {
    const searchTerm: string = search?.join(' ') ?? '';
    const [inputValue, setInputValue] = React.useState(searchTerm);

    const onSearchChange = ({ target: { value } }: React.ChangeEvent<HTMLInputElement>) => {
        setInputValue(value);
        updateSearchParams('search', value.split(' '));
    };

    return (
        <SearchBarFilterBox>
            <StyledSearchField
                placeholder="Search..."
                id="Find or Scan product-label"
                variant="filled"
                fullWidth
                value={inputValue}
                onChange={onSearchChange}
                InputProps={{
                    startAdornment: (
                        <InputAdornment position="start">
                            <SearchIcon sx={{ ...searchIconStyle, color: 'black', paddingRight: '.5em' }} />
                        </InputAdornment>
                    ),
                    disableUnderline: true,
                }}
            />
        </SearchBarFilterBox>
    );
};

export default SearchBarFilterV2;
