import React from 'react';
import { FilterChip, MenuItemCheckboxProps, convertPxToRem } from '@treez-inc/component-library';
import { Box, styled } from '@mui/material/';
import { IFilterState, IFilterCheckboxItem, IFilter, FilterKeys } from './types';
import removeDuplicates from '../../utils/removeDuplicates';

const SelectedFiltersBox = styled(Box)({
    display: 'flex',
    flexDirection: 'row',
    columnGap: convertPxToRem(8),
    flexWrap: 'wrap',
    gap: '1em',
});

interface SelectedFiltersProps {
    filterState: IFilterState;
    removeSearchParams: (filterKey: keyof IFilter, value: string) => void;
}

const SelectedFilters = ({ filterState, removeSearchParams }: SelectedFiltersProps) => {
    const keys = Object.keys(filterState).filter((key: string) => key !== 'search');
    const selectedCheckboxValues: IFilterCheckboxItem[] = [];
    keys.forEach((key: string) => {
        const currentCheckboxes: MenuItemCheckboxProps[] | undefined = filterState[key as FilterKeys];
        const currentCheckboxesWithFilterKey: IFilterCheckboxItem[] =
            currentCheckboxes?.map((c: MenuItemCheckboxProps) => ({
                ...c,
                filterKey: key,
            })) || [];
        selectedCheckboxValues.push(...currentCheckboxesWithFilterKey);
    });

    // Pushes the Status values 'Active and Deactivated to the end"
    const filteredChips = removeDuplicates(selectedCheckboxValues).sort((a, b) => {
        if (a.value === 'active' || a.value === 'inactive') return 1;
        if (b.value === 'active' || b.value === 'inactive') return -1;
        return 0;
    });

    const onRemoveFilter = (cb: IFilterCheckboxItem) => {
        selectedCheckboxValues.forEach((item: IFilterCheckboxItem) => {
            if (item.label.toLowerCase() === cb.label.toLowerCase()) {
                removeSearchParams(item.filterKey as keyof IFilter, item.value);
            }
        });
    };

    return (
        <SelectedFiltersBox>
            {filteredChips &&
                filteredChips.map((cb: IFilterCheckboxItem) => (
                    <FilterChip onDelete={() => onRemoveFilter(cb)} key={cb.key} color="orange" label={cb.label} />
                ))}
        </SelectedFiltersBox>
    );
};

export default SelectedFilters;
