import { SyntheticEvent, useState } from 'react';

const useMenu = () => {
    const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);
    const [selectedIndex, setSelectedIndex] = useState<number | null>(null);
    const open = Boolean(anchorEl);

    const handleClick = (event: SyntheticEvent<HTMLElement>) => {
        setAnchorEl(event.currentTarget);
    };

    const handleClose = () => {
        setAnchorEl(null);
    };

    const handleClickMenuItem = (index: number) => {
        setSelectedIndex(index);
    };

    const handleClickMenuItemClose = (index: number) => {
        setSelectedIndex(index);
        setAnchorEl(null);
    };

    return {
        anchorEl,
        selectedIndex,
        open,
        handleClick,
        handleClose,
        handleClickMenuItem,
        handleClickMenuItemClose,
    };
};

export default useMenu;
