import { useSearchParams } from 'react-router-dom';
import { IFilter } from '../types';

const useQueryParams = () => {
    const [searchParams, setSearchParams] = useSearchParams();
    const filterKeys: (keyof IFilter)[] = ['category', 'subCategory', 'status', 'brand', 'search', 'classification'];

    const clearSearchParams = () => {
        filterKeys.forEach((key: keyof IFilter) => {
            searchParams.delete(key);
        });
        setSearchParams(searchParams);
    };

    const getFiltersFromSearchParams = (): IFilter => {
        const filters: IFilter = {
            search: searchParams.get('search') ? searchParams.get('search')!.replace(/\+/g, ' ').split(',') : [],
        };

        filterKeys.forEach((key) => {
            const paramValue = searchParams.get(key);
            if (paramValue) {
                filters[key] = paramValue.split(',');
            } else {
                filters[key] = [];
            }
        });

        return filters;
    };

    const updateSearchParams = (filterKey: keyof IFilter, value: string[] | undefined) => {
        const filterValues: string | undefined = value && value.length ? value.join(',') : undefined;
        if (filterValues) {
            searchParams.set(filterKey, filterValues);
        } else {
            searchParams.delete(filterKey);
        }
        setSearchParams(searchParams);
    };

    const removeSearchParams = (filterKey: keyof IFilter, value: string) => {
        const currentFilterValues: string | null = searchParams.get(filterKey);
        const filterValues: string | undefined = currentFilterValues
            ?.split(',')
            .filter((f: string) => f !== value)
            .join(',');
        if (filterValues) {
            searchParams.set(filterKey, filterValues);
        } else {
            searchParams.delete(filterKey);
        }
        setSearchParams(searchParams);
    };

    return {
        searchParams,
        clearSearchParams,
        getFiltersFromSearchParams,
        removeSearchParams,
        updateSearchParams,
    };
};

export default useQueryParams;
