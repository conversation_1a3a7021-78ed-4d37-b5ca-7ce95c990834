import React, { useEffect, useMemo, useState } from 'react';
import { Button, MenuItemCheckboxProps } from '@treez-inc/component-library';
import { Box, Autocomplete, GlobalStyles, css } from '@mui/material/';
import styled from '@emotion/styled';
import FilterDropdown from './FilterDropdown';
import { IFilterState, IFilterComponent, FilterKeys } from './types';
import useQueryParams from './hooks/useQueryParams';
import { alphabeticalAscOrder, isEmpty } from '../../utils/common';
import useDebounce from './hooks/useDebounce';
import { DEBOUNCE_TIME } from '../../utils/constants';
import SelectedFilters from './SelectedFilters';

const FilterComponentBox = styled(Box)(() => ({
    position: 'relative',
    paddingLeft: '1.25em',
    paddingRight: '1.25em',
    paddingTop: '1.25em',
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    gap: '0.5em',
}));

const StyledFilterBox = styled(Box)({
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    flexWrap: 'wrap',
    gap: '1em',
});

const DropdownFilterBox = styled(Box)({
    columnGap: '0.5em',
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
    gap: '.5em',
});

export const StyledAutoComplete = styled(Autocomplete)(() => ({
    border: '0.094rem solid #BFBFBF',
    borderRadius: '0.75em',

    '& .MuiAutocomplete-inputRoot': {
        minWidth: '10.5em',
        maxHeight: '1.95em',
    },

    '& .MuiFormLabel-root': {
        fontSize: '0.875rem',
        lineHeight: '1.25rem',
        top: '-0.5em',
        color: 'black !important',
    },

    '& .MuiInputLabel-shrink': {
        color: 'black !important',
    },

    '& .MuiFormControl-root': {
        boxSizing: 'border-box',
        fontSize: '0.875rem',
        lineHeight: '1.25rem',
        color: 'black',
        padding: '0 0 0.25em 0 !important',
    },

    '& .MuiInputBase-input': {
        padding: '0 0 1em 0.45em !important',
    },

    '& .MuiOutlinedInput-notchedOutline': {
        border: 'none',
    },
}));

const ChipContainer = styled(Box)(() => ({
    display: 'flex',
    alignItems: 'center',
    gap: '0.5em',
    flexWrap: 'wrap',
    marginRight: '7em',

    '@media (max-width: 430px)': {
        marginBottom: '3em',
    },
}));

const ButtonContainer = styled(Box)({
    position: 'absolute',
    right: '0',
    bottom: '0',
});

const FilterComponent: React.FC<IFilterComponent> = (props) => {
    const { brandData, categoryData, statusData, subCategories, classifications, onFilterChange, onSearchTermChange } =
        props;

    const [filterState, setFilterState] = useState<IFilterState>({});

    const sortedBrandData = alphabeticalAscOrder(brandData, 'label');
    const sortedCategoryData = alphabeticalAscOrder(categoryData, 'label');
    const sortedSubCategoryData = alphabeticalAscOrder(subCategories, 'label');

    const { searchParams, clearSearchParams, getFiltersFromSearchParams, updateSearchParams, removeSearchParams } =
        useQueryParams();

    const urlFilters = useMemo(() => getFiltersFromSearchParams(), [searchParams]);
    const debouncedSearchTerm = useDebounce(filterState.search?.join(' ') || '', DEBOUNCE_TIME);

    useEffect(() => {
        const fromEditPage = localStorage.getItem('fromEditPage');
        const currentURL = window.location.href;

        if (fromEditPage === 'true' && !currentURL.includes('pricing')) {
            const cachedFilters = localStorage.getItem('productControlFilters');
            if (cachedFilters) {
                const parsedFilters = JSON.parse(cachedFilters);
                Object.entries(parsedFilters).forEach(([key, value]) => {
                    updateSearchParams(key as keyof IFilterState, value as string[]);
                });
            }
            localStorage.removeItem('fromEditPage');
        }
    }, []);

    const getSelectedCheckboxes = (filterKey: FilterKeys, values: string[] | undefined) => {
        switch (filterKey) {
            case 'category': {
                return categoryData.filter((cat: MenuItemCheckboxProps) => values?.includes(cat.value));
            }
            case 'status': {
                return statusData.filter((s: MenuItemCheckboxProps) => values?.includes(s.value));
            }
            case 'brand': {
                return brandData.filter((b: MenuItemCheckboxProps) => values?.includes(b.value));
            }
            case 'subCategory': {
                return subCategories.filter((b: MenuItemCheckboxProps) => values?.includes(b.value));
            }
            case 'classification': {
                return classifications?.filter((b: MenuItemCheckboxProps) => values?.includes(b.value));
            }
            default:
                return [];
        }
    };

    useEffect(() => {
        // set "status" query param to active on page load to show only active products initially
        const areQueryParamsEmpty = Object.values(urlFilters).every(isEmpty);
        if (areQueryParamsEmpty) {
            updateSearchParams('status', ['active']);
        }
    }, []);

    useEffect(() => {
        onSearchTermChange(debouncedSearchTerm?.trim() || '');
    }, [debouncedSearchTerm]);

    useEffect(() => {
        const { search = [], ...filters } = urlFilters;
        setFilterState((prev: IFilterState) => ({
            ...prev,
            search,
        }));
        Object.entries(filters).forEach(([key, value]) => {
            const selectedCheckboxes: MenuItemCheckboxProps[] | undefined = getSelectedCheckboxes(
                key as FilterKeys,
                value as string[],
            );
            setFilterState((prev: IFilterState) => ({
                ...prev,
                [key]: selectedCheckboxes,
            }));
        });
        onFilterChange(filters);
    }, [urlFilters]);

    return (
        <FilterComponentBox>
            <GlobalStyles
                styles={css`
                    [data-testid='clear-filters-button'] {
                        font-size: 0.9rem !important;
                        padding: 0.125em 0 0 0.5em !important;
                        min-height: auto !important;
                        height: 2.25em !important;
                        min-width: 8em !important;
                        border-radius: 0.75em !important;
                        flex-direction: row-reverse !important;
                        align-items: center !important;
                        gap: 0.25em;
                        font-weight: normal !important;
                        margin-bottom: 0.125em !important;
                    }
                `}
            />
            <StyledFilterBox>
                <DropdownFilterBox>
                    <FilterDropdown
                        chipId="category-dropdown-chip"
                        filterKey="category"
                        label="Category"
                        menuId="category-checkbox"
                        values={sortedCategoryData}
                        filterState={filterState}
                        updateSearchParams={updateSearchParams}
                        searchField={false}
                    />
                    <FilterDropdown
                        chipId="subCategory-dropdown-chip"
                        filterKey="subCategory"
                        label="Subcategory"
                        menuId="subCategory-checkbox"
                        values={sortedSubCategoryData}
                        filterState={filterState}
                        updateSearchParams={updateSearchParams}
                    />
                    <FilterDropdown
                        chipId="brand-dropdown-chip"
                        filterKey="brand"
                        label="Brand"
                        menuId="brand-checkbox"
                        values={sortedBrandData}
                        filterState={filterState}
                        updateSearchParams={updateSearchParams}
                    />
                    <FilterDropdown
                        chipId="classification-dropdown-chip"
                        filterKey="classification"
                        label="Classification"
                        menuId="classification-checkbox"
                        values={classifications}
                        filterState={filterState}
                        updateSearchParams={updateSearchParams}
                        searchField={false}
                    />
                    <FilterDropdown
                        chipId="status-dropdown-chip"
                        filterKey="status"
                        label="Status"
                        menuId="status-checkbox"
                        values={statusData}
                        filterState={filterState}
                        updateSearchParams={updateSearchParams}
                        searchField={false}
                    />
                </DropdownFilterBox>
                <ChipContainer>
                    <SelectedFilters filterState={filterState} removeSearchParams={removeSearchParams} />
                </ChipContainer>
            </StyledFilterBox>
            <ButtonContainer>
                {Object.values(urlFilters).some((value) => Array.isArray(value) && value.length > 0) && (
                    <Button
                        onClick={() => clearSearchParams()}
                        label="Clear Filters"
                        variant="secondary"
                        small={false}
                        iconName="Close"
                        testId="clear-filters-button"
                    />
                )}
            </ButtonContainer>
        </FilterComponentBox>
    );
};

export default FilterComponent;
