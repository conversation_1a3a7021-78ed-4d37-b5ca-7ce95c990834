import { IconButtonProps, MenuItemCheckboxProps } from '@treez-inc/component-library';

type IconName = IconButtonProps['iconName'];

export interface IFilter {
    search?: string[];
    category?: string[];
    subCategory?: string[];
    status?: string[];
    brand?: string[];
    classification?: string[];
}

export type FilterKeys = keyof Omit<IFilter, 'search'>;

export type IFilterState = {
    [K in FilterKeys]?: MenuItemCheckboxProps[];
} & { search?: string[] };

export interface IFilterCheckboxItem extends MenuItemCheckboxProps {
    filterKey: string;
}

export interface IProductCategoryFilter {
    iconName: IconName;
    displayName: string;
    categoryId: string;
}

export interface IFilterComponent {
    brandData: MenuItemCheckboxProps[];
    categoryData: MenuItemCheckboxProps[];
    statusData: MenuItemCheckboxProps[];
    subCategories: MenuItemCheckboxProps[];
    classifications: MenuItemCheckboxProps[];
    onFilterChange: (newState: IFilter) => void;
    onSearchTermChange: (search: string) => void;
}

export interface IFilterDropdown {
    filterState: IFilterState;
    chipId: string;
    filterKey: FilterKeys;
    label: string;
    menuId: string;
    values: MenuItemCheckboxProps[];
    searchField?: boolean;
    updateSearchParams: (filterKey: keyof IFilter, value: string[] | undefined) => void;
}
