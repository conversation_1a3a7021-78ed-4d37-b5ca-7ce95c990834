import React from 'react';
import { Backdrop, styled } from '@mui/material';
import { CircularProgress } from '@treez-inc/component-library';

const StyledBackDrop = styled(Backdrop)(({ theme }) => ({
    color: theme.palette.green06.main,
    zIndex: theme.zIndex.tooltip + 1,
}));

interface ILoader {
    loading: boolean;
}

const Loader: React.FC<ILoader> = ({ loading }) => (
    <StyledBackDrop data-testid="backdrop-loader" open={loading}>
        <CircularProgress color="green06" />
    </StyledBackDrop>
);

export default Loader;
