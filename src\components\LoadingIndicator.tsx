import React from 'react';
import { CircularProgress } from '@treez-inc/component-library';

interface LoadingIndicatorProps {
    isLoading?: boolean;
}
const LoadingIndicator = ({ isLoading }: LoadingIndicatorProps) => {
    if (!isLoading) {
        return null;
    }

    return (
        <div style={{ position: 'absolute', zIndex: 100, top: '50%', left: '50%', transform: 'translate(-50%, -50%)' }}>
            <CircularProgress />
        </div>
    );
};

export default LoadingIndicator;
