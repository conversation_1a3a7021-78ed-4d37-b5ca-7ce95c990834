import React from 'react';
import { Theme, useTheme } from '@mui/material/styles';
import { Box, Chip, FormControl, InputLabel, MenuItem, OutlinedInput, Select, SelectChangeEvent } from '@mui/material';
import { useFormContext } from 'react-hook-form';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import CloseIcon from '@mui/icons-material/Close';
import { allColors } from '@treez-inc/component-library';
import ValidationErrorBox from './ValidationErrorBox';
import { MenuItemsProps } from '../interfaces/ProductProps';

const ITEM_HEIGHT = 48;
const ITEM_PADDING_TOP = 8;

const MultiSelectChip = React.forwardRef(
    ({ label, menuItems, onSelectChange, onSelectBlur, value, dataTestId }: any, ref) => {
        const theme = useTheme();
        const methods = useFormContext();

        const MenuProps = {
            PaperProps: {
                style: {
                    backgroundColor: allColors.grey02.main,
                    fontSize: '15px',
                    maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
                },
            },
        };

        const handleChange = (event: SelectChangeEvent<string[]>) => {
            // On autofill we get a stringified value.
            const updatedMenuItems =
                typeof event.target.value === 'string' ? event.target.value.split(',') : event.target.value;
            onSelectChange(updatedMenuItems);
        };

        function getMenuItemStyles(menuItemName: string, selectedMenuValues: readonly string[], menuTheme: Theme) {
            return {
                fontSize: '15px',
                fontFamily: 'Roboto',
                fontStyle: 'normal',
                fontWeight:
                    selectedMenuValues.indexOf(menuItemName) === -1 ? 400 : menuTheme.typography.fontWeightMedium,
                lineHeight: '23px',
            };
        }

        const handleDelete = (e: React.MouseEvent, menuItem: string) => {
            e.preventDefault();
            onSelectChange(value.filter((item: string) => item !== menuItem));
        };

        const clearSelectedValues = () => {
            onSelectChange([]);
        };

        return (
            <>
                <Box ref={ref}>
                    <Chip
                        disabled={!value?.length || !menuItems?.length}
                        style={{ float: 'right' }}
                        clickable
                        onClick={clearSelectedValues}
                        label="Clear"
                    />
                    <FormControl
                        sx={{
                            background: allColors.grey03.main,
                            borderRadius: '19px',
                            width: '100%',
                        }}
                    >
                        <InputLabel id="demo-mutiple-chip-checkbox-label" data-testid="multiple-chip-checkbox-label">
                            {label}
                        </InputLabel>
                        <Select
                            style={{ borderRadius: '19px' }}
                            labelId="demo-mutiple-chip-checkbox-label"
                            id="demo-mutiple-chip-checkbox"
                            data-testid={`multiple-chip-${dataTestId}`}
                            disabled={!menuItems.length}
                            multiple
                            value={value as string[]}
                            input={<OutlinedInput id="select-multiple-chip" label="Size" />}
                            onChange={handleChange}
                            onBlur={onSelectBlur}
                            MenuProps={MenuProps}
                            IconComponent={KeyboardArrowDownIcon}
                            renderValue={(selected) => (
                                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                                    {(selected as string[]).map((item: string) => (
                                        <Chip
                                            key={item}
                                            label={item}
                                            clickable
                                            style={{ backgroundColor: allColors.brown.main }}
                                            deleteIcon={
                                                <CloseIcon
                                                    style={{ color: 'black' }}
                                                    onMouseDown={(event) => event.stopPropagation()}
                                                />
                                            }
                                            onDelete={(e) => handleDelete(e, item)}
                                        />
                                    ))}
                                </Box>
                            )}
                        >
                            {menuItems?.map((menuItem: MenuItemsProps) => (
                                <MenuItem
                                    key={menuItem.displayName}
                                    value={menuItem.displayValue}
                                    style={getMenuItemStyles(menuItem.displayName, (value as string[]) || [], theme)}
                                    data-testid={`multiple-chip-item-${dataTestId}-${menuItem.displayName}`}
                                >
                                    {menuItem.displayName}
                                </MenuItem>
                            ))}
                        </Select>
                    </FormControl>
                </Box>
                {methods.formState.errors.sizeVariant && !value.length && (
                    <ValidationErrorBox fieldName="size-variant">Select a size variant</ValidationErrorBox>
                )}
            </>
        );
    },
);

export default MultiSelectChip;
