import React, { useState } from 'react';
import { <PERSON>complete, TextField, createFilterOptions, createTheme, ThemeProvider } from '@mui/material/';
import { allColors, CircularProgress } from '@treez-inc/component-library';
import ConfirmationModal from './ConfirmationModal';
import { MenuItemsProps } from '../interfaces/ProductProps';
import useSnackbarContext from '../hooks/snackbar/useSnackbarContext';
import { convertToNumber } from '../utils/common';
import AddNewOption, { CreateOptionContainer, NewDropdownOption, NoOptions } from './AddNewOption';

const filter = createFilterOptions<MenuItemsProps>();

interface MultiSelectWithSearchProps {
    // component label
    label?: string;
    // list of items to show in the dropdown
    menuItems: MenuItemsProps[];
    onChange: any;
    onBlur: any;
    // value of the component
    value: MenuItemsProps[];
    // disables the component
    isDisabled?: boolean;
    // Has all the necessary data to create a new option
    dataForNewOption?: any;
    // mutation function that creates a new option in the backend
    mutation?: any;
    // createPermissions includes the user permissions needed to create a new option in the dropdown
    createPermissions?: string[];
    // isValueANumber option is used in case if we want to enforce dropdown value to be a number
    // createNewOption option is used to check if we need to use the "create a new option" feature in this component
    options?: { isValueANumber?: boolean; createNewOption?: boolean };
}

// component
const MultiSelectWithSearch = React.forwardRef(
    (
        {
            label,
            menuItems: menuData,
            onChange,
            onBlur,
            value,
            isDisabled = false,
            dataForNewOption,
            mutation,
            createPermissions,
            options = { isValueANumber: false, createNewOption: false },
        }: MultiSelectWithSearchProps,
        ref,
    ) => {
        // initialize state
        const [loading, setLoading] = useState<boolean>(false);
        const [isAutoCompleteOpen, setIsAutoCompleteOpen] = useState(false);
        const [isHovered, setIsHovered] = useState(false);
        const [open, toggleOpen] = useState<boolean>(false);
        const [menuItems, setMenuItems] = useState<MenuItemsProps[]>(menuData);
        const [newInputValue, setNewInputValue] = useState('');
        const addOption: MenuItemsProps = { displayName: 'Add', displayValue: 'Add' };

        const autoCompleteTheme = createTheme({
            components: {
                MuiAutocomplete: {
                    styleOverrides: {
                        root: {
                            '& .MuiOutlinedInput-root': {
                                border: 'none',
                                '& .MuiOutlinedInput-notchedOutline': {
                                    border: 'none !important',
                                },
                                '&:hover .MuiOutlinedInput-notchedOutline': {
                                    border: 'none !important',
                                },
                                '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                                    border: '2px solid black !important',
                                },
                            },
                        },
                        inputRoot: {
                            borderRadius: '1em',
                            fontFamily: 'Roboto',
                            fontStyle: 'normal',
                            fontWeight: 400,
                            fontSize: '0.938rem',
                            background: allColors.gray.main,
                        },
                        popper: {
                            borderRadius: '4px 4px 17px 17px',
                            boxShadow: '0px 30px 40px rgba(0, 0, 0, 0.12)',
                        },
                        paper: {
                            background: allColors.grey02.main,
                            borderRadius: '4px 4px 17px 17px',
                            boxShadow: '0px 30px 40px rgba(0, 0, 0, 0.12)',
                        },
                        option: {
                            '&:hover': {
                                background: `${allColors.green03.main} !important`,
                            },
                            "&[aria-selected='true']": {
                                background: `${allColors.green03.main} !important`,
                            },
                            fontFamily: 'Roboto',
                            fontStyle: 'normal',
                            fontWeight: 400,
                            fontSize: '0.938rem',
                            lineHeight: '24px',
                        },
                    },
                },
                MuiInputLabel: {
                    styleOverrides: {
                        root: {
                            color: 'gray',
                            '&.Mui-focused': {
                                color: `${allColors.green10.main}!important`,
                            },
                        },
                    },
                },
            },
        });

        // dialog methods to open and close dialog
        const showConfirmation = () => toggleOpen(true);
        const closeConfirmation = () => toggleOpen(false);

        // snackbar initialization
        const { setSnackbar } = useSnackbarContext();

        // transform the value based on type
        const getValue = (currentValue: string | any[]) => {
            const current = typeof currentValue === 'string' ? currentValue.split(',') : currentValue;
            return current;
        };

        // save new option
        const saveNewOption = async () => {
            // call the API for create mutation
            const data = await mutation.mutateAsync([
                {
                    name: newInputValue,
                    attributeCategoryId: dataForNewOption?.attributeCategoryId,
                },
            ]);
            const newOption = data?.data?.[0];

            const currentValue = {
                displayName: newOption?.name,
                displayValue: newOption?.id,
            };

            // set new value in the dropdown
            onChange([...value, currentValue]);

            // set new menu items in options
            setMenuItems((prevMenuItems: MenuItemsProps[]) => {
                const values = prevMenuItems || [];
                return [...values, currentValue];
            });
        };

        // handle mouse enter and mouse leave events for entered value
        const handleMouseEnter = () => {
            setIsHovered(true);
        };
        const handleMouseLeave = () => {
            setIsHovered(false);
        };
        // custom styles for entered value
        const addNewInputStyles = {
            cursor: 'pointer',
            borderRadius: '10px',
            backgroundColor: isHovered ? `${allColors.green03.main}` : 'none',
        };
        // add newly entered value as a selection in the input box of autocomplete
        const addNewInputAsSelection = () => {
            // check if entered value needs to be a number
            if (options.isValueANumber) {
                const newInputNumber = convertToNumber(newInputValue);
                if (newInputNumber === null) {
                    setNewInputValue('');
                    return;
                }
            }
            // check if entered value is a duplicate
            const isDuplicate = value.findIndex((item: MenuItemsProps) => item.displayName === newInputValue) !== -1;
            if (!isDuplicate) {
                const newValue: MenuItemsProps = { displayName: newInputValue, displayValue: newInputValue };
                onChange([...value, newValue]);
            }
            setIsAutoCompleteOpen(false);
            setNewInputValue('');
        };

        // form submission
        const handleSubmit = async () => {
            try {
                setLoading(true);
                closeConfirmation();

                await saveNewOption();

                setSnackbar({
                    message: `New ${label} has been added.`,
                    severity: 'info',
                    iconName: 'Success',
                });
                setNewInputValue('');
                setLoading(false);
            } catch (error) {
                setSnackbar({
                    message: `An error has occurred while processing your request.`,
                    severity: 'error',
                    iconName: 'Error',
                });
                setNewInputValue('');
                setLoading(false);
            }
        };

        const handleClose = () => {
            closeConfirmation();
            setNewInputValue('');
        };

        return (
            <>
                {loading && <CircularProgress />}
                {!loading && (
                    <ThemeProvider theme={autoCompleteTheme}>
                        <Autocomplete
                            autoHighlight
                            clearOnBlur
                            disabled={isDisabled}
                            filterOptions={(__options, params) => {
                                const filterResult = filter(menuItems, params);
                                // when no value is typed in text box, return all menu items
                                if (!params.inputValue) {
                                    return filterResult;
                                }
                                // when value is typed in text box, and there are matching results,
                                // return results along with 'Add' option to show Add button (see renderOption prop)
                                // in case the user wants to add that specific typed value as a new option
                                if (params.inputValue && filterResult.length) {
                                    // if there is an exact match, do not allow creation of the same option
                                    const index: number = filterResult.findIndex(
                                        (item: MenuItemsProps) =>
                                            item.displayName.toLowerCase() === params.inputValue.toLowerCase(),
                                    );
                                    if (index >= 0) {
                                        return filterResult;
                                    }
                                    return [...filterResult, addOption];
                                }
                                // otherwise return empty result to show component setup in noOptionsText
                                return [];
                            }}
                            getOptionLabel={(option: MenuItemsProps): string => option?.displayName || ''}
                            handleHomeEndKeys
                            id="multi-select-with-search"
                            loading={loading}
                            multiple
                            noOptionsText={
                                <>
                                    {!newInputValue && (
                                        <NoOptions className="multi-select-no-options">
                                            No Options:{' '}
                                            {options.isValueANumber ? 'Enter a Custom Number' : 'Enter a Custom Value'}
                                        </NoOptions>
                                    )}
                                    {options.createNewOption && newInputValue && (
                                        <AddNewOption
                                            newSelectValue={newInputValue}
                                            permissions={createPermissions as string[]}
                                            showConfirmation={showConfirmation}
                                        />
                                    )}
                                    {!options.createNewOption && newInputValue && (
                                        <CreateOptionContainer
                                            sx={addNewInputStyles}
                                            onMouseEnter={() => handleMouseEnter()}
                                            onMouseLeave={() => handleMouseLeave()}
                                            onClick={addNewInputAsSelection}
                                        >
                                            Custom Option:
                                            <NewDropdownOption>{newInputValue}</NewDropdownOption>
                                        </CreateOptionContainer>
                                    )}
                                </>
                            }
                            open={isAutoCompleteOpen}
                            options={menuItems || []}
                            onBlur={onBlur}
                            onChange={(__event, data: MenuItemsProps[]) => {
                                onChange(data);
                            }}
                            onInputChange={(event, inputValue) => {
                                // do not set value on blur event, because blur event reloads the component and resets the state variable
                                if (event?.type === 'change') {
                                    // removes leading and trailing spaces including empty string values
                                    // using regex and then sets the value in state
                                    setNewInputValue(inputValue.replace(/^\s+|\s+$|\s+(?=\s)/g, ''));
                                }
                            }}
                            onOpen={() => setIsAutoCompleteOpen(true)}
                            onClose={() => setIsAutoCompleteOpen(false)}
                            isOptionEqualToValue={(option, selectedOption) =>
                                option.displayValue === selectedOption.displayValue
                            }
                            ref={ref}
                            renderInput={(params) => (
                                <TextField
                                    sx={{
                                        border: 'ActiveBorder',
                                    }}
                                    {...params}
                                    label={label}
                                />
                            )}
                            renderOption={(props, option, state) => {
                                const key = `menuItem-${state.index}-${option.displayValue}`;
                                if (option.displayValue === 'Add') {
                                    // eslint-disable-next-line react/prop-types
                                    const { style, className } = props;
                                    const updatedStyles = {
                                        ...style,
                                        paddingTop: '10px',
                                        paddingBottom: '0px',
                                    };
                                    return (
                                        <li
                                            className={className}
                                            style={updatedStyles}
                                            data-testid="autocomplete-option"
                                            key={key}
                                        >
                                            <AddNewOption
                                                newSelectValue={newInputValue}
                                                permissions={createPermissions as string[]}
                                                showConfirmation={showConfirmation}
                                            />
                                        </li>
                                    );
                                }
                                return (
                                    <li {...props} data-testid="autocomplete-option" key={key}>
                                        {option.displayName}
                                    </li>
                                );
                            }}
                            selectOnFocus
                            sx={{ width: '100%' }}
                            value={getValue(value)}
                        />
                    </ThemeProvider>
                )}
                <ConfirmationModal
                    displayValue={newInputValue}
                    handleClose={handleClose}
                    handleSubmit={handleSubmit}
                    label={label?.replace('*', '').trim()}
                    open={open}
                />
            </>
        );
    },
);

export default MultiSelectWithSearch;
