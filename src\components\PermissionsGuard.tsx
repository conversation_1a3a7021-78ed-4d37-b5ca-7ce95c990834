import React, { ReactNode } from 'react';
import { useUserPermissionsContext } from '../permissions/userPermissions.context';

interface GuardProps {
    children: ReactNode;
    permissions: string[];
}

const PermissionsGuard = ({ children, permissions }: GuardProps) => {
    const { validateUserPermissions } = useUserPermissionsContext();

    if (validateUserPermissions(permissions)) {
        // eslint-disable-next-line react/jsx-no-useless-fragment
        return <>{children}</>;
    }
    // eslint-disable-next-line react/jsx-no-useless-fragment
    return <></>;
};

export default PermissionsGuard;
