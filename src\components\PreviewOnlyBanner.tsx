import React from 'react';
import { Box, styled } from '@mui/material';
import { Icon, Tooltip, convertPxToRem } from '@treez-inc/component-library';

interface PreviewOnlyBannerProps {
    'data-testid'?: string;
}

const PreviewOnlyInfoIcon = styled(Box)(() => ({
    display: "flex",
    marginLeft: convertPxToRem(8),
}));

const StyledPreviewOnlyBanner = styled(Box)(({ theme }) => ({
    backgroundColor: '#F9C548',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
    ...theme.typography.body2,
}));

const PreviewOnlyBanner: React.FC<PreviewOnlyBannerProps> = ({ 'data-testid': dataTestId }) => {
    return (
        <StyledPreviewOnlyBanner data-testid={dataTestId}>
            You are in preview only mode
            <PreviewOnlyInfoIcon>
                <Tooltip
                    placement="right"
                    themeColor="dark"
                    title={`You're viewing the new product catalog, which is not yet active for your store(s). Your current products in Treez remain unchanged. This page is for preview only-no changes will apply yet.`}
                    variant="multiRow"
                >
                    <Icon iconName="InfoOutlined" />
                </Tooltip>
            </PreviewOnlyInfoIcon>
        </StyledPreviewOnlyBanner>
    );
};

export default PreviewOnlyBanner;
