import React, { ChangeEvent, useEffect, useState } from 'react';
import {
    styled,
    Radio as MUIRadio,
    RadioProps as MUIRadioProps,
    RadioGroup,
    RadioGroupProps as MUIRadioGroupProps,
    FormControlLabel as MUIFormControlLabel,
    FormLabel as MUIFormLabel,
    FormControl,
} from '@mui/material';
import { convertPxToRem } from '@treez-inc/component-library';

export interface RadioButtonProps
    extends Pick<MUIRadioProps, 'disabled' | 'required'>,
        Pick<MUIRadioGroupProps, 'value' | 'onChange' | 'defaultValue' | 'name'> {
    /** Error will display if a required selection is missing. Error cannot be displayed without a radio group label */
    error?: boolean;
    /** Hides the radio group label if true.  */
    hideGroupLabel?: boolean;
    /** The text label applied to the radio button group */
    label: string;
    /** An array of options for radio buttons */
    radioButtons: Array<{ value: string; label?: string }>;
    /** Displays radios horizontally in a row instead of a column layout */
    row?: boolean;
    /** Test id that can be used for targeting elements in tests: data-testid={testId} */
    testId?: string;
}

const StyledFormLabel = styled(MUIFormLabel)(({ theme }) => ({
    ...theme.typography.largeTextStrong,
    color: theme.palette.primaryBlackText.main,
    '&.Mui-error': {
        color: theme.palette.error.main,
    },
    '&.Mui-focused': {
        color: theme.palette.primaryBlackText.main,
    },
    '&.Mui-disabled': {
        color: theme.palette.disabledText.main,
    },
}));

const StyledFormControlLabel = styled(MUIFormControlLabel, {
    shouldForwardProp: (prop) => prop !== 'disabled',
})<{
    disabled: boolean;
}>(({ theme, disabled }) => ({
    // label
    '> span:nth-of-type(2)': {
        ...theme.typography.largeText,
        '&.Mui-disabled': {
            color: theme.palette.disabledText.main,
        },
    },
    '&.MuiRadio-root': {
        '> span': {
            border: disabled
                ? `${convertPxToRem(1.5)} solid ${theme.palette.grey04.main}`
                : `${convertPxToRem(1.5)} solid ${theme.palette.grey05.main}`,
        },
    },
}));

const StyledRadio = styled(MUIRadio)({
    '&.MuiRadio-root': {
        '&:hover': {
            backgroundColor: 'transparent',
        },
    },
});

const RadioIcon = styled('span')(({ theme }) => ({
    backgroundColor: theme.palette.primaryWhite.main,
    border: `${convertPxToRem(1.5)} solid ${theme.palette.grey05.main}`,
    borderRadius: '50%',
    boxSizing: 'border-box',
    height: convertPxToRem(20),
    width: convertPxToRem(20),
    '& .Mui-checked': {
        border: `${convertPxToRem(1.5)} solid ${theme.palette.sativaGreen.main}`,
    },
    'input:hover ~ &': {
        border: `${convertPxToRem(1.5)} solid ${theme.palette.grey07.main}`,
    },
    'input:disabled ~ &': {
        backgroundColor: theme.palette.grey03.main,
        border: `${convertPxToRem(1.5)} solid ${theme.palette.grey04.main}`,
    },
    'input:disabled:hover ~ &': {
        border: `${convertPxToRem(1.5)} solid ${theme.palette.grey04.main}`,
    },
    '.Mui-focusVisible &': {
        border: `${convertPxToRem(1.5)} solid ${theme.palette.primaryBlack.main}`,
    },
}));

const RadioCheckedIcon = styled('span')(({ theme }) => ({
    backgroundColor: theme.palette.sativaGreen.main,
    border: `${convertPxToRem(1.5)} solid ${theme.palette.sativaGreen.main}`,
    borderRadius: '50%',
    boxSizing: 'border-box',
    height: convertPxToRem(20),
    width: convertPxToRem(20),
    '&:before': {
        display: 'block',
        backgroundColor: theme.palette.primaryBlack.main,
        borderRadius: '50%',
        height: convertPxToRem(6),
        width: convertPxToRem(6),
        marginTop: '32%',
        marginLeft: '32%',
        content: '""',
    },
    '.Mui-focusVisible': {
        outline: `${convertPxToRem(1.5)} solid ${theme.palette.primaryBlack.main}`,
    },
}));

export const RadioButton = ({
    defaultValue,
    disabled = false,
    error = false,
    hideGroupLabel = false,
    name,
    value,
    label,
    onChange,
    radioButtons = [],
    required,
    row = false,
    testId,
}: RadioButtonProps) => {
    const [selectedValue, setSelectedValue] = useState<string | undefined>(
        value !== undefined ? value?.toString() : undefined,
    );

    useEffect(() => {
        if (selectedValue !== value?.toString()) setSelectedValue(value?.toString());
    }, [value]);

    const handleChange = (event: ChangeEvent<HTMLInputElement>, eventValue: string) => {
        setSelectedValue(eventValue);
        if (onChange) {
            onChange(event, eventValue);
        }
    };

    return (
        <FormControl disabled={disabled} error={error} required={required}>
            {!hideGroupLabel && (
                <StyledFormLabel id={label} data-testid={testId && `label-${testId}`}>
                    {label}
                </StyledFormLabel>
            )}
            <RadioGroup
                aria-labelledby={label}
                data-testid={testId}
                {...(value !== undefined ? { value: selectedValue } : { defaultValue })}
                name={name}
                onChange={handleChange}
                row={row}
            >
                {radioButtons.map((button: { value: string; label?: string }) => (
                    <StyledFormControlLabel
                        key={button.value}
                        control={
                            <StyledRadio
                                disableRipple
                                checkedIcon={<RadioCheckedIcon />}
                                icon={<RadioIcon />}
                                inputProps={{
                                    'aria-label': button.label,
                                }}
                            />
                        }
                        label={button.label}
                        value={button.value}
                        disabled={disabled}
                    />
                ))}
            </RadioGroup>
        </FormControl>
    );
};
