import React, { useState } from 'react';
import { Autocomplete, createFilterOptions, createTheme, TextField, ThemeProvider } from '@mui/material/';
import { allColors, CircularProgress, InputProps } from '@treez-inc/component-library';
import { Controller, useFormContext } from 'react-hook-form';
import { MenuItemsProps } from '../interfaces/ProductProps';
import ConfirmationModal from './ConfirmationModal';
import useSnackbarContext from '../hooks/snackbar/useSnackbarContext';
import AddNewOption, { NoOptions } from './AddNewOption';

const filter = createFilterOptions<MenuItemsProps>();

interface SelectWithSearchProps extends InputProps {
    label: string;
    menuItems: MenuItemsProps[];
    refetch: any;
    name: string;
    createMutation: any;
    dataForNewOption: Record<string, string>;
    createPermissions: string[];
    isRequired?: boolean;
}

/**
 * @param {label} label - label to display in the dropdown
 * @param {menuItems} menuItems - list of menu items to show in the dropdown list
 * @param {refetch} refetch - Refetch function to retrieve latest data after creating a new option
 * @param {name} name - Name of the field to register in React Hook Form
 * @param {createMutation} createMutation - Create mutation function used to create a new option
 * @param {dataForNewOption} dataForNewOption - Pass data in object format needed to create a new option, except name and organizationId. If no data is needed, pass an empty object
 * @param {createPermissions} createPermissions - Permissions needed to create a new option
 */
const SelectWithSearch = ({
    label,
    menuItems,
    refetch,
    name,
    createMutation,
    dataForNewOption,
    createPermissions,
    disabled,
    isRequired,
}: Omit<SelectWithSearchProps, 'onChange'>) => {
    const methods = useFormContext();
    const [loading, setLoading] = useState<boolean>(false);
    const [open, toggleOpen] = useState<boolean>(false);
    const showConfirmation = () => toggleOpen(true);
    const closeConfirmation = () => toggleOpen(false);
    const getCurrentValue = (value: string) => menuItems.find((item: MenuItemsProps) => item.displayValue === value);
    const [newSelectValue, setNewSelectValue] = useState('');
    const { setSnackbar } = useSnackbarContext();
    const addOption: MenuItemsProps = { displayName: 'Add', displayValue: 'Add' };

    const autoCompleteTheme = createTheme({
        components: {
            MuiAutocomplete: {
                styleOverrides: {
                    root: {
                        '& .MuiOutlinedInput-root': {
                            border: 'none',
                            '& .MuiOutlinedInput-notchedOutline': {
                                border: 'none !important',
                            },
                            '&:hover .MuiOutlinedInput-notchedOutline': {
                                border: 'none !important',
                            },
                            '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                                border: '2px solid black !important',
                            },
                        },
                    },
                    inputRoot: {
                        borderRadius: '1em',
                        fontFamily: 'Roboto',
                        fontStyle: 'normal',
                        fontWeight: 400,
                        fontSize: '0.938rem',
                        background: allColors.gray.main,
                    },
                    popper: {
                        borderRadius: '4px 4px 17px 17px',
                        boxShadow: '0px 30px 40px rgba(0, 0, 0, 0.12)',
                    },
                    paper: {
                        background: allColors.grey02.main,
                        borderRadius: '4px 4px 17px 17px',
                        boxShadow: '0px 30px 40px rgba(0, 0, 0, 0.12)',
                    },
                    option: {
                        '&:hover': {
                            background: `${allColors.green03.main} !important`,
                        },
                        "&[aria-selected='true']": {
                            background: `${allColors.green03.main} !important`,
                        },
                        fontFamily: 'Roboto',
                        fontStyle: 'normal',
                        fontWeight: 400,
                        fontSize: '0.938rem',
                        lineHeight: '24px',
                    },
                },
            },
            MuiInputLabel: {
                styleOverrides: {
                    root: {
                        color: 'gray',
                        '&.Mui-focused': {
                            color: `${allColors.green10.main}!important`,
                        },
                    },
                },
            },
        },
    });

    const handleSubmit = (onSelectChange: any) => {
        setLoading(true);
        closeConfirmation();
        createMutation
            .mutateAsync([{ name: newSelectValue, ...dataForNewOption }])
            .then(async (result: any) => {
                const newOption = result.data[0];
                onSelectChange(newOption.id);
                // refetch to show latest data in the dropdown
                await refetch();
                // clear loading and new select value
                setNewSelectValue('');
                setLoading(false);
                setSnackbar({
                    message: `${label} added.`,
                    severity: 'info',
                    iconName: 'Success',
                });
            })
            .catch(() => {
                setNewSelectValue('');
                setLoading(false);
                setSnackbar({
                    message: `An error has occurred while processing your request.`,
                    severity: 'error',
                    iconName: 'Error',
                });
            });
    };

    const handleClose = () => {
        closeConfirmation();
        setNewSelectValue('');
    };

    return (
        <div data-testid={`select-with-search-${name?.toLowerCase().replaceAll(' ', '-')}`}>
            {loading && <CircularProgress />}
            {!loading && (
                <Controller
                    control={methods.control}
                    name={name}
                    render={({ field: { onChange, onBlur, value, ref } }) => (
                        <>
                            <ThemeProvider theme={autoCompleteTheme}>
                                <Autocomplete
                                    autoHighlight
                                    clearOnBlur
                                    disabled={disabled}
                                    filterOptions={(__options, params) => {
                                        const filterResult = filter(menuItems, params);
                                        // when no value is typed in text box, return all menu items
                                        if (!params.inputValue) {
                                            return filterResult;
                                        }
                                        // when value is typed in text box, and there are matching results,
                                        // return results along with 'Add' option to show Add button (see renderOption prop)
                                        // in case the user wants to add that specific typed value as a new option
                                        if (params.inputValue && filterResult.length) {
                                            // if there is an exact match, do not allow creation of the same option
                                            const index: number = filterResult.findIndex(
                                                (item: MenuItemsProps) =>
                                                    item.displayName.toLowerCase() === params.inputValue.toLowerCase(),
                                            );
                                            if (index >= 0) {
                                                return filterResult;
                                            }
                                            return [...filterResult, addOption];
                                        }
                                        // otherwise return empty result to show component setup in noOptionsText
                                        return [];
                                    }}
                                    getOptionLabel={(option): string => {
                                        if (typeof option === 'string') {
                                            return option;
                                        }
                                        if (option && option.displayValue) {
                                            return option.displayName;
                                        }
                                        return '';
                                    }}
                                    handleHomeEndKeys
                                    id="select-with-search"
                                    isOptionEqualToValue={(option, selectedOption) =>
                                        option?.displayValue === selectedOption?.displayValue
                                    }
                                    loading={loading}
                                    noOptionsText={
                                        <>
                                            <NoOptions>No Options</NoOptions>
                                            {newSelectValue && (
                                                <AddNewOption
                                                    newSelectValue={newSelectValue}
                                                    permissions={createPermissions}
                                                    showConfirmation={showConfirmation}
                                                />
                                            )}
                                        </>
                                    }
                                    onBlur={onBlur}
                                    onChange={(__event, newValue: MenuItemsProps | null) => {
                                        if (newValue !== null) {
                                            onChange(newValue?.displayValue);
                                        } else {
                                            onChange(newValue);
                                        }
                                    }}
                                    onInputChange={(event, inputValue) => {
                                        // do not set value on blur event, because blur event reloads the component and resets the state variable
                                        if (event?.type === 'change') {
                                            // removes leading and trailing spaces including empty string values
                                            // using regex and then sets the value in state
                                            setNewSelectValue(inputValue.replace(/^\s+|\s+$|\s+(?=\s)/g, ''));
                                        }
                                    }}
                                    options={menuItems}
                                    ref={ref}
                                    renderInput={(params) => (
                                        <TextField
                                            sx={{ border: 'ActiveBorder' }}
                                            {...params}
                                            label={`${label}${isRequired ? '*' : ''}`}
                                        />
                                    )}
                                    renderOption={(props, option, state) => {
                                        const key = `menuItem-${state.index}-${option.displayValue}`;
                                        if (option.displayValue === 'Add') {
                                            const { className, style } = props;
                                            const updatedStyles = {
                                                ...style,
                                                paddingTop: '10px',
                                                paddingBottom: '0px',
                                            };
                                            return (
                                                <li
                                                    className={className}
                                                    style={updatedStyles}
                                                    data-testid="autocomplete-option"
                                                    key={key}
                                                >
                                                    <AddNewOption
                                                        newSelectValue={newSelectValue}
                                                        permissions={createPermissions}
                                                        showConfirmation={showConfirmation}
                                                    />
                                                </li>
                                            );
                                        }
                                        return (
                                            <li {...props} data-testid="autocomplete-option" key={key}>
                                                {option.displayName}
                                            </li>
                                        );
                                    }}
                                    selectOnFocus
                                    sx={{ width: '100%' }}
                                    value={getCurrentValue(value) || null}
                                />
                            </ThemeProvider>
                            <ConfirmationModal
                                displayValue={newSelectValue}
                                handleClose={handleClose}
                                handleSubmit={handleSubmit}
                                onSelectChange={onChange}
                                label={label}
                                open={open}
                            />
                        </>
                    )}
                />
            )}
        </div>
    );
};

export default SelectWithSearch;
