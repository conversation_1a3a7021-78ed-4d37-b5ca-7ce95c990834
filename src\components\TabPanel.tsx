import React from 'react';
import Box from '@mui/material/Box';
import { styled } from '@mui/material/styles';

const TypographyBox = styled(Box)`
    margin: 0;
    font-family: Roboto;
    font-weight: 400;
    font-size: 1rem;
`;
const TabPanel = (props: any) => {
    const { children, value, index } = props;
    return (
        <div role="tabpanel" hidden={value !== index} id={`tabpanel-${index}`} aria-labelledby={`tab-${index}`}>
            {value === index && <TypographyBox>{children}</TypographyBox>}
        </div>
    );
};

export default TabPanel;
