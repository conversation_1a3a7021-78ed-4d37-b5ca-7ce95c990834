import React, { ReactNode } from 'react';
import { Tooltip } from './Tooltip/Tooltip';

interface FormToolTipWrapperProps {
    toolTip?: string;
    children: ReactNode;
}

const ToolTipWrapper = ({ toolTip, children }: FormToolTipWrapperProps) => {
    if (toolTip) {
        return (
            <Tooltip title={toolTip} variant="multiRow">
                <>{children}</>
            </Tooltip>
        );
    }

    return <>{children}</>;
};

export default ToolTipWrapper;
