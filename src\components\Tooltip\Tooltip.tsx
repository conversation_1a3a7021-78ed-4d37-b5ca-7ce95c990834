import React from 'react';
import { styled, Tooltip as MUI<PERSON>ooltip, TooltipProps as MUITooltipProps, alpha } from '@mui/material';

export interface TooltipProps
    extends Pick<
        MUITooltipProps,
        'arrow' | 'children' | 'onClose' | 'onOpen' | 'open' | 'placement' | 'enterDelay' | 'leaveDelay'
    > {
    /** Cursor type when hovering over the child element */
    cursor?: 'help' | 'default';
    /** Tooltip displays a bolded header text with the multiRow variant; does NOT apply to singleRow variant */
    headerText?: string;
    /** Applies dark or light color styles */
    themeColor?: 'dark' | 'light';
    /** The text displayed in the tooltip */
    title: string;
    /** Applies content variant styles */
    variant?: 'singleRow' | 'multiRow' | 'context';
    /** test id used for targeting elements in tests: data-testid={testId} */
    testId?: string;
}
const StyledTooltip = styled(
    ({ className, ...props }: MUITooltipProps) => <MUITooltip {...props} classes={{ popper: className }} />,
    { shouldForwardProp: (prop) => prop !== 'themeColor' },
)<TooltipProps>(({ theme, themeColor }) => ({
    '& .MuiTooltip-tooltip': {
        ...theme.typography.mediumText,
        backgroundColor: themeColor === 'light' ? theme.palette.primaryWhite.main : theme.palette.primaryBlack.main,
        border: themeColor === 'light' ? `1px solid ${theme.palette.grey02.main}` : 'none',
        borderRadius: '0.9375em',
        boxShadow: `0 11 18 -3 rgba(0, 0, 0, 0.17)`,
        color: themeColor === 'light' ? theme.palette.primaryBlackText.main : theme.palette.primaryWhiteText.main,
    },
    '& .MuiTooltip-arrow': {
        color: themeColor === 'light' ? theme.palette.primaryWhite.main : theme.palette.primaryBlack.main,
    },
}));

const StyledSingleRowTooltip = styled(StyledTooltip)({
    height: '2.4375em',
    '& .MuiTooltip-tooltip': {
        maxWidth: '17.75em',
        padding: `0.5em 0.75em 0.625em}`,
    },
});

const StyledMultiRowTooltip = styled(StyledTooltip, {
    shouldForwardProp: (prop) => prop !== 'headerText',
})<{ headerText?: string }>(({ theme, headerText }) => ({
    '& .MuiTooltip-tooltip': {
        maxWidth: '21.25em',
        padding: '0.75em',
        // multiRow title
        '> span': {
            // headerText
            '> p': {
                ...theme.typography.mediumTextStrong,
                display: headerText !== null ? 'block' : 'none',
                margin: 0,
                paddingBottom: '0.25em',
            },
        },
    },
}));

const StyledContextTooltip = styled(StyledTooltip)(({ theme }) => ({
    '& .MuiTooltip-tooltip': {
        ...theme.typography.extraSmallText,
        backgroundColor: alpha(theme.palette.grey09.main, 0.92),
        color: theme.palette.primaryWhiteText.main,
    },
}));

const StyledDiv = styled('div', {
    shouldForwardProp: (prop) => prop !== 'cursor',
})<{ cursor?: string }>(({ cursor }) => ({
    width: '100%',

    ...(cursor === 'help' && {
        '*:hover': {
            cursor: 'help',
        },
    }),
}));

const StyledSpan = styled('span', {
    shouldForwardProp: (prop) => prop !== 'cursor',
})<{ cursor?: string }>(({ cursor }) => ({
    width: '100%',
    ...(cursor === 'help' && {
        cursor: 'help',
    }),
}));

export const Tooltip = ({
    children,
    cursor = 'help',
    enterDelay = 300,
    headerText,
    leaveDelay = 400,
    onClose,
    onOpen,
    open,
    placement = 'bottom',
    themeColor = 'dark',
    testId,
    title,
    variant = 'singleRow',
    ...rest
}: TooltipProps) => {
    const shortenedSingleRowTitle = title.length > 40 ? title.substring(0, 40) : title;

    const multiRowTitle = (
        <span>
            {headerText && <p>{headerText}</p>}
            {title}
        </span>
    ) as React.ReactNode & string;

    return (
        <>
            {variant === 'singleRow' && (
                <StyledSingleRowTooltip
                    data-testid={testId}
                    title={shortenedSingleRowTitle}
                    themeColor={themeColor}
                    placement={placement}
                    onClose={onClose}
                    onOpen={onOpen}
                    open={open}
                    enterDelay={enterDelay}
                    leaveDelay={leaveDelay}
                    PopperProps={{ keepMounted: true }}
                    {...rest}
                >
                    {children.props.disabled ? (
                        <StyledSpan cursor={cursor}>{children}</StyledSpan>
                    ) : (
                        <StyledDiv cursor={cursor}>{children}</StyledDiv>
                    )}
                </StyledSingleRowTooltip>
            )}
            {variant === 'multiRow' && (
                <StyledMultiRowTooltip
                    data-testid={testId}
                    title={multiRowTitle}
                    themeColor={themeColor}
                    placement={placement}
                    onClose={onClose}
                    onOpen={onOpen}
                    open={open}
                    enterDelay={enterDelay}
                    leaveDelay={leaveDelay}
                    PopperProps={{ keepMounted: true }}
                    {...rest}
                >
                    {children.props.disabled ? (
                        <StyledSpan cursor={cursor}>{children}</StyledSpan>
                    ) : (
                        <StyledDiv cursor={cursor}>{children}</StyledDiv>
                    )}
                </StyledMultiRowTooltip>
            )}
            {variant === 'context' && (
                <StyledContextTooltip
                    data-testid={testId}
                    title={title}
                    placement={placement}
                    onClose={onClose}
                    onOpen={onOpen}
                    open={open}
                    enterDelay={enterDelay}
                    leaveDelay={leaveDelay}
                    {...rest}
                >
                    <StyledDiv cursor="default">{children}</StyledDiv>
                </StyledContextTooltip>
            )}
        </>
    );
};
