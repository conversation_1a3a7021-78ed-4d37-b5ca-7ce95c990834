import React from 'react';
import { LinearProgress, styled } from '@mui/material/';
import { convertPxToRem } from '@treez-inc/component-library';

const StyledLinearProgress = styled(LinearProgress)(({ theme }) => ({
    backgroundColor: theme.palette.green03.main,
    '& .MuiLinearProgress-bar': {
        backgroundColor: theme.palette.green06.main,
        borderRadius: convertPxToRem(99),
    },
}));

const TreezLinearProgress = () => <StyledLinearProgress />;

export default TreezLinearProgress;
