import React, { ReactNode } from 'react';
import { Box, styled } from '@mui/material';
import convertPxToRem from '@treez-inc/component-library/dist/util-functions/convertPxToRem';

const ValidationError = styled(Box)(({ theme }) => ({
    ...theme.typography.smallText,
    color: theme.palette.error.main,
    margin: `${convertPxToRem(8)} 0 0 ${convertPxToRem(16)}`,
}));

interface ValidationErrorBoxProps {
    fieldName: string;
    children: ReactNode;
}

const ValidationErrorBox = ({ fieldName, children }: ValidationErrorBoxProps) => (
    <ValidationError data-testid={`${fieldName}-validation-error-box`}>{children}</ValidationError>
);

export default ValidationErrorBox;
