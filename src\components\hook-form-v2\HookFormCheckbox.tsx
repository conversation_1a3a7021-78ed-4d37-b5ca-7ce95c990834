import React from 'react';
import { Controller, useFormContext } from 'react-hook-form';
import { styled, Box } from '@mui/material';
import { Checkbox, Tooltip, Icon } from '@treez-inc/component-library';
import convertPxToRem from '@treez-inc/component-library/dist/util-functions/convertPxToRem';
import { isEmpty } from '../../utils/common';

interface HookFormCheckboxProps {
    name: string;
    label: string;
    toolTipText?: string;
    disabled?: boolean;
    onChangeHandler?: (value: boolean, label: string) => void;
}

const StyledHookContainer = styled(Box)({
    display: 'flex',
    alignItems: 'center',
    flexDirection: 'row',
    gap: convertPxToRem(8),
});
const StyledIconContainer = styled(Box)(() => ({
    display: 'flex',
}));

const HookFormCheckbox = ({
    name,
    label,
    toolTipText = '',
    disabled = false,
    onChangeHandler,
}: HookFormCheckboxProps) => {
    const { control } = useFormContext();

    return (
        <StyledHookContainer>
            <Controller
                control={control}
                name={name}
                render={({ field: { onChange, value } }) => {
                    const onhandleCheckboxChange = (checked?: boolean | undefined) => {
                        onChange(checked);
                        if (onChangeHandler) onChangeHandler(checked || false, name);
                    };
                    return (
                        <Checkbox
                            value={value}
                            label={label}
                            checked={value}
                            data-testid={`checkbox-${name?.toLowerCase().replaceAll(' ', '-')}`}
                            onChange={onhandleCheckboxChange}
                            disabled={disabled}
                        />
                    );
                }}
            />
            {!isEmpty(toolTipText) && (
                <Tooltip
                    title={toolTipText}
                    variant="multiRow"
                    testId={`${label?.toLowerCase().replaceAll(' ', '-')}-tooltip`}
                >
                    <StyledIconContainer>
                        <Icon iconName="InfoOutlined" color="primaryBlack" />
                    </StyledIconContainer>
                </Tooltip>
            )}
        </StyledHookContainer>
    );
};

export default HookFormCheckbox;
