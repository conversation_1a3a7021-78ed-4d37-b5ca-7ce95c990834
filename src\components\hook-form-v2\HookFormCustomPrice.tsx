/* eslint-disable react/no-unused-prop-types */
import React from 'react';
import { useFormContext } from 'react-hook-form';
import { SelectChangeEvent } from '@mui/material/Select';
import { IconName } from '@treez-inc/component-library/dist/components/Icon/types';
import { PriceTier } from '../../views/ProductFormV2/Types/PricingTypes';
import HookFormSelect from './HookFormSelect';
import HookFormPriceInput from './HookFormPriceInput';
import { PricingMethodType } from '../../utils/constants';
import HookFormCustomSelect from './HookFormCustomSelect';
import useProduct from '../../views/ProductFormV2/Hooks/useProduct';
import { ensureFunction } from '../../utils/common';

type HookFormSelectProps = React.ComponentProps<typeof HookFormSelect>;

interface HookFormCustomPriceProps extends Omit<HookFormSelectProps, 'menuItems' | 'label' | 'name'> {
    menuItems?: HookFormSelectProps['menuItems'];
    label?: HookFormSelectProps['label'];
    name?: HookFormSelectProps['name'];
    onBlur?: (e: any) => void;
    indexes?: {
        storeIdx: number;
        skuIdx: number;
        forcedMethod?: PricingMethodType;
    };
    priceTiers?: PriceTier[];
    currentSkuPrice?: number;
    handleIsDirty?: (bool: boolean) => void;
    toggleCustomPrice?: (storeId: string, skuId: string, bool?: boolean) => void;
    pricingMethodOverride?: PricingMethodType;
    inputMode?: boolean;
    queueEntityPriceForDeletion?: (entityId: string) => void;
}

type MenuItem = {
    displayValue: string | number;
    displayName: string;
    iconProps?: {
        iconName: IconName;
    };
};

export default function HookFormCustomPrice({
    priceTiers,
    name,
    onChange,
    onBlur,
    indexes,
    currentSkuPrice,
    handleIsDirty,
    toggleCustomPrice,
    pricingMethodOverride,
    inputMode,
    queueEntityPriceForDeletion,
}: HookFormCustomPriceProps) {
    const {
        setValue,
        getValues,
        formState: { isDirty },
    } = useFormContext();
    const { product, findEntityPriceByOrgEntAndSkuId } = useProduct();

    const productPricingMethod = product.pricingMethod;
    const productTierPriceId = product.priceTierId;

    const [showPriceInput, setShowPriceInput] = React.useState(false);

    const currentInputBasePrice =
        name || (indexes ? `stores.${indexes.storeIdx}.skus.${indexes.skuIdx}.defaultPrices.base` : '');
    const currentSelectPriceTier =
        name || (indexes ? `stores.${indexes.storeIdx}.skus.${indexes.skuIdx}.priceTierId` : '');

    const storeId = indexes ? getValues(`stores.${indexes.storeIdx}.id`) : '';
    const skuId = indexes ? getValues(`stores.${indexes.storeIdx}.skus.${indexes.skuIdx}.id`) : '';

    const entityPrice = findEntityPriceByOrgEntAndSkuId(storeId, skuId);

    const customPricePath = indexes ? `stores.${indexes.storeIdx}.skus.${indexes.skuIdx}.isCustomPrice` : '';

    const skuBasePriceName = `defaultPrices.${indexes?.skuIdx}.base`;

    const safeToggleCustomPrice = ensureFunction(toggleCustomPrice);
    const safeOnChange = ensureFunction(onChange);
    const safeHandleIsDirty = ensureFunction(handleIsDirty);
    const safeOnBlur = ensureFunction(onBlur);
    const safeQueueEntityPriceForDeletion = ensureFunction(queueEntityPriceForDeletion);

    const handleFlatFields = (typedValue: string | number | null) => {
        // this is looking for the Base Price
        // name and only applying if you trying to
        // type a value that's saved in db already;
        if (name === skuBasePriceName) {
            if (!currentSkuPrice) {
                safeHandleIsDirty(Number(typedValue) !== 0);
            } else {
                safeHandleIsDirty(Number(typedValue) !== currentSkuPrice / 100);
            }
        }
        safeHandleIsDirty(true);
    };

    const handleTierFields = (selectedValue: string | number | null, savedTierPrice?: string) => {
        if (savedTierPrice === undefined || savedTierPrice === null) {
            safeHandleIsDirty(selectedValue !== '');
        } else {
            safeHandleIsDirty(selectedValue !== savedTierPrice);
        }
    };

    const determineShowPriceInput = () => {
        // 0 Case for when the fields are dirty (FLAT ONLY)
        if (isDirty && pricingMethodOverride === PricingMethodType.FLAT) {
            const priceTierId = getValues(currentSelectPriceTier);
            const isCustomPrice = getValues(customPricePath);
            return !priceTierId || !isCustomPrice;
        }
        // 1. Special case for reset
        if (indexes?.forcedMethod) {
            return indexes.forcedMethod === PricingMethodType.FLAT;
        }

        // 2. Entity price takes
        if (entityPrice?.pricingMethod) {
            return entityPrice.pricingMethod === PricingMethodType.FLAT;
        }

        // 3. Check if there's a pricing method override
        if (pricingMethodOverride !== undefined) {
            return pricingMethodOverride === PricingMethodType.FLAT;
        }

        // 4. Check product pricing method
        if (productPricingMethod === PricingMethodType.TIER) {
            return false;
        }

        // 5. If there's a SKU price but no method specified
        if (currentSkuPrice) {
            return true;
        }

        // 6. Default case - no methods or prices, show select
        return false;
    };

    React.useEffect(() => {
        setShowPriceInput(determineShowPriceInput());
    }, [entityPrice, pricingMethodOverride, productPricingMethod, currentSkuPrice, indexes?.forcedMethod]);

    const buildTierPricesDropdown = () => {
        const menuItem: MenuItem[] = (Array.isArray(priceTiers) ? priceTiers : []).map((tier: PriceTier) => ({
            displayValue: tier.id,
            displayName: tier.name,
        }));

        // Add 'Custom Flat Price' option if name is not 'priceTierId'
        // priceTierId is the name for product level priceTierId (all skus)
        if (name !== 'priceTierId') {
            menuItem.unshift({
                displayName: 'Custom Flat Price',
                displayValue: null as unknown as number,
                iconProps: {
                    iconName: 'Add' as const,
                },
            });
        }

        return menuItem;
    };

    const handleSelectChange = (event: SelectChangeEvent<unknown>, child: React.ReactNode) => {
        if (onChange) onChange(event, child);

        const selectedValue = event.target.value;
        setShowPriceInput(selectedValue === null);
    };

    const handleClose = () => {
        setShowPriceInput(false);
        setValue(currentInputBasePrice, null);

        const formPriceTierId = getValues(currentSelectPriceTier);

        if ((formPriceTierId || productTierPriceId) && pricingMethodOverride === PricingMethodType.TIER) {
            if (entityPrice && entityPrice.id) {
                safeQueueEntityPriceForDeletion(entityPrice.id);
                safeHandleIsDirty(true);
            }
            setValue(currentSelectPriceTier, formPriceTierId || productTierPriceId);
        } else {
            setValue(currentSelectPriceTier, null);
        }

        safeToggleCustomPrice(storeId, skuId, true);
        setValue(customPricePath, false);
    };

    if (inputMode) {
        return (
            <HookFormPriceInput
                startAdornment="$"
                name={currentInputBasePrice}
                productCategoryName={product.productCategory?.name}
                label="Price"
                key={currentInputBasePrice}
                hasCloseIcon={!inputMode}
                onChange={(e) => {
                    safeOnChange(e, undefined);

                    const selectedValue = e.target.value;
                    handleFlatFields(selectedValue);
                }}
                onClose={handleClose}
                onBlur={(e) => {
                    safeOnBlur(e);
                }}
            />
        );
    }

    return (
        <>
            {showPriceInput ? (
                <HookFormPriceInput
                    startAdornment="$"
                    name={currentInputBasePrice}
                    productCategoryName={product.productCategory?.name}
                    label="Price"
                    key={currentInputBasePrice}
                    hasCloseIcon
                    onChange={(e) => {
                        safeOnChange(e, undefined);

                        const selectedValue = e.target.value;
                        handleFlatFields(selectedValue);
                    }}
                    onClose={handleClose}
                />
            ) : (
                <HookFormCustomSelect
                    menuItems={buildTierPricesDropdown()}
                    name={currentSelectPriceTier}
                    label="Tier Label"
                    key={currentSelectPriceTier}
                    onChange={(e, c) => {
                        handleSelectChange(e, c);

                        const selectedValue = e.target.value as string;
                        handleTierFields(selectedValue);
                    }}
                />
            )}
        </>
    );
}
