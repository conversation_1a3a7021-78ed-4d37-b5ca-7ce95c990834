import React, { useEffect } from 'react';
import { Select } from '@treez-inc/component-library';
import { Controller, useFormContext } from 'react-hook-form';
import { SelectProps } from '@treez-inc/component-library/dist/components/Select';

export interface HookFormSelectProps extends SelectProps {
    name: string;
    autoSelectDefault?: boolean;
    defaultPropKey?: string;
    openDropDown?: boolean;
}

export default function HookFormCustomSelect({
    name,
    autoSelectDefault,
    defaultPropKey,
    openDropDown,
    menuItems,
    onChange,
    ...props
}: HookFormSelectProps) {
    const {
        control,
        formState: { errors },
        getFieldState,
        getValues,
        setValue,
    } = useFormContext();

    const currentValue = getValues(name);

    useEffect(() => {
        if (autoSelectDefault) {
            if (menuItems && (!currentValue || currentValue === '')) {
                const getDefaultItem = () => {
                    if (menuItems.length === 1) {
                        return menuItems[0];
                    }
                    if (defaultPropKey) {
                        return menuItems.find((m: any) => m[defaultPropKey] === true);
                    }
                    return undefined;
                };
                const defaultItem = getDefaultItem();
                if (defaultItem) {
                    setValue(name, defaultItem.displayValue);
                }
            }
        }
    }, [menuItems]);

    const getError = () => {
        if (errors[name]) {
            return errors[name]?.message?.toString();
        }
        const fieldState = getFieldState(name);
        return fieldState?.error?.message;
    };

    return (
        <div>
            <Controller
                control={control}
                name={name}
                render={({ field }) => (
                    <Select
                        {...props}
                        menuItems={menuItems}
                        onChange={(e, c) => {
                            const selectedValue = e.target.value;
                            field.onChange(selectedValue);
                            if (onChange) onChange(e, c);
                        }}
                        value={field.value ?? ''}
                        helperText={getError()}
                        error={!!getError()}
                        testId={`input-${name?.toLowerCase().replaceAll(' ', '-')}`}
                    />
                )}
            />
        </div>
    );
}
