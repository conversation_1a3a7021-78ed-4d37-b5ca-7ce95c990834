import React, { ComponentProps } from 'react';
import { FormDetailsProps, InputType } from '../../interfaces/dto/productCategory';
import HookFormInput from './HookFormInput';
import HookFormSwitch from './HookFormSwitch';
import HookFormSelect from './HookFormSelect';
import ToolTipWrapper from '../ToolTipWraper';
import { alphabeticalAscOrder } from '../../utils/common';
import { COMPLIANCE_FIELDS_ENUM } from '../../utils/product-categories-data';

type InputProps = Partial<ComponentProps<typeof HookFormInput>>;
type SwitchProps = Partial<ComponentProps<typeof HookFormSwitch>>;
type SelectProps = Partial<ComponentProps<typeof HookFormSelect>>;

interface HookFormDynamicProps {
    formatName?: (name: string) => string;
    data: FormDetailsProps;
    disabled?: boolean;
}

const HookFormDynamicField = ({
    data,
    disabled = false,
    formatName,
    ...rest
}: HookFormDynamicProps & (InputProps | SwitchProps | SelectProps)) => {
    const name = formatName ? formatName(data.input) : data.input;

    if (data.inputType === InputType.NUMBER || data.inputType === InputType.STRING) {
        const inputRest = rest as InputProps;
        return (
            <ToolTipWrapper toolTip={data.input === COMPLIANCE_FIELDS_ENUM.TOTAL_MG_THC ? '' : data.toolTip}>
                <HookFormInput
                    {...inputRest}
                    disabled={disabled}
                    name={name}
                    label={data.label.includes('*') ? data.label.replace('*', '') : data.label}
                />
            </ToolTipWrapper>
        );
    }

    if (data.inputType === InputType.BOOLEAN) {
        const switchRest = rest as SwitchProps;
        return (
            <ToolTipWrapper toolTip={data.toolTip}>
                <HookFormSwitch {...switchRest} disabled={disabled} name={name} label={data.label} />
            </ToolTipWrapper>
        );
    }

    if (data.inputType === InputType.SELECT_OPTIONS) {
        const selectRest = rest as SelectProps;
        return (
            <ToolTipWrapper toolTip={data.toolTip}>
                <HookFormSelect
                    {...selectRest}
                    disabled={disabled}
                    name={name}
                    label={data.label}
                    menuItems={alphabeticalAscOrder(data?.options || [], 'displayName') || []}
                />
            </ToolTipWrapper>
        );
    }

    return <div>Dev Note - Type {data.inputType} not implemented in HookFormDynamic</div>;
};

export default HookFormDynamicField;
