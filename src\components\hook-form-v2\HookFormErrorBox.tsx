import React from 'react';
import { Box, styled } from '@mui/material';
import convertPxToRem from '@treez-inc/component-library/dist/util-functions/convertPxToRem';
import { useFormContext } from 'react-hook-form';

const ValidationError = styled(Box)(({ theme }) => ({
    ...theme.typography.smallText,
    color: theme.palette.error.main,
    margin: `${convertPxToRem(8)} 0 0 ${convertPxToRem(16)}`,
}));

interface HookFormErrorBoxProps {
    name: string;
}

const HookFormErrorBox = ({ name }: HookFormErrorBoxProps) => {
    const {
        formState: { errors },
        getFieldState,
    } = useFormContext();

    const getError = () => {
        if (errors[name]) {
            return errors[name]?.message?.toString();
        }
        const fieldState = getFieldState(name);
        return fieldState?.error?.message;
    };

    if (!getError()) {
        return null;
    }
    return <ValidationError data-testid={`${name}-validation-error-box`}>{getError()?.toString()}</ValidationError>;
};
export default HookFormErrorBox;
