import React from 'react';
import { Input, InputProps } from '@treez-inc/component-library';
import { Controller, useFormContext } from 'react-hook-form';

type HookFormProps = Omit<InputProps, 'onChange'>;

export interface HookFormInputProps extends HookFormProps {
    name: string;
    label: string;
    hasCloseIcon?: boolean;
    onChange?: (event: React.ChangeEvent<HTMLInputElement>) => void;
}
export default function HookFormInput({ name, label, hasCloseIcon, onChange, ...props }: HookFormInputProps) {
    const {
        control,
        formState: { errors },
        getFieldState,
    } = useFormContext();

    const getError = () => {
        if (errors[name]) {
            return errors[name]?.message?.toString();
        }
        const fieldState = getFieldState(name);
        return fieldState?.error?.message;
    };

    return (
        <>
            <Controller
                control={control}
                name={name}
                render={({ field }) => (
                    <div onBlur={field.onBlur}>
                        <Input
                            {...props}
                            onChange={(e) => {
                                field.onChange(e);
                                if (onChange) onChange(e);
                            }}
                            value={field.value ?? ''}
                            helperText={getError()}
                            error={!!getError()}
                            testId={`input-${name?.toLowerCase().replaceAll(' ', '-')}`}
                            label={label}
                        />
                    </div>
                )}
            />
        </>
    );
}
