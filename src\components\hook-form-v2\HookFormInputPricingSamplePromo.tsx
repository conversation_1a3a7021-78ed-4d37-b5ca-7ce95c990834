import React, { useEffect, useState } from 'react';
import { Input, InputProps } from '@treez-inc/component-library';
import { Controller, useFormContext } from 'react-hook-form';
import { currencyFormatter } from '../../hooks/currencyConverter';

type HookFormProps = Omit<InputProps, 'onChange'>;

export interface HookFormInputPricingProps extends HookFormProps {
    name: string;
    label: string;
    onChange?: (event: React.ChangeEvent<HTMLInputElement>) => void;
}

function HookFormInputPricing({ name, label, onChange: change, ...props }: HookFormInputPricingProps) {
    const [internalValue, setInternalValue] = useState<string>('');

    const { control, getFieldState } = useFormContext();

    const getError = () => {
        if (!name) return null;

        const fieldState = getFieldState(name);

        if (fieldState.invalid) {
            return 'Price is required';
        }

        return null;
    };

    const priceOnChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { value } = e.target;
        let numericValue = value.replace(/[^0-9.]/g, '');
        // Split the value into integer and decimal parts
        const parts = numericValue.split('.');

        // Limit the integer part to 6 digits
        if (parts[0].length > 6) {
            parts[0] = parts[0].slice(0, 6);
        }
        // If there is a decimal part, limit it to 2 digits
        if (parts.length > 1) {
            parts[1] = parts[1].slice(0, 2);
        }
        numericValue = parts.join('.');
        const regex = /^\d{0,6}(\.\d{0,2})?$/;
        if (regex.test(numericValue)) {
            setInternalValue(numericValue);
        }
        return numericValue;
    };

    return (
        <Controller
            control={control}
            name={name}
            render={({ field }) => {
                useEffect(() => {
                    if (field.value) {
                        const valueInCents = field.value;
                        const valueInDollars = valueInCents / 100; // Convert cents to dollars
                        setInternalValue(currencyFormatter.format(valueInDollars)); // Format as dollars
                    } else {
                        setInternalValue('');
                    }
                }, []);

                const handleBlur = () => {
                    const numericValue = internalValue ? Number(internalValue.replace(/[^0-9.]/g, '')) : 0; // Default to 0 if empty
                    const valueInCents = numericValue * 100; // Convert to cents
                    setInternalValue(numericValue ? currencyFormatter.format(numericValue) : '');
                    field.onChange(valueInCents);
                };
                return (
                    <div onBlur={handleBlur}>
                        <Input
                            startAdornment="$"
                            {...props}
                            onChange={(e) => {
                                if (change instanceof Function) change(e);
                                const numericValue = priceOnChange(e);
                                field.onChange(numericValue);
                            }}
                            value={internalValue ?? ''}
                            helperText={getError()}
                            error={!!getError()}
                            testId={`input-${name?.toLowerCase().replaceAll(' ', '-')}`}
                            label={label}
                        />
                    </div>
                );
            }}
        />
    );
}

export default HookFormInputPricing;
