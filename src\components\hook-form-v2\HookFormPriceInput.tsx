import React from 'react';
import { styled } from '@mui/material/styles';
import { Icon, Input, InputProps } from '@treez-inc/component-library';
import { Controller, useFormContext } from 'react-hook-form';
import { currencyFormatter } from '../../hooks/currencyConverter';
import { formatPriceInput, REQUIRED_PRICE_ERROR, STRING_PRICE_ZERO } from '../../utils/priceUtils';
import { ensureFunction } from '../../utils/common';
import PRODUCT_CATEGORIES from '../../utils/product-categories-data';

type HookFormProps = Omit<InputProps, 'onChange'>;

export interface HookFormPriceInputProps extends HookFormProps {
    name: string;
    label: string;
    onChange?: (event: React.ChangeEvent<HTMLInputElement>) => void;
    validatorFn?: (value: string) => string;
    onClose?: () => void;
    onBlur?: (e: React.FocusEvent<HTMLInputElement>) => void;
    hasCloseIcon?: boolean;
    productCategoryName?: string;
}

const Container = styled('div')`
    position: relative;
`;

const ButtonContainer = styled('div')<{ hasError?: boolean }>`
    position: absolute;
    height: 44px;
    top: 28px;
    bottom: 0;
    right: 16px;

    transform: translateY(-50%);
    z-index: 1;

    & > button {
        border: none;
        width: 100%;
        height: 100%;
        border: none;
        cursor: pointer;
    }
`;

export default function HookFormPriceInput({
    name,
    label,
    onChange,
    validatorFn,
    onBlur,
    onClose,
    hasCloseIcon,
    productCategoryName,
    ...props
}: HookFormPriceInputProps) {
    const {
        control,
        formState: { errors },
        getFieldState,
        getValues,
    } = useFormContext();

    const safeOnClose = ensureFunction(onClose);

    const getError = () => {
        if (errors[name]) {
            return errors[name]?.message?.toString();
        }

        const value = getValues(name);
        if (value === STRING_PRICE_ZERO && productCategoryName !== PRODUCT_CATEGORIES.NonInv) {
            return REQUIRED_PRICE_ERROR;
        }
        const fieldState = getFieldState(name);
        return fieldState?.error?.message;
    };

    const handleClose = () => {
        safeOnClose();
    };

    return (
        <Container>
            <Controller
                control={control}
                name={name}
                render={({ field }) => {
                    const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
                        const { value } = e.target;
                        const numericValue = value ? Number(value.replace(/[^0-9.]/g, '')) : 0;
                        field.onChange(currencyFormatter.format(numericValue));
                    };

                    return (
                        <div>
                            <Input
                                {...props}
                                onBlur={(e: React.FocusEvent<HTMLInputElement>) => {
                                    handleBlur(e);
                                    field.onBlur();
                                    if (onBlur) onBlur(e);
                                }}
                                onChange={(e) => {
                                    field.onChange(e);
                                    if (onChange) onChange(e);
                                }}
                                value={formatPriceInput(field.value) ?? ''}
                                helperText={getError()}
                                error={!!getError()}
                                testId={`input-${name?.toLowerCase().replaceAll(' ', '-')}`}
                                label={label}
                            />
                            {hasCloseIcon && (
                                <ButtonContainer hasError={Boolean(getError())}>
                                    <button type="button" onClick={handleClose} aria-label="Close">
                                        <Icon iconName="Close" />
                                    </button>
                                </ButtonContainer>
                            )}
                        </div>
                    );
                }}
            />
        </Container>
    );
}
