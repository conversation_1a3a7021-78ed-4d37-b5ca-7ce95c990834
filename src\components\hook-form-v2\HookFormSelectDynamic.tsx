import React, { useEffect, useState } from 'react';
import { Select } from '@treez-inc/component-library';
import { Controller, useFormContext } from 'react-hook-form';
import { QueryKeyConfig } from '../../api/types';
import useLoadData from '../../hooks/useLoadData';
import ValidationErrorBox from '../ValidationErrorBox';

type SelectItem = {
    displayValue: string | number;
    displayName: string;
};

export interface HookFormSelectDynamicProps {
    name: string;
    queryConfig: QueryKeyConfig;
    preProcessData: (data: any[]) => SelectItem[];
    defaultPropKey?: string;
    autoSelectDefault: boolean;
    helperText?: string;
    label: string;
}

export default function HookFormSelectDynamic({
    name,
    queryConfig,
    autoSelectDefault,
    defaultPropKey,
    preProcessData,
    ...props
}: HookFormSelectDynamicProps) {
    const [menuItems, setMenuItems] = useState<SelectItem[]>([]);
    const {
        control,
        formState: { errors },
        getValues,
        setValue,
    } = useFormContext();

    const { isLoading, isError, data } = useLoadData<any[]>({
        queryConfig,
    });

    useEffect(() => {
        const items = data ? preProcessData(data) : [];
        setMenuItems(items);
    }, [data]);

    useEffect(() => {
        if (autoSelectDefault) {
            const currentValue = getValues(name);
            if (menuItems && (!currentValue || currentValue === '')) {
                const getDefaultItem = () => {
                    if (menuItems.length === 1) {
                        return menuItems[0];
                    }
                    if (defaultPropKey) {
                        return menuItems.find((m: any) => m[defaultPropKey] === true);
                    }
                    return undefined;
                };
                const defaultItem = getDefaultItem();
                if (defaultItem) {
                    setValue(name, defaultItem.displayValue);
                }
            }
        }
    }, [menuItems]);

    return (
        <div>
            <Controller
                control={control}
                name={name}
                render={({ field }) => (
                    <Select
                        {...props}
                        menuItems={menuItems}
                        onChange={field.onChange}
                        value={field.value ?? ''}
                        label=""
                        error={!!isError}
                        testId={`input-${name?.toLowerCase().replaceAll(' ', '-')}`}
                    />
                )}
            />
            <>{isLoading && 'Loading...'}</>
            {errors[name] && (
                <ValidationErrorBox fieldName="brand">{errors[name]?.message?.toString()}</ValidationErrorBox>
            )}
        </div>
    );
}
