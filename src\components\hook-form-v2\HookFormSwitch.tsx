import React from 'react';
import { Controller, useFormContext } from 'react-hook-form';
import { Switch } from '@treez-inc/component-library';
import convertPxToRem from '@treez-inc/component-library/dist/util-functions/convertPxToRem';
import { styled } from '@mui/material';

interface HookFormCheckboxProps {
    disabled?: boolean;
    label: string;
    name: string;
}

const StyledHookContainer = styled('div')(() => ({
    paddingRight: convertPxToRem(5),
    // removes the hardcoded height of the switch in the component lib, which messes with our alignment
    '& .MuiSwitch-root': {
        height: 'unset',
    },
}));

const HookFormSwitch = ({ disabled = false, label, name }: HookFormCheckboxProps) => {
    const { control } = useFormContext();
    return (
        <StyledHookContainer>
            <Controller
                control={control}
                name={name}
                render={({ field: { onChange, value } }) => (
                    <Switch
                        disabled={disabled}
                        label={label}
                        checked={value}
                        value={value}
                        onChange={onChange}
                        testId="hook-form-switch"
                    />
                )}
            />
        </StyledHookContainer>
    );
};

export default HookFormSwitch;
