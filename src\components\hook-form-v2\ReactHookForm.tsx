import React, { ReactNode } from 'react';
import { FormProvider, UseFormReturn } from 'react-hook-form';
import LoadingIndicator from '../LoadingIndicator';

interface ReactHookFormProps {
    onSubmit: (values: any) => any;
    children: ReactNode;
    formName: string;
    formContextProps: UseFormReturn;
    isBusy?: boolean;
}

const ReactHookForm = ({ onSubmit, formContextProps, children, formName, isBusy }: ReactHookFormProps) => (
    <FormProvider {...formContextProps}>
        <form onSubmit={formContextProps.handleSubmit(onSubmit)} id={formName}>
            {children}
            <LoadingIndicator isLoading={isBusy} />
        </form>
    </FormProvider>
);

export default ReactHookForm;
