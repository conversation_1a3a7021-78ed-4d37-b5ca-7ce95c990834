import React, { useEffect } from 'react';
import { Controller, useFormContext } from 'react-hook-form';
import { SelectProps } from '@treez-inc/component-library/dist/components/Select';
import {
    MenuProps as MenuPropsType,
    Select,
    Box,
    MenuItem,
    styled,
    FormControl,
    InputLabel,
    Typography,
} from '@mui/material';
import useProduct from '../../views/ProductFormV2/Hooks/useProduct';
import { BulkProductSubCategories } from '../../interfaces/dto/productSubCategory';
import { PricingMethodType } from '../../utils/constants';
import { ensureFunction } from '../../utils/common';

const CustomSelectorStyle = styled(Select)(({ theme }) => ({
    backgroundColor: theme.palette.treezGrey[3],
    borderRadius: '1.25em',
    padding: 0,
    display: 'flex',
    alignItems: 'end',
    '&:hover': {
        backgroundColor: theme.palette.treezGrey[3],
    },
    '&:focus': {
        outline: `2.5px solid ${theme.palette.primaryBlack.main}`,
    },
    '&.Mui-disabled': {
        backgroundColor: theme.palette.treezGrey[2],
        outline: 'unset',
    },
    '& .MuiFilledInput-input': {
        paddingLeft: '16px',

        '&:focus': {
            backgroundColor: 'transparent',
            borderRadius: '19px',
            outline: `2.5px solid ${theme.palette.primaryBlack.main}`,
        },
    },
    '& .MuiSelect-filled': {
        padding: '1em 1em 0.8em 1em',
    },
    '& .MuiBox-root': {
        paddingTop: '3px',
    },
}));
const StyledMenuItem = styled(MenuItem)(({ theme }) => ({
    backgroundColor: 'transparent',
    '&:hover': {
        backgroundColor: theme.palette.treezGreen[3],
    },
    '&.Mui-selected': {
        backgroundColor: theme.palette.treezGreen[3],
        '&:hover': {
            backgroundColor: theme.palette.treezGreen[3],
        },
    },
    '&.Mui-focusVisible:not(:hover)': {
        backgroundColor: 'transparent',
    },
}));
const MenuProps: Partial<MenuPropsType> = {
    sx: {
        '& .MuiPopover-paper': {
            borderRadius: '.5em',
            boxShadow: '0 1.875rem 2.5rem rgba(0, 0, 0, 0.12)',
            backgroundColor: '#f5f5f5',
        },
    },
};
const InputLabelStyled = styled(InputLabel)({
    marginTop: '1px',
    marginLeft: '5px',

    '&.Mui-focused': {
        color: '#8c8c8c',
        fontSize: '.75em',
    },
    '&.MuiFormLabel-filled': {
        color: '#8c8c8c',
        fontSize: '.75em',
    },
});

export interface HookFormSelectProps extends SelectProps {
    name: string;
    autoSelectDefault?: boolean;
    setOpenSubcategoryChangeDialog?: (bool: boolean) => void;
    defaultPropKey?: string;
}

export default function HookFormSelect({
    name,
    autoSelectDefault,
    defaultPropKey,
    menuItems,
    setOpenSubcategoryChangeDialog,
    onChange,
    ...props
}: HookFormSelectProps) {
    const {
        product: { pricingMethod, productSubCategory },
        isBulkSubCategory,
        entityPrices,
    } = useProduct();

    const {
        control,
        formState: { errors },
        getFieldState,
        getValues,
        setValue,
    } = useFormContext();

    const someEntityPriceHasTier = entityPrices.some((ep) => ep.pricingMethod === PricingMethodType.TIER);

    const DELI_STYLE = Object.values(BulkProductSubCategories);

    const safeSetOpenSubcategoryChangeDialog = ensureFunction(setOpenSubcategoryChangeDialog);
    const safeOnChange = ensureFunction(onChange);

    useEffect(() => {
        if (autoSelectDefault) {
            const currentValue = getValues(name);
            if (menuItems && (!currentValue || currentValue === '')) {
                const getDefaultItem = () => {
                    if (menuItems.length === 1) {
                        return menuItems[0];
                    }
                    if (defaultPropKey) {
                        return menuItems.find((m: any) => m[defaultPropKey] === true);
                    }
                    return undefined;
                };
                const defaultItem = getDefaultItem();
                if (defaultItem) {
                    setValue(name, defaultItem.displayValue);
                }
            }
        }
    }, [menuItems]);

    const getError = () => {
        if (errors[name]) {
            return errors[name]?.message?.toString();
        }
        const fieldState = getFieldState(name);
        return fieldState?.error?.message;
    };

    const enhancedMenuItems = menuItems.map((item) => ({
        ...item,
        displayName: DELI_STYLE.includes(item.displayName as BulkProductSubCategories)
            ? `${item.displayName}`
            : item.displayName,
    }));

    const isFlowerExtractBulk = isBulkSubCategory(productSubCategory?.name!);

    return (
        <FormControl fullWidth variant="filled">
            <InputLabelStyled id={`${name}-label`}>{props.label}</InputLabelStyled>
            <Controller
                control={control}
                name={name}
                render={({ field }) => (
                    <CustomSelectorStyle
                        {...props}
                        labelId={`${name}-label`}
                        label={props.label}
                        MenuProps={MenuProps}
                        disableUnderline
                        onChange={(event, selectedNode) => {
                            if (
                                isFlowerExtractBulk &&
                                (pricingMethod === PricingMethodType.TIER || someEntityPriceHasTier)
                            ) {
                                safeSetOpenSubcategoryChangeDialog(true);
                            } else {
                                field.onChange(event);
                                safeOnChange(event, selectedNode);
                            }
                        }}
                        value={field.value ?? ''}
                        data-testid={`input-${name?.toLowerCase().replaceAll(' ', '-')}`}
                    >
                        {enhancedMenuItems.map((item) => (
                            <StyledMenuItem key={item.displayValue} value={item.displayValue}>
                                <Box display="flex" justifyContent="space-between" width="100%">
                                    <Typography>{item.displayName}</Typography>
                                    {DELI_STYLE.includes(
                                        item.displayName.replace('Deli Style', '') as BulkProductSubCategories,
                                    ) && <Typography sx={{ color: '#808080' }}>Deli Style</Typography>}
                                </Box>
                            </StyledMenuItem>
                        ))}
                    </CustomSelectorStyle>
                )}
            />
            {Boolean(getError()) && (
                <Typography sx={{ paddingLeft: '1em' }} variant="caption" color="error">
                    {getError()}
                </Typography>
            )}
        </FormControl>
    );
}
