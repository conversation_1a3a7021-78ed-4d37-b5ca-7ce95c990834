import React, { useEffect, useRef, useState } from 'react';
import { Button, styled } from '@mui/material';
import { Link } from 'react-router-dom';
import detectOutsideClick from '../../utils/detectOutsideClick';
import { ADD_PRODUCT_URL } from '../../utils/constants';

const ButtonLink = styled(Link)(() => ({
    textDecoration: 'none',
}));

const AddButton = styled(Button)(({ theme }) => ({
    zIndex: 1,
    backgroundColor: `${theme.palette.sativaGreen.main}`,
    borderRadius: '1em',
    boxShadow: 'none',
    fontSize: '15px',
    height: '35px',

    '&:hover': {
        boxShadow: 'none',
        backgroundColor: `${theme.palette.green06.main}`,
    },
}));

interface IAddButtonProps {
    isDisabled: boolean;
}

export default function AddProductButton({ isDisabled }: IAddButtonProps) {
    // close options component on outside click
    const optionsRef = useRef(null);
    const [listening, setListening] = useState(false);
    useEffect(() => {
        detectOutsideClick(listening, setListening, optionsRef, false);
    });

    return (
        <ButtonLink to={`${ADD_PRODUCT_URL}`}>
            <AddButton variant="contained" data-testid="add-product-button" disabled={isDisabled}>
                Add Product
            </AddButton>
        </ButtonLink>
    );
}
