import React from 'react';
import { Icon } from '@treez-inc/component-library';
import { useNavigate } from 'react-router-dom';
import { Box, styled } from '@mui/material/';
import { ADD_PRODUCT_URL } from '../../utils/constants';

const OptionsContainer = styled(Box)(({ theme }) => ({
    alignItems: 'center',
    background: `${theme.palette.treezGrey[2]}`,
    borderRadius: '17px',
    boxShadow: '0px 30px 40px rgba(0, 0, 0, 0.12)',
    display: 'flex-box',
    flexDirection: 'column',
    fontFamily: 'Roboto',
    fontSize: '0.875rem',
    fontWeight: 500,
    height: '108px',
    padding: '10px 0px',
    position: 'absolute',
    width: '213px',
    zIndex: 99,
}));

const OptionBtn = styled(Box)(({ theme }) => ({
    alignItems: 'center',
    alignSelf: 'stretch',
    borderRadius: '17px',
    cursor: 'pointer',
    display: 'flex',
    flex: 'none',
    flexGrow: 0,
    flexDirection: 'row',
    gap: '10px',
    height: '44px',
    order: 0,
    padding: '0px 0px 2px 16px',
    textAlign: 'center',
    width: '197px',

    '&:hover': {
        backgroundColor: `${theme.palette.treezGrey[3]}`,
    },
}));

export default function AddProductBtnOptions() {
    const navigate = useNavigate();

    const goToProductCreation = () => {
        navigate(`${ADD_PRODUCT_URL}`);
    };

    return (
        <OptionsContainer>
            <OptionBtn onClick={goToProductCreation}>
                <Icon iconName="Add" fontSize="small" /> <span>New</span>
            </OptionBtn>
            <OptionBtn>
                <Icon iconName="Copy" fontSize="small" /> <span>From existing</span>
            </OptionBtn>
        </OptionsContainer>
    );
}
