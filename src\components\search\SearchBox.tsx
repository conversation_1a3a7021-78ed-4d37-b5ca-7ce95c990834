import React from 'react';
import { Input } from '@treez-inc/component-library';

export interface SearchBoxProps {
    onChange: (value: string) => void;
    searchTerm: string;
}

export default function SearchBox({ setSearchTerm, filters }: any) {
    const value = filters.quickFilterValues.join('') || '';
    return <Input value={value} label="Find or Scan Product" onChange={(e) => setSearchTerm(e.target.value)} />;
}
