import React from 'react';
import { styled } from '@mui/material';

const FieldLabel = styled('div')(({ theme }) => ({
    alignItems: 'left',
    color: `${theme.palette.grey08.main}`,
    display: 'flex',
    fontFamily: 'Roboto',
    fontSize: '15px',
    fontStyle: 'normal',
    fontWeight: 400,
    lineHeight: '23px',
}));

const SectionFieldLabel = ({ children }: any) => <FieldLabel>{children}</FieldLabel>;

export default SectionFieldLabel;
