export const convertSnakeCaseToPascalCaseWithSpaces = (snakeCase: string): string => {
    // Remove underscores and split the string into an array of words
    const words = snakeCase.split('_');
    // Capitalize the first letter of each word and join them with spaces
    const pascalCaseWords = words.map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase());

    return pascalCaseWords.join(' ');
};

export const currencyFormatter = new Intl.NumberFormat('en-US', {
    style: 'decimal', // Use decimal style instead of currency style
    minimumFractionDigits: 2, // Ensure two decimal places
    maximumFractionDigits: 2,
});
