import React from 'react';

interface OrganizationEntityInfoProviderProps {
    stores: {
        id: string;
        name: string;
    }[];
    regions: string[];
}

const defaultValues: OrganizationEntityInfoProviderProps = {
    stores: [],
    regions: [],
};
const OrganizationEntityInfoContext = React.createContext<OrganizationEntityInfoProviderProps>(defaultValues);
export const useOrganizationEntityInfoContext = () => React.useContext(OrganizationEntityInfoContext);
export const OrganizationEntityInfoContextProvider = OrganizationEntityInfoContext.Provider;
