import { useState } from 'react';
import { OrganizationEntityDto } from '../../interfaces/dto/organizationEntity';
import { getData } from '../../api/genericAccessor';
import Entities from '../../interfaces/entities.enum';

interface StoreProps {
    id: string;
    name: string;
}

const useOrganizationEntityMethods = () => {
    const [regions, setRegions] = useState<string[]>([]);

    const [stores, setStores] = useState<StoreProps[]>([]);

    const fetchOrganizationEntityInfo = async () => {
        try {
            const organizationEntityInfo: OrganizationEntityDto[] = await getData(Entities.ORGANIZATION_ENTITY, {});

            const orgStores = Array.from(
                new Set(
                    organizationEntityInfo?.map((item) => ({
                        id: item.id,
                        name: item.name,
                    })),
                ),
            );
            setStores(orgStores);

            const regionsInfo = Array.from(
                new Set(
                    organizationEntityInfo?.filter((item) => item?.address?.region)?.map((item) => item.address.region),
                ),
            );
            setRegions(regionsInfo);
        } catch (error) {
            // eslint-disable-next-line no-console
            console.error('Error fetching organization entity info:', error);
        }
    };

    return { regions, stores, fetchOrganizationEntityInfo };
};

export default useOrganizationEntityMethods;
