import { useMutation, useQueryClient } from 'react-query';
import { MutationKeyConfig, QueryKeyConfig } from '../../api/types';
import { createProductCollection } from '../../api/product-collection-api/productCollectionAccessor';

interface CreateDataParams {
    config: MutationKeyConfig;
    invalidateQueryKeys?: (data: any) => QueryKeyConfig;
}

const useCreateCollection = ({ config, invalidateQueryKeys }: CreateDataParams) => {
    const client = useQueryClient();
    return useMutation({
        mutationKey: config.mutationKey,
        mutationFn: (data: any) => createProductCollection(data),
        onSuccess: ({ data }: any) => {
            if (invalidateQueryKeys) {
                client.invalidateQueries(invalidateQueryKeys(data));
            }
        },
    });
};

export default useCreateCollection;
