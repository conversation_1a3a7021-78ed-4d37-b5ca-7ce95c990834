import { useQuery } from 'react-query';
import { QueryKeyConfig } from '../../api/types';
import { getProductCollectionData } from '../../api/product-collection-api/productCollectionAccessor';

interface LoadCollectionDataParams {
    queryConfig: QueryKeyConfig;
    isEnabled?: boolean;
}

const useLoadCollectionData = <T>({ queryConfig, isEnabled }: LoadCollectionDataParams) =>
    useQuery<T>({
        queryKey: queryConfig.queryKey,
        queryFn: () => getProductCollectionData(queryConfig.route),
        enabled: isEnabled !== false && !!(queryConfig.isEnabled || queryConfig.isEnabled === undefined),
        notifyOnChangeProps: 'tracked',
        cacheTime: queryConfig.cacheTime !== undefined ? queryConfig.cacheTime : 0,
        ...(queryConfig.staleTime !== undefined && { staleTime: queryConfig.staleTime }),
    });

export default useLoadCollectionData;
