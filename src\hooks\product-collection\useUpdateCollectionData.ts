import { useMutation, useQueryClient } from 'react-query';
import { MutationKeyConfig, QueryKeyConfig } from '../../api/types';
import { updateProductCollectionData } from '../../api/product-collection-api/productCollectionAccessor';

interface CreateDataParams {
    config: MutationKeyConfig;
    invalidateQueryKeys?: (data: any) => QueryKeyConfig;
}

const useUpdateProductCollectionData = ({ config, invalidateQueryKeys }: CreateDataParams) => {
    const client = useQueryClient();
    return useMutation({
        mutationKey: config.mutationKey,
        mutationFn: (data: any) => updateProductCollectionData(config.route, data),
        onSuccess: ({ data }: any) => {
            if (invalidateQueryKeys) {
                client.invalidateQueries(invalidateQueryKeys(data));
            }
        },
    });
};

export default useUpdateProductCollectionData;
