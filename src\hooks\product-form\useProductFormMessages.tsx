import useSnackbarContext from '../snackbar/useSnackbarContext';

const useProductFormMessages = () => {
    const { setSnackbar } = useSnackbarContext();

    const constructBackendErrorMsg = (err: any): string =>
        `Error type: ${err.response.data.errorType}; Error message: ${err.response.data.message}`;

    // PUBLIC METHODS
    const showValidationError = (err: any) => {
        // eslint-disable-next-line no-console
        console.log(err);
        setSnackbar({
            message: `There are items above that require your attention`,
            severity: 'warning',
            iconName: 'Warning',
        });
    };

    const showBackendError = (err: any) => {
        const backendError = constructBackendErrorMsg(err);
        setSnackbar({
            message: backendError,
            severity: 'error',
            iconName: 'Error',
        });
    };

    return {
        showValidationError,
        showBackendError,
    };
};

export default useProductFormMessages;
