import { useState } from 'react';
import { SnackbarProps } from '@treez-inc/component-library';

export type TSnack = Pick<SnackbarProps, 'message' | 'severity' | 'iconName'>;

export type TSnackHandler = (snack: TSnack) => void;

const defaultSnack = {
    message: '',
} as const;

const useSnackbar = (): [TSnackHandler, SnackbarProps] => {
    const [open, setOpen] = useState<boolean>(false);
    const [snack, setSnack] = useState<TSnack>(defaultSnack);

    const handleSetSnackbar = (newSnack: TSnack) => {
        setSnack(newSnack);
        setOpen(true);
    };

    const onClose = () => {
        setOpen(false);
    };

    return [
        handleSetSnackbar,
        {
            open,
            onClose,
            testId: 'snackbar',
            message: snack.message,
            severity: snack.severity,
            iconName: snack.iconName,
        },
    ];
};

export default useSnackbar;
