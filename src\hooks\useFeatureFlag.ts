import { useContext } from 'react';
import { FeatureFlagContext } from '../providers/FeatureFlagProvider';
import FeatureFlag from '../interfaces/featureFlag';

const useFeatureFlag = (name: FeatureFlag) => {
    const context = useContext(FeatureFlagContext);

    if (context == null) {
        throw new Error('useFeatureFlagContext must be used within a FeatureFlagProvider');
    }

    return context.checkFeatureEnabled(name);
};

export default useFeatureFlag;
