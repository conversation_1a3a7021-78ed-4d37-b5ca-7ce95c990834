import { useQuery } from 'react-query';
import { getDataByRoute } from '../api/genericAccessor';
import { ORGANIZATION_MANAGEMENT_API_URL } from '../utils/constants';

export const orgFeatureFlagsUrl = (organizationId: string) =>
    `${ORGANIZATION_MANAGEMENT_API_URL}/organizations/${organizationId}/feature-flag`;

export interface FeatureFlagData {
    service: string;
    status: boolean;
}

const useFeatureFlagApi = (organizationId: string) => {
    const route = orgFeatureFlagsUrl(organizationId);

    return useQuery<FeatureFlagData[]>({
        queryKey: ['FATURE_FLAG', organizationId],
        queryFn: () => getDataByRoute(route, {}),
        enabled: true,
        notifyOnChangeProps: 'tracked',
        refetchOnMount: false,
        refetchOnWindowFocus: false,
        cacheTime: Infinity,
        staleTime: Infinity,
    });
};

export default useFeatureFlagApi;
