import { useQuery } from 'react-query';
import { QueryKeyConfig } from '../api/types';
import { getData } from '../api/genericAccessor';

interface LoadDataParams {
    queryConfig: QueryKeyConfig;
    isEnabled?: boolean;
}

const useLoadData = <T>({ queryConfig, isEnabled }: LoadDataParams) =>
    useQuery<T>({
        queryKey: queryConfig.queryKey,
        queryFn: () => getData(queryConfig.route, queryConfig.params),
        enabled: isEnabled !== false && !!(queryConfig.isEnabled || queryConfig.isEnabled === undefined),
        notifyOnChangeProps: 'tracked',
        refetchOnMount: queryConfig.refetchOnMount === undefined ? true : queryConfig.refetchOnMount,
        refetchOnWindowFocus: queryConfig.refetchOnWindowFocus === undefined ? true : queryConfig.refetchOnWindowFocus,
        cacheTime: queryConfig.cacheTime !== undefined ? queryConfig.cacheTime : 0,
        ...(queryConfig.staleTime !== undefined && { staleTime: queryConfig.staleTime }),
    });

export default useLoadData;
