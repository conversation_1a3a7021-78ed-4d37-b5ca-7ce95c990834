import { UseFormReturn, useForm } from 'react-hook-form';
import { joiResolver } from '@hookform/resolvers/joi';

interface UseReactHookFormParams<T> {
    defaultValues?: T;
    values?: T;
    joiSchema?: any;
}

const useReactHookForm = <T>({ defaultValues, values, joiSchema }: UseReactHookFormParams<T>): UseFormReturn =>
    useForm({
        mode: 'onBlur',
        reValidateMode: 'onChange',
        shouldFocusError: true,
        ...(defaultValues && { defaultValues }),
        ...(values && { values }),
        ...(joiSchema && { resolver: joiResolver(joiSchema, { stripUnknown: true, abortEarly: false }) }),
    });

export default useReactHookForm;
