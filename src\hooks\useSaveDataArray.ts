import { useMutation, useQueryClient } from 'react-query';
import { MutationKeyConfig, QueryKeyConfig } from '../api/types';
import { createData, deleteData, updateData } from '../api/genericAccessor';

interface CreateDataParams {
    mutationConfig: MutationKeyConfig;
    invalidateQueryKeys?: (data: any) => QueryKeyConfig;
}

interface MutationVariables {
    createData?: any;
    updateData?: any;
    deleteData?: { ids: any[] };
}

interface ApiResponse<T> {
    data?: T;
    error?: any;
}

export interface MutationResponse<T> {
    createData?: ApiResponse<T>;
    updateData?: ApiResponse<T>;
    deleteData?: ApiResponse<T>;
}

type RequestApi = (entity: string, data: any) => Promise<any>;

const errorWrapper = async <T>(request: RequestApi, route: string, data: any): Promise<ApiResponse<T>> => {
    try {
        const result = await request(route, data);
        return { data: result.data ? result.data : result, error: result.failed };
    } catch (error) {
        return { error };
    }
};

const saveData = async <T>(data: MutationVariables, route: string): Promise<MutationResponse<T>> => {
    const result = await Promise.all([
        data.createData ? errorWrapper<T>(createData, route, data.createData) : Promise.resolve(undefined),
        data.updateData ? errorWrapper<T>(updateData, route, data.updateData) : Promise.resolve(undefined),
        data.deleteData ? errorWrapper<T>(deleteData, route, data.deleteData) : Promise.resolve(undefined),
    ]);

    return { createData: result[0], updateData: result[1], deleteData: result[2] };
};

const saveDataList = async <T>(dataItems: MutationVariables[], route: string): Promise<MutationResponse<T>[]> =>
    Promise.all<MutationResponse<T>>(dataItems.map((d) => saveData(d, route)));

const useSaveDataArray = <T>({ mutationConfig, invalidateQueryKeys }: CreateDataParams) => {
    const client = useQueryClient();

    return useMutation<MutationResponse<T>[], any, MutationVariables[], any>({
        mutationKey: mutationConfig.mutationKey,
        mutationFn: (data: MutationVariables[]) => saveDataList(data, mutationConfig.route),
        onSuccess: ({ data }: any) => {
            if (invalidateQueryKeys) {
                client.invalidateQueries(invalidateQueryKeys(data).queryKey);
            }
        },
    });
};

export default useSaveDataArray;
