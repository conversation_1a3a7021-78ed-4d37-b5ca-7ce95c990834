import { IconName } from '@treez-inc/component-library/dist/components/Icon/types';
import { DetailsDto } from './details';
import { ReferenceIdDto } from './referenceId';
import { BrandDto } from './brand';
import { ProductSubCategoryDto } from './productSubCategory';
import { ImageDetailsDto } from './imageDetails';
import { PricingMethodType } from '../../utils/constants';
import { SkuDto, SkuResponseDto } from './sku';

export interface ProductDto {
    id?: string;
    category?: IconName;
    brand?: BrandDto;
    brandId?: string;
    createdAt?: string;
    deletedAt?: null | string;
    details?: DetailsDto;
    images?: ImageDetailsDto[];
    status: string;
    name: string;
    organizationId?: string;
    productAttributes?: [];
    productSubCategory?: ProductSubCategoryDto;
    productSubCategoryId?: string;
    referenceIds?: ReferenceIdDto[];
    strain?: string;
    updatedAt?: string;
    variants?: SkuDto[];
    verifiedReferenceId?: string;
    pricingMethod?: PricingMethodType;
    priceTierId?: string | null;
}

export interface ProductSearchResponse {
    id: string;
    productId: string;
    productName: string;
    verifiedReferenceId?: string;
    brandId: string;
    brandName: string;
    productSubCategoryId: string;
    productSubCategoryName: string;
    productCategoryId: string;
    productCategoryName: string;
    strain: string;
    classification: string;
    images: ImageDetailsDto[];
    status: string;
    lastUpdated: string;
    organizationId: string;
    allSizes: string;
    hierarchy: string[];
    isChild: boolean;
    variants: SkuResponseDto[];
    formattedVariantSizes: string[];
    formattedVariantSkus: string[];
}

export interface PricingPageProduct extends ProductSearchResponse {
    productCategoryIcon?: IconName;
}

export interface ProductMergeDto {
    productIds: string[];
    targetMergeProductId: string;
}
