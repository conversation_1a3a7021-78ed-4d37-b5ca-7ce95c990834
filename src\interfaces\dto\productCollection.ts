export interface ProductCollectionItems {
    variantId: string | undefined;
}
export interface ProductCollection {
    id?: string;
    name: string | undefined;
    organizationId?: string;
    sync?: boolean;
    createdAt?: string;
    updatedAt?: string;
    deletedAt?: string;
    collectionItems?: ProductCollectionItems[];
}

export interface GetProductCollectionsResponse {
    totalCount: number;
    data: ProductCollection[];
}

export interface ProductCollectionMenuItems {
    displayName: string;
    displayValue: string;
}
