import { DetailsDto } from './details';
import { ReferenceIdDto } from './referenceId';
import { BrandDto } from './brand';
import { ProductSubCategoryDto } from './productSubCategory';
import { ImageDetailsDto } from './imageDetails';
import { ProductCategoryDto } from './productCategory';
import { ProductAttributeData } from './productAttributeData';
import { SkuDto } from './sku';
import { PricingMethodType } from '../../utils/constants';

export interface ProductData {
    id?: string;

    productCategory?: ProductCategoryDto;
    productSubCategory?: ProductSubCategoryDto;
    productSubCategoryId?: string;

    brand?: BrandDto;
    brandId?: string;

    status?: string;
    name: string;
    uom?: string;
    organizationId?: string;
    strain?: string;

    details?: DetailsDto;
    images?: ImageDetailsDto[];
    productAttributes?: ProductAttributeData[];
    variants?: SkuDto[];
    productImages?: ImageDetailsDto[];

    verifiedReferenceId?: string;
    referenceIds?: ReferenceIdDto[];

    pricingMethod?: PricingMethodType;
    priceTierId?: string | null;

    createdAt?: string;
    updatedAt?: string;
    deletedAt?: null | string;
}
