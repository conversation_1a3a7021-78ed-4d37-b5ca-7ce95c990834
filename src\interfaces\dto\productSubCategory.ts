export interface ProductSubCategoryDto {
    id: string;
    name: string;
    isCannabis: boolean;
    productCategoryId: string;
}

export enum BulkProductSubCategories {
    BULK_FLOWER = 'Bulk Flower',
    SHAKE = 'Shake',
    STRAIN_SPECIFIC_SHAKE = 'Strain Specific Shake',
    BULK_EXTRACT = 'Bulk Extract',
}

export enum InfusedSubCategories {
    INFUSED = 'Infused',
    INFUSED_FLOWER = 'Infused Flower',
    INFUSED_BLUNT = 'Infused Blunt',
}
