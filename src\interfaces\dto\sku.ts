import { DefaultPricesDto } from './defaultPrices';
import { ImageDetailsDto } from './imageDetails';
import { ReferenceIdDto } from './referenceId';
import { SkuDetailsDto } from './skuDetails';

export interface SkuDto {
    additionalSku?: string[];
    amount: number | null;
    children?: SkuDto[];
    defaultPrices?: DefaultPricesDto;
    details?: SkuDetailsDto;
    id?: string;
    images?: ImageDetailsDto[];
    merchandiseSize: string | null;
    name: string;
    organizationId?: string;
    parentId?: string;
    productId: string;
    referenceIds?: ReferenceIdDto[];
    sku?: string | null;
    status: string;
    unitCount: number;
    uom: string | null;
    updatedAt?: string;
    verifiedReferenceId?: string;
    label?: string;
}

export type ChildSkuDto = Partial<
    Pick<SkuDto, 'additionalSku' | 'defaultPrices' | 'referenceIds' | 'sku' | 'status'> & {
        details: Pick<SkuDetailsDto, 'description' | 'hideFromEcomMenu' | 'menuTitle'>;
    }
>;

export type SkuApiDto = SkuDto & {
    promo?: ChildSkuDto;
    sample?: ChildSkuDto;
};

export type SkuBasePriceData = Pick<
    SkuDto,
    'id' | 'defaultPrices' | 'unitCount' | 'amount' | 'uom' | 'merchandiseSize'
>;

export type SkuResponseDto = SkuDto & {
    label: string; // This is a calculated field returned in the response from the backend that displays sku size. Ex: 1 g - 2 pack, 1 g - 2 pack - Sample etc
};

export enum SkuType {
    BASE = 'base',
    PROMO = 'promo',
    SAMPLE = 'sample',
    NONE = 'none',
}
