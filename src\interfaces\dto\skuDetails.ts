export interface SkuDetailsDto {
    cbdPerDose?: number;
    description?: string;
    useCustomName?: boolean;
    sameAsAmount?: boolean;
    doses?: number;
    extractionMethod?: string;
    grossWeight?: number;
    hideFromEcomMenu?: boolean;
    isPromo?: boolean;
    isSample?: boolean;
    menuTitle?: string;
    name?: string; // old field, replaced by menuTitle
    netWeight?: number;
    netWeightUom?: string;
    thcPerDose?: number;
    totalConcentrateWeight?: number;
    totalFlowerWeight?: number;
    totalMgCbd?: number;
    totalMgThc?: number;
    usableMarijuanaWeight?: number;
    useGlobalDescription?: boolean;
}
