import { useMutation } from 'react-query';
import { createData } from '../../api/genericAccessor';
import { AttributeDto } from '../../interfaces/dto/attribute';
import Entities from '../../interfaces/entities.enum';

const useCreateAttributeMutation = () =>
    useMutation({
        mutationFn: (newAttribute: AttributeDto[]) => createData(Entities.ATTRIBUTE, newAttribute),
    });

export default useCreateAttributeMutation;
