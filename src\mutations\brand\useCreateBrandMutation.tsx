import { useMutation, useQueryClient } from 'react-query';
import { createData } from '../../api/genericAccessor';
import { BrandDto } from '../../interfaces/dto/brand';
import Entities from '../../interfaces/entities.enum';
import queryKeyStore from '../../queries/queryKeyStore';

const useCreateBrandMutation = () => {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: (newBrand: BrandDto[]) => createData(Entities.BRAND, newBrand),
        onSuccess: ({ data }) =>
            Promise.all(
                data.map((item: any) =>
                    queryClient.invalidateQueries(
                        queryKeyStore.brand.list({
                            ids: item.ids,
                        }),
                    ),
                ),
            ),
    });
};

export default useCreateBrandMutation;
