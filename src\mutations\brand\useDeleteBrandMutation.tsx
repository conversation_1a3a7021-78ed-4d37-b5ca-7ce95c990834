import { useMutation, useQueryClient } from 'react-query';
import Entities from '../../interfaces/entities.enum';
import queryKeyStore from '../../queries/queryKeyStore';
import { deleteData } from '../../api/genericAccessor';

const useDeleteBrandMutation = () => {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: (ids: Array<string>) => deleteData(Entities.BRAND, ids),
        onSuccess: () => queryClient.invalidateQueries(queryKeyStore.brand.list({})),
    });
};

export default useDeleteBrandMutation;
