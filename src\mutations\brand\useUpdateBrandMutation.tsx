import { useMutation, useQueryClient } from 'react-query';
import { updateData } from '../../api/genericAccessor';
import Entities from '../../interfaces/entities.enum';
import queryKeyStore from '../../queries/queryKeyStore';

const useUpdateBrandMutation = () => {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: (brandData) => updateData(Entities.BRAND, brandData),
        onSuccess: () => queryClient.invalidateQueries(queryKeyStore.brand.list({})),
    });
};

export default useUpdateBrandMutation;
