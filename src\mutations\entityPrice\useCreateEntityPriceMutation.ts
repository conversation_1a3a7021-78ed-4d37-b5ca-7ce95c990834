import { useMutation, useQueryClient } from 'react-query';
import { createData } from '../../api/genericAccessor';
import Entities from '../../interfaces/entities.enum';
import queryKeyStore from '../../queries/queryKeyStore';
import { EntityPriceDto } from '../../interfaces/dto/entityPrice';

const useCreateEntityPriceMutation = () => {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: (entityDetails: EntityPriceDto[]) => createData(Entities.ENTITY_PRICE, entityDetails),
        onSuccess: ({ data }) =>
            Promise.all(
                data.map((item: any) =>
                    queryClient
                        .invalidateQueries(
                            queryKeyStore.price.list({
                                variantIds: item.ids,
                            }),
                        )
                        .then(() =>
                            queryClient.refetchQueries(
                                queryKeyStore.price.list({
                                    variantIds: item.ids,
                                }),
                            ),
                        ),
                ),
            ),
    });
};

export default useCreateEntityPriceMutation;
