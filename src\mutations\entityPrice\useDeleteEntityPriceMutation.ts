import { useMutation, useQueryClient } from 'react-query';
import { deleteData } from '../../api/genericAccessor';
import Entities from '../../interfaces/entities.enum';
import queryKeyStore from '../../queries/queryKeyStore';

const useDeleteEntityPriceMutation = () => {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: (ids: string[]) => deleteData(Entities.ENTITY_PRICE, ids),
        onSuccess: ({ data }) =>
            Promise.all(
                data.map((item: any) =>
                    queryClient
                        .invalidateQueries(
                            queryKeyStore.price.list({
                                variantIds: item.ids,
                            }),
                        )
                        .then(() =>
                            queryClient.refetchQueries(
                                queryKeyStore.price.list({
                                    variantIds: item.ids,
                                }),
                            ),
                        ),
                ),
            ),
    });
};

export default useDeleteEntityPriceMutation;
