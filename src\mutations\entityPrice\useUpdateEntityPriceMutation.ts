import { useMutation, useQueryClient } from 'react-query';
import { updateData } from '../../api/genericAccessor';
import Entities from '../../interfaces/entities.enum';
import queryKeyStore from '../../queries/queryKeyStore';
import { EntityPriceDto } from '../../interfaces/dto/entityPrice';

const useUpdateEntityPriceMutation = () => {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: (entityDetails: Partial<EntityPriceDto>[]) => updateData(Entities.ENTITY_PRICE, entityDetails),
        onSuccess: ({ data }) =>
            Promise.all(
                data.map((item: any) =>
                    queryClient
                        .invalidateQueries(
                            queryKeyStore.price.list({
                                variantIds: item.ids,
                            }),
                        )
                        .then(() =>
                            queryClient.refetchQueries(
                                queryKeyStore.price.list({
                                    variantIds: item.ids,
                                }),
                            ),
                        ),
                ),
            ),
    });
};

export default useUpdateEntityPriceMutation;
