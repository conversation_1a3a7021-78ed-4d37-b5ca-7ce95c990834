import { useMutation, useQueryClient } from 'react-query';
import { createData } from '../../api/genericAccessor';
import Entities from '../../interfaces/entities.enum';
import queryKeyStore from '../../queries/queryKeyStore';
import { ImageDetailsDto } from '../../interfaces/dto/imageDetails';

const useCreateImageDetailsMutation = () => {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: (imageDetails: ImageDetailsDto[]) => createData(Entities.IMAGE_DETAILS, imageDetails),
        onSuccess: ({ data }) =>
            Promise.all(
                data.map((item: any) =>
                    queryClient.invalidateQueries(
                        queryKeyStore.imageDetails.list({
                            ids: item.ids,
                        }),
                    ),
                ),
            ),
    });
};

export default useCreateImageDetailsMutation;
