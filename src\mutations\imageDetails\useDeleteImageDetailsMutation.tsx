import { useMutation, useQueryClient } from 'react-query';
import { deleteData } from '../../api/genericAccessor';
import Entities from '../../interfaces/entities.enum';
import queryKeyStore from '../../queries/queryKeyStore';

const useDeleteImageDetailsMutation = () => {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: (ids: Array<string>) => deleteData(Entities.IMAGE_DETAILS, ids),
        onSuccess: ({ data }) =>
            Promise.all(
                data.map((item: any) =>
                    queryClient.invalidateQueries(
                        queryKeyStore.imageDetails.list({
                            ids: item.ids,
                        }),
                    ),
                ),
            ),
    });
};

export default useDeleteImageDetailsMutation;
