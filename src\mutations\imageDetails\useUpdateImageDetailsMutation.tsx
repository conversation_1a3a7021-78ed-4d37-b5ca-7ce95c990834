import { useMutation, useQueryClient } from 'react-query';
import { updateData } from '../../api/genericAccessor';
import Entities from '../../interfaces/entities.enum';
import queryKeyStore from '../../queries/queryKeyStore';
import { ImageDetailsDto } from '../../interfaces/dto/imageDetails';

const useUpdateImageDetailsMutation = () => {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: (data: Partial<ImageDetailsDto>[]) => updateData(Entities.IMAGE_DETAILS, data),
        onSuccess: ({ data }) =>
            Promise.all(
                data.map((item: any) =>
                    queryClient.invalidateQueries(
                        queryKeyStore.imageDetails.list({
                            ids: item.ids,
                        }),
                    ),
                ),
            ),
    });
};

export default useUpdateImageDetailsMutation;
