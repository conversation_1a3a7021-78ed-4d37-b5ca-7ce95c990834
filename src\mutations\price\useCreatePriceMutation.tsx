import { useMutation, useQueryClient } from 'react-query';
import { createData } from '../../api/genericAccessor';
import Entities from '../../interfaces/entities.enum';
import queryKeyStore from '../../queries/queryKeyStore';
import { EntityPriceDto } from '../../interfaces/dto/entityPrice';

const useCreatePriceMutation = () => {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: (newPrice: Array<EntityPriceDto>) => createData(Entities.PRICE, newPrice),
        onSuccess: ({ data }) =>
            Promise.all(data.map(() => queryClient.invalidateQueries(queryKeyStore.price.list({})))),
    });
};

export default useCreatePriceMutation;
