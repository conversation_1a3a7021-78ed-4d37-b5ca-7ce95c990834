import { useMutation, useQueryClient } from 'react-query';
import { updateData } from '../../api/genericAccessor';
import Entities from '../../interfaces/entities.enum';
import queryKeyStore from '../../queries/queryKeyStore';
import { EntityPriceDto } from '../../interfaces/dto/entityPrice';

const useUpdatePriceMutation = () => {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: (price: Array<EntityPriceDto>) => updateData(Entities.PRICE, price),
        onSuccess: ({ data }) =>
            Promise.all(data.map(() => queryClient.invalidateQueries(queryKeyStore.price.list({})))),
    });
};

export default useUpdatePriceMutation;
