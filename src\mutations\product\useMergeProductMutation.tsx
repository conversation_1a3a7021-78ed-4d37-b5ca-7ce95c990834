import { useMutation, useQueryClient } from 'react-query';
import { updateData } from '../../api/genericAccessor';
import { ProductMergeDto } from '../../interfaces/dto/product';
import Entities from '../../interfaces/entities.enum';
import queryKeyStore from '../../queries/queryKeyStore';

const useMergeProductMutation = () => {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: (data: ProductMergeDto) => updateData(Entities.MERGE_PRODUCT, data),
        onSuccess: ({ data }) =>
            Promise.all(
                data.map((item: any) =>
                    queryClient.invalidateQueries(
                        queryKeyStore.product.list({
                            ids: item.ids,
                        }),
                    ),
                ),
            ),
    });
};

export default useMergeProductMutation;
