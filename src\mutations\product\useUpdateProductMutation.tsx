import { useMutation, useQueryClient } from 'react-query';
import { updateData } from '../../api/genericAccessor';
import { ProductDto } from '../../interfaces/dto/product';
import Entities from '../../interfaces/entities.enum';
import queryKeyStore from '../../queries/queryKeyStore';

const useUpdateProductMutation = () => {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: (data: ProductDto[]) => updateData(Entities.PRODUCT, data),
        onSuccess: ({ data }) =>
            Promise.all(
                data.map((item: any) =>
                    queryClient.invalidateQueries(
                        queryKeyStore.product.list({
                            ids: item.ids,
                        }),
                    ),
                ),
            ),
    });
};

export default useUpdateProductMutation;
