import { useMutation, useQueryClient } from 'react-query';
import { createData } from '../../api/genericAccessor';
import Entities from '../../interfaces/entities.enum';
import queryKeyStore from '../../queries/queryKeyStore';
import { SkuDto } from '../../interfaces/dto/sku';

const useCreateSkuMutation = () => {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: (newSku: SkuDto[]) => createData(Entities.SKU, newSku),
        onSuccess: ({ data }) =>
            Promise.all(
                data.map((item: any) =>
                    queryClient.invalidateQueries(
                        queryKeyStore.sku.list({
                            ids: item.ids,
                        }),
                    ),
                ),
            ),
    });
};

export default useCreateSkuMutation;
