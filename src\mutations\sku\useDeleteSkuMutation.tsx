import { useMutation, useQueryClient } from 'react-query';
import { deleteData } from '../../api/genericAccessor';
import Entities from '../../interfaces/entities.enum';
import queryKeyStore from '../../queries/queryKeyStore';

const useDeleteSkuMutation = () => {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: (data: Array<string>) => deleteData(Entities.SKU, { ids: data }),
        onSuccess: ({ data }) =>
            Promise.all(
                data.map((item: any) =>
                    queryClient.invalidateQueries(
                        queryKeyStore.sku.list({
                            ids: item.ids,
                        }),
                    ),
                ),
            ),
    });
};

export default useDeleteSkuMutation;
