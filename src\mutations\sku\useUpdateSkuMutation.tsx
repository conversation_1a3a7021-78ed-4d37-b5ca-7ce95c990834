import { useMutation, useQueryClient } from 'react-query';
import { updateData } from '../../api/genericAccessor';
import Entities from '../../interfaces/entities.enum';
import queryKeyStore from '../../queries/queryKeyStore';
import { SkuDto } from '../../interfaces/dto/sku';

const useUpdateSkuMutation = () => {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: (data: Partial<SkuDto>[]) => updateData(Entities.SKU, data),
        onSuccess: ({ data }) =>
            Promise.all(
                data.map((item: any) =>
                    queryClient.invalidateQueries(
                        queryKeyStore.sku.list({
                            ids: item.ids,
                        }),
                    ),
                ),
            ),
    });
};

export default useUpdateSkuMutation;
