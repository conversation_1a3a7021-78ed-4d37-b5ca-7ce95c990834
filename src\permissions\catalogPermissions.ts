// eslint-disable-next-line no-shadow
enum CatalogPermissions {
    // SERVICE LEVEL
    CATALOG_SERVICE_ADJUST = 'catalogservice::adjust',
    CATALOG_SERVICE_LIST = 'catalogservice::list',
    CATALOG_SERVICE_DELETE = 'catalogservice::delete',

    // BRAND
    ADJUST_BRAND = 'catalogservice::adjustbrand',
    LIST_BRAND = 'catalogservice::listbrand',

    // PRODUCT
    CREATE_PRODUCT = 'catalogservice::createproduct',
    ADJUST_PRODUCT = 'catalogservice::adjustproduct',
    LIST_PRODUCT = 'catalogservice::listproduct',

    // VARIANT
    CREATE_VARIANT = 'catalogservice::createvariant',
    ADJUST_VARIANT = 'catalogservice::adjustvariant',
    LIST_VARIANT = 'catalogservice::listvariant',

    // ENTITY_PRICE
    CREATE_ENTITY_PRICE = 'catalogservice::createentityprice',
    ADJUST_ENTITY_PRICE = 'catalogservice::adjustentityprice',
    LIST_ENTITY_PRICE = 'catalogservice::listentityprice',

    // ATTRIBUTE
    ADJUST_ATTRIBUTE = 'catalogservice::adjustattribute',
    LIST_ATTRIBUTE = 'catalogservice::listattribute',
    DELETE_ATTRIBUTE = 'catalogservice::deleteattribute',

    // ATTRIBUTE_CATEGORY
    CREATE_ATTRIBUTE_CATEGORY = 'catalogservice::createattributecategory',
    ADJUST_ATTRIBUTE_CATEGORY = 'catalogservice::adjustattributecategory',
    LIST_ATTRIBUTE_CATEGORY = 'catalogservice::listattributecategory',
    DELETE_ATTRIBUTE_CATEGORY = 'catalogservice::deleteattributecategory',

    LIST_ORGANIZATION_ENTITY = 'catalogservice::listorganizationentity',

    // TREEZ_ADMIN
    TREEZ_ADMIN = 'catalogservice::treezadmin',
}

export default CatalogPermissions;
