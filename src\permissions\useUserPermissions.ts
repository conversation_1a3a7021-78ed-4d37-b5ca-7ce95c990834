import { useState } from 'react';
import Permissions from '../interfaces/permissions';

const useUserPermissions = () => {
    const [userOrgId, setUserOrgId] = useState<string>('');
    const [userPermissions, setUserPermissions] = useState<string[]>([]);

    const validateUserPermissions = (permsToCheck: string[]) =>
        permsToCheck.some((perm: string) => userPermissions.includes(perm));

    const saveUserPermissions = async (getPermissions: () => Promise<Permissions>) => {
        const data = await getPermissions();
        setUserPermissions(data?.permissions || []);
    };

    const saveUserOrgId = async (newUserOrgId: string) => {
        setUserOrgId(newUserOrgId);
    };

    return {
        validateUserPermissions,
        saveUserPermissions,
        saveUserOrgId,
        userOrgId,
    };
};

export default useUserPermissions;
