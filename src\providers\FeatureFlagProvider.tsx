import React, { useMemo, createContext } from 'react';
import useFeature<PERSON>lagApi, { FeatureFlagData } from '../hooks/useFeatureFlagApi';
import { getUserOrgIdFromTokens } from '../interfaces/tokens';
import FeatureFlag from '../interfaces/featureFlag';

export interface IFeatureFlagContext {
    checkFeatureEnabled: (flag: FeatureFlag) => boolean;
}

export const FeatureFlagContext = createContext<IFeatureFlagContext | null>(null);

export interface IFeatureFlagProviderProps {
    children: React.ReactNode;
    getTokens: () => any;
}

const checkFeatureFlag = (flag: FeatureFlag, data: FeatureFlagData[] | null) => {
    if (data?.some((d) => d.service === flag.toString() && d.status === true)) {
        return true;
    }
    return false;
};

const FeatureFlagProvider: React.FC<IFeatureFlagProviderProps> = ({ children, getTokens }) => {
    const { data } = useFeatureFlagApi(getUserOrgIdFromTokens(getTokens));

    const contextProvider = useMemo(
        () => ({
            checkFeatureEnabled: (flag: FeatureFlag) => (data ? checkFeatureFlag(flag, data) : false),
        }),
        [data],
    );

    return <FeatureFlagContext.Provider value={contextProvider}>{children}</FeatureFlagContext.Provider>;
};

export default FeatureFlagProvider;
