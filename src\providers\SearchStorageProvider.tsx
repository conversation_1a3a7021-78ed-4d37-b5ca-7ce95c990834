import React, { createContext, useState, useMemo, ReactNode } from 'react';
import { SearchStorageContextType } from '../interfaces/searchContext';

const minutesToMilliseconds = (minutes: number) => minutes * 60 * 1000;

const DEFAULT_SEARCH_EXPIRATION = minutesToMilliseconds(10); // setting 10 min as expiry time for search term

export const SearchStorageContext = createContext<SearchStorageContextType | undefined>(undefined);

export const SearchStorageProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
    const [searchTerm, setSearchTerm] = useState<string>('');

    const clearSearch = () => {
        setSearchTerm('');
    };

    if (searchTerm) {
        setTimeout(() => {
            clearSearch();
        }, DEFAULT_SEARCH_EXPIRATION);
    }

    const contextValue = useMemo(
        () => ({
            storedSearchTerm: searchTerm,
            setStoredSearchTerm: setSearchTerm,
        }),
        [searchTerm, setSearchTerm],
    );

    return <SearchStorageContext.Provider value={contextValue}>{children}</SearchStorageContext.Provider>;
};
