import { createQueryKeyStore } from '@lukemorales/query-key-factory';
import { createData, getData } from '../api/genericAccessor';
import Entities from '../interfaces/entities.enum';
import type { ProductSearchQueryProps } from './useProductSearchQuery';
import { PriceTierType } from '../interfaces/dto/PriceTierType';

const queryKeyStore = createQueryKeyStore({
    brand: {
        all: null,
        list: (listQueryParams: { ids?: string[] }) => ({
            queryKey: [{ ...listQueryParams }],
            queryFn: () => getData(Entities.BRAND, listQueryParams),
        }),
    },
    product: {
        all: null,
        list: (listQueryParams: { ids?: string[] }) => ({
            queryKey: [{ ...listQueryParams }],
            queryFn: () => getData(Entities.PRODUCT, listQueryParams),
        }),
    },
    variant: {
        all: null,
        list: (listQueryParams: { ids?: string[]; productId?: string }) => ({
            queryKey: [{ ...listQueryParams }],
            queryFn: () => getData(Entities.VARIANT, listQueryParams),
        }),
    },
    productCategory: {
        all: null,
        list: (listQueryParams?: any) => ({
            queryKey: [{ ...listQueryParams }],
            queryFn: () => getData(Entities.PRODUCT_CATEGORY, listQueryParams),
        }),
    },
    price: {
        all: null,
        list: (listQueryParams: { variantIds?: string[] }) => ({
            queryKey: [{ ...listQueryParams }],
            queryFn: () => getData(Entities.PRICE, listQueryParams),
        }),
    },
    sku: {
        all: null,
        list: (listQueryParams: { ids?: string[]; productId?: string }) => ({
            queryKey: [listQueryParams],
            queryFn: () => getData(Entities.SKU, listQueryParams),
        }),
    },
    store: {
        all: null,
        list: (listQueryParams?: any) => ({
            queryKey: [{ ...listQueryParams }],
            queryFn: () => getData(Entities.ORGANIZATION_ENTITY, {}),
        }),
    },
    attributeCategory: {
        all: null,
        list: (listQueryParams: {}) => ({
            queryKey: [{ ...listQueryParams }],
            queryFn: () => getData(Entities.ATTRIBUTE_CATEGORY, {}),
        }),
    },
    imageDetails: {
        all: null,
        list: (listQueryParams: {}) => ({
            queryKey: [{ ...listQueryParams }],
            queryFn: () => getData(Entities.IMAGE_DETAILS, {}),
        }),
    },
    productSearch: {
        all: null,
        list: (listQueryParams: ProductSearchQueryProps) => ({
            queryKey: ['list-query-params', JSON.stringify(listQueryParams)],
            queryFn: () => createData(Entities.SEARCH_PRODUCT, listQueryParams),
        }),
    },
    priceTier: {
        all: null,
        list: (listQueryParams: { types: PriceTierType[] }) => ({
            queryKey: [{ ...listQueryParams }],
            queryFn: () => getData(Entities.PRICE_TIER, listQueryParams),
        }),
    },
});

export default queryKeyStore;
