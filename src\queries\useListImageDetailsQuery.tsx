import { useQuery } from 'react-query';
import { UseQueryOptions } from 'react-query/types/react/types';
import queryKeyStore from './queryKeyStore';

const useListImageDetailsQuery = ({
    productIds,
    variantIds,
    options,
}: {
    productIds: string[];
    variantIds: string[];
    options?: Partial<UseQueryOptions>;
}) =>
    useQuery({
        ...queryKeyStore.imageDetails.list({ productIds, variantIds }),
        ...options,
    });

export default useListImageDetailsQuery;
