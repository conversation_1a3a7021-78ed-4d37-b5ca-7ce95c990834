import { useQuery } from 'react-query';
import { UseQueryOptions } from 'react-query/types/react/types';
import queryKeyStore from './queryKeyStore';
import { PriceTierType } from '../interfaces/dto/PriceTierType';

const useListPriceTiers = ({
    isBulkProduct,
    options,
}: {
    ids?: string[];
    isBulkProduct?: boolean;
    options?: Partial<UseQueryOptions>;
}) =>
    useQuery({
        ...queryKeyStore.priceTier.list({
            types: isBulkProduct ? [PriceTierType.BulkWeight] : [PriceTierType.Weight, PriceTierType.Unit],
        }),
        ...options,
    });

export default useListPriceTiers;
