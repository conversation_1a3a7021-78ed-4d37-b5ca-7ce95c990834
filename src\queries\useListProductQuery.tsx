import { useQuery } from 'react-query';
import { ProductDto } from '../interfaces/dto/product';
import queryKeyStore from './queryKeyStore';

const useListProductQuery = ({ productIds, options }: { productIds?: string[]; options?: Record<string, any> }) =>
    useQuery<ProductDto[]>({
        ...queryKeyStore.product.list({ ids: productIds }),
        ...options,
    });

export default useListProductQuery;
