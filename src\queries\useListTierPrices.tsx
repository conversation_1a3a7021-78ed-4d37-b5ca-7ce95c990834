import { useQuery } from 'react-query';
import { UseQueryOptions } from 'react-query/types/react/types';
import queryKeyStore from './queryKeyStore';
import { PriceTierType } from '../interfaces/dto/PriceTierType';

const useListTierPrices = ({ options }: { ids?: string[]; options?: Partial<UseQueryOptions> }) =>
    useQuery({
        ...queryKeyStore.priceTier.list({
            types: [PriceTierType.BulkWeight, PriceTierType.Weight, PriceTierType.Unit],
        }),
        ...options,
    });

export default useListTierPrices;
