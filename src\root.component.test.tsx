import React from 'react';
import { act, render, screen, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import jwtDecode from 'jwt-decode';
import Root from './root.component';
import IPermissions from './interfaces/permissions';

jest.mock('jwt-decode', () => jest.fn());

const tokens = {
    expiresIn: 360,
    idToken: 'idToken',
    accessToken: 'accessToken',
    refreshToken: 'refreshToken',
};

const permissions: IPermissions = {
    id: '',
    permissions: [''],
    entityContexts: [{ id: '' }],
};

describe('Root component', () => {
    it('renders a message', async () => {
        (jwtDecode as jest.Mock).mockImplementationOnce(() => ({ exp: 12345 }));
        await act(async () =>
            render(<Root getTokens={() => tokens} getPermissions={() => Promise.resolve(permissions)} />),
        );

        await waitFor(() => {
            expect(screen.getByText(/404 Not Found/i)).toBeInTheDocument();
        });
    });
});
