import React, { useEffect } from 'react';
import { ContentContainer } from '@treez/nav-sidebar';
import { createBrowserRouter, RouterProvider } from 'react-router-dom';
import { TreezThemeProvider } from '@treez-inc/component-library';
import { QueryClient, QueryClientProvider } from 'react-query';
import { Box, styled } from '@mui/material';
import AuthTokens, { getUserOrgIdFromTokens } from './interfaces/tokens';
import RootView from './views/RootView/RootView';
import About from './views/About/About';
import Brand from './views/Brand/Brand';
import { setAuthTokensAccessor } from './api/genericAccessor';
import SnackbarProvider from './providers/SnackbarProvider';
import ErrorView from './views/ErrorView/ErrorView';
import IPermissions from './interfaces/permissions';
import useUserPermissions from './permissions/useUserPermissions';
import { UserPermissionsContextProvider } from './permissions/userPermissions.context';
import ProductControl from './views/ProductControl/ProductControl';
import { BASE_PATH, VERSION_BUILD_DATE, VERSION_COMMIT, VERSION_TAG } from './utils/constants';
import EditProductPage from './views/ProductFormV2/EditProductPage';
import { OrganizationEntityInfoContextProvider } from './hooks/organization-entity/useOrganizationEntityInfoContext';
import useOrganizationEntityMethods from './hooks/organization-entity/useOrganizationEntityMethods';
import Mode from './views/ProductFormV2/Types/Mode';
import { setAuthTokensProductCollectionAccessor } from './api/product-collection-api/productCollectionAccessor';
import { SearchStorageProvider } from './providers/SearchStorageProvider';
import Pricing from './views/Pricing/Pricing';
import FeatureFlagProvider from './providers/FeatureFlagProvider';

const ScrollBox = styled(Box)(({ theme }) => ({
    backgroundColor: `${theme.palette.primaryWhite.main}`,
    height: '100%',
    overflowY: 'auto',
}));

interface IFrameworkProps {
    getTokens: () => AuthTokens;
    // clearTokens?: () => void;
    getPermissions: () => Promise<IPermissions>;
}

(globalThis as any).version = {
    ...(globalThis as any).version,
    productControl: {
        ...(globalThis as any).version?.productControl,
        tag: VERSION_TAG,
        commit: VERSION_COMMIT,
        buildDate: VERSION_BUILD_DATE,
    },
};

const Root: React.FC<IFrameworkProps> = ({ getPermissions, getTokens }) => {
    setAuthTokensAccessor(getTokens);
    setAuthTokensProductCollectionAccessor(getTokens);
    const { validateUserPermissions, saveUserPermissions, saveUserOrgId, userOrgId } = useUserPermissions();
    const { regions, stores, fetchOrganizationEntityInfo } = useOrganizationEntityMethods();

    useEffect(() => {
        saveUserPermissions(getPermissions);
        saveUserOrgId(getUserOrgIdFromTokens(getTokens));
        fetchOrganizationEntityInfo();
    }, []);

    const router = createBrowserRouter([
        {
            path: BASE_PATH,
            element: <RootView />,
            errorElement: <ErrorView />,
            children: [
                {
                    path: `about`,
                    element: <About />,
                },
                {
                    path: `brand`,
                    element: <Brand />,
                },
                {
                    path: `create/:productId?`,
                    element: <EditProductPage mode={Mode.CREATE} />,
                },
                {
                    path: 'edit/:productId',
                    element: <EditProductPage mode={Mode.EDIT} />,
                },
                {
                    index: true, // This will make the component the default at "/mfe"
                    element: <ProductControl />,
                },
                {
                    path: 'pricing',
                    element: <Pricing />,
                },
                {
                    path: '*',
                    element: <h2>404 Not Found</h2>,
                },
            ],
        },
        {
            path: '*',
            element: <h2>404 Not Found</h2>,
        },
    ]);
    const queryClientProvider = new QueryClient();
    return (
        <React.StrictMode>
            <QueryClientProvider client={queryClientProvider}>
                <TreezThemeProvider>
                    <ContentContainer>
                        <FeatureFlagProvider getTokens={getTokens}>
                            <OrganizationEntityInfoContextProvider value={{ regions, stores }}>
                                <UserPermissionsContextProvider value={{ validateUserPermissions, userOrgId }}>
                                    <SnackbarProvider>
                                        <SearchStorageProvider>
                                            <ScrollBox>
                                                <RouterProvider
                                                    router={router}
                                                    fallbackElement={<div>Loading...</div>}
                                                />
                                            </ScrollBox>
                                        </SearchStorageProvider>
                                    </SnackbarProvider>
                                </UserPermissionsContextProvider>
                            </OrganizationEntityInfoContextProvider>
                        </FeatureFlagProvider>
                    </ContentContainer>
                </TreezThemeProvider>
            </QueryClientProvider>
        </React.StrictMode>
    );
};

export default Root;
