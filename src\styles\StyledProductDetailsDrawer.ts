import { Box, styled, Typography } from '@mui/material';
import convertPxToRem from '@treez-inc/component-library/dist/util-functions/convertPxToRem';

// Common
export const SectionWrapper = styled(Box)(() => ({
    marginTop: convertPxToRem(16),
}));

export const SubHeader = styled(Typography)(({ theme }) => ({
    color: theme.palette.grey08.main,
}));

export const ProductCustomDivider = styled('hr')(({ theme }) => ({
    borderColor: theme.palette.grey03.main,
    borderWidth: 0,
    borderBottomWidth: 'thin',
    margin: 0,
}));

// Image carousel
export const CarouselWrapper = styled(Box)(() => ({
    display: 'flex',
    alignItems: 'center',
    gap: convertPxToRem(10),
    justifyContent: 'center',
    width: convertPxToRem(496),
    minHeight: convertPxToRem(200),
    maxHeight: convertPxToRem(348),
    padding: `${convertPxToRem(0)} ${convertPxToRem(2)}`,
}));

export const ImageWrapper = styled(Box)(() => ({
    display: 'flex',
    flexShrink: '0',
    alignItems: 'center',
    flexDirection: 'column',
    justifyContent: 'center',
    // width: convertPxToRem(200),
    height: convertPxToRem(290),
}));

// Product highlighter
export const HighlighterContainer = styled(Box)(() => ({
    display: 'flex',
    justifyContent: 'start',
    alignItems: 'center',
}));

export const HighlighterTypography = styled(Typography)(({ theme }) => ({
    color: theme.palette.grey08.main,
    fontSize: convertPxToRem(12),
}));

export const HighlighterCustomDotIcon = styled(Box)(({ theme }) => ({
    backgroundColor: theme.palette.grey08.main,
    width: convertPxToRem(3),
    height: convertPxToRem(3),
    borderRadius: '50%',
}));

// Product title
export const ProductTitleTypography = styled(Typography)(({ theme }) => ({
    color: theme.palette.primaryBlackText.main,
}));

export const ProductMgThcTypography = styled(Typography)(({ theme }) => ({
    color: theme.palette.primaryBlackText.main,
    margin: `${convertPxToRem(12)} 0 0 0`,
    fontSize: convertPxToRem(32),
}));

// Global description
export const ProductGlobalDescTypography = styled(Typography)(({ theme }) => ({
    color: theme.palette.grey08.main,
}));

export const ProductGlobalDescContainer = styled(Box)(() => ({
    margin: `${convertPxToRem(12)} 0 0 0`,
}));

export const ProductGlobalDescription = styled(Typography)(({ theme }) => ({
    ...theme.typography.largeText,
}));

// Variants
export const VariantDetails = styled(Box)(() => ({
    margin: `${convertPxToRem(16)} 0 ${convertPxToRem(16)} 0`,
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'flex-start',
    gap: convertPxToRem(12),
    alignSelf: 'stretch',
}));

export const VariantCardsWrapper = styled(Box)(() => ({
    display: 'flex',
    alignItems: 'flex-start',
    gap: convertPxToRem(16),
    alignSelf: 'stretch',
    flexWrap: 'wrap',
}));

export const VariantCardContainer = styled(Box)(({ theme }) => ({
    display: 'flex',
    flexDirection: 'column',
    width: convertPxToRem(116),
    padding: convertPxToRem(20),
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'stretch',
    borderRadius: convertPxToRem(20),
    border: `1px solid ${theme.palette.grey04.main}`,
    background: theme.palette.primaryWhite.main,
    boxShadow: `0 ${convertPxToRem(1)} ${convertPxToRem(3)} 0 #1a00000d, 0 ${convertPxToRem(1)} ${convertPxToRem(
        2,
    )} ${convertPxToRem(-1)} #1a00000d`,
}));

export const VariantCardTitle = styled(Typography)(({ theme }) => ({
    color: theme.palette.primaryBlack.main,
    textAlign: 'center',
    whiteSpace: 'normal',
    paddingBottom: convertPxToRem(4),
}));

export const VariantCardPricing = styled(Typography)(({ theme }) => ({
    color: theme.palette.secondaryText.main,
    textAlign: 'center',
    whiteSpace: 'normal',
    paddingBottom: convertPxToRem(12),
}));

export const VariantSkuTypography = styled(Typography)(({ theme }) => ({
    color: theme.palette.grey08.main,
    textAlign: 'center',
    whiteSpace: 'normal',
}));

export const VariantOunceTypography = styled(Typography)(({ theme }) => ({
    color: theme.palette.grey08.main,
    textAlign: 'center',
    whiteSpace: 'normal',
    padding: `${convertPxToRem(12)} 0 ${convertPxToRem(2)} 0`,
}));

// Attribute
export const AttributesChipContainer = styled(Box)(() => ({
    display: 'flex',
    flexWrap: 'wrap',
    gap: convertPxToRem(12),
    margin: `${convertPxToRem(16)} 0 ${convertPxToRem(24)} 0`,
}));
