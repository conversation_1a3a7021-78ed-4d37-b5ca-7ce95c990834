import { Button, styled } from '@mui/material/';
import convertPxToRem from '@treez-inc/component-library/dist/util-functions/convertPxToRem';

const StyledButton = styled(Button)(({ theme }) => ({
    color: theme.palette.primaryBlackText.main,
    height: convertPxToRem(40),
    padding: `0rem ${convertPxToRem(20)}`,
    borderRadius: convertPxToRem(15),
    transition: `all 0.2s`,
    backgroundColor: theme.palette.sativaGreen.main,
    ...theme.typography.largeTextStrong,
    '&:hover': {
        backgroundColor: theme.palette.green06.main,
    },
    '&:disabled': {
        color: theme.palette.disabledText.main,
        backgroundColor: theme.palette.grey03.main,
    },
    '&:active': {
        transform: `scale(0.98)`,
    },
    '&.Mui-focusVisible': {
        border: `${convertPxToRem(2.5)} solid ${theme.palette.primaryBlack.main}`,
    },
    '& .MuiButton-startIcon': {
        marginRight: convertPxToRem(4),
    },
}));

// styles for primary button variant
export const StyledPrimaryButton = styled(StyledButton)(({ theme }) => ({
    backgroundColor: theme.palette.sativaGreen.main,
    '&:hover': {
        backgroundColor: theme.palette.green06.main,
    },
}));

// styles for secondary button variant
export const StyledSecondaryButton = styled(StyledButton)(({ theme }) => ({
    backgroundColor: theme.palette.primaryWhite.main,
    border: `${convertPxToRem(1)} solid ${theme.palette.green10.main}`,
    color: theme.palette.green10.main,
    '&:hover': {
        backgroundColor: theme.palette.green03.main,
        border: `${convertPxToRem(1)} solid ${theme.palette.green03.main}`,
    },
    '&:disabled': {
        backgroundColor: theme.palette.primaryWhite.main,
        border: `${convertPxToRem(1)} solid ${theme.palette.grey04.main}`,
    },
}));
