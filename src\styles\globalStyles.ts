/* eslint-disable import/prefer-default-export */
import { Box, Typography, styled } from '@mui/material';
import convertPxToRem from '@treez-inc/component-library/dist/util-functions/convertPxToRem';
import VerifiedIcon from '@mui/icons-material/Verified';

export const StyledLoadingContainer = styled(Box)({
    height: '80%',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
});

export const ProductFormSectionBox = styled(Box)(({ theme }) => ({
    backgroundColor: theme.palette.primaryWhite.main,
    border: `${convertPxToRem(1)} solid ${theme.palette.grey04.main}`,
    borderRadius: convertPxToRem(16),
    display: 'flex',
    flexDirection: 'column',
    fontFamily: 'Roboto',
    rowGap: convertPxToRem(16),
    padding: convertPxToRem(24),
    // width: convertPxToRem(768), //UseMe: Need to change the width of the form container in the future.
}));

export const StyledHeader = styled(Box)(() => ({
    color: `rgba(0, 0, 0, 0.89)`,
    fontSize: convertPxToRem(17),
    fontStyle: 'normal',
    height: convertPxToRem(24),
    lineHeight: convertPxToRem(24),
    textAlign: 'left',
}));

export const StyledSubHeader = styled(Box)(() => ({
    color: `rgba(89, 89, 89, 1)`,
    fontSize: convertPxToRem(15),
    fontStyle: 'normal',
    lineHeight: convertPxToRem(23),
}));

// Sample and Promo Badge
export const VariantPromoSampleBadge = styled(Box)(({ theme }) => ({
    display: 'flex',
    height: convertPxToRem(20),
    padding: `0 ${convertPxToRem(6)} ${convertPxToRem(1)} ${convertPxToRem(6)}`,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: convertPxToRem(6),
    background: theme.palette.grey03.main,
    marginBottom: convertPxToRem(8),
    width: convertPxToRem(50),
}));

export const VariantPromoSampleTypo = styled(Typography)(({ theme }) => ({
    ...theme.typography.smallTextStrong,
    color: theme.palette.grey08.main,
    fontSize: convertPxToRem(11),
    textAlign: 'center',
}));

export const GlobalBadge = styled(VerifiedIcon)(({ theme }) => ({
    color: theme.palette.green06.main,
}));

export const VariantField = styled(Box)({
    padding: `${convertPxToRem(8)} 0`,
});
