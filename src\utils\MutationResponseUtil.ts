import { MutationResponse } from '../hooks/useSaveData';

export interface MutationErrorData {
    title: string;
    errors: {
        entityName?: string;
        messages: string[];
    }[];
}
const getErrors = (
    error: any,
    getEntityName?: (entity?: any) => string | undefined,
): {
    entityName?: string;
    messages: string[];
}[] => {
    const responseErrors = error.response?.data?.failed ?? error;
    if (responseErrors.message) {
        return [{ entityName: getEntityName?.(error.entity), messages: [error.message] }];
    }
    if (responseErrors && Array.isArray(responseErrors)) {
        return responseErrors.reduce(
            (acc: string[], e: any) => [...acc, { entityName: getEntityName?.(e.entity), messages: [e.errors] }],
            [],
        );
    }
    return [];
};

const filterMutationErrors = <T>(
    mutationResult: MutationResponse<T>,
    getEntityName?: (entity?: any) => string | undefined,
) => {
    const errors: MutationErrorData[] = [];
    if (mutationResult.createData?.error) {
        errors.push({
            title: 'Create Failed',
            errors: getErrors(mutationResult.createData?.error, getEntityName),
        });
    }
    if (mutationResult.updateData?.error) {
        errors.push({ title: 'Update Failed', errors: getErrors(mutationResult.updateData?.error, getEntityName) });
    }
    if (mutationResult.deleteData?.error) {
        errors.push({ title: 'Delete Failed', errors: getErrors(mutationResult.deleteData?.error, getEntityName) });
    }
    return errors.length > 0 ? errors : undefined;
};

export default filterMutationErrors;
