import { IconName } from '@treez-inc/component-library/dist/components/Icon/types';
import PRODUCT_CATEGORIES from './product-categories-data';

const findCategoryIcon = (categoryName: string): IconName => {
    switch (categoryName) {
        case PRODUCT_CATEGORIES.Beverage:
            return 'Beverage';
        case PRODUCT_CATEGORIES.Cartridge:
            return 'Cartridge';
        case PRODUCT_CATEGORIES.Edible:
            return 'Edibles';
        case PRODUCT_CATEGORIES.Extract:
            return 'Extracts';
        case PRODUCT_CATEGORIES.Flower:
            return 'Flower';
        case PRODUCT_CATEGORIES.Merch:
            return 'Merch';
        case PRODUCT_CATEGORIES.Misc:
            return 'Miscellaneous';
        case PRODUCT_CATEGORIES.Pill:
            return 'Pill';
        case PRODUCT_CATEGORIES.Plant:
            return 'Plant';
        case PRODUCT_CATEGORIES.Preroll:
            return 'PreRoll';
        case PRODUCT_CATEGORIES.Tincture:
            return 'Tincture';
        case PRODUCT_CATEGORIES.Topical:
            return 'Topical';
        default:
            return 'Release';
    }
};

export default findCategoryIcon;
