/**
 * Converts a string into title case.
 * @param value string
 * @returns Returns string in title case.
 */
export const convertToTitleCase = (value: string): string =>
    value?.toLowerCase().replace(/(^|\s)\w/g, (char) => char.toUpperCase());

export const convertToNumber = (inputString: string): Number | null => {
    let input = inputString.trim();
    if (input.match(/^\.[0-9]+/)) {
        input = `0${input}`; // e.g. '.5' -> '0.5'
    }
    const parsedValue = parseInt(input, 10);
    const isInvalid = Number.isNaN(parsedValue);
    return isInvalid ? null : parsedValue;
};

/**
 * Checks if `value` is `null` or `undefined` or an empty string.
 * @param value any
 * @returns Returns `true` if `value` is `null` or `undefined`, else `false`.
 */
export const isNil = (value: any): boolean => value === undefined || value === null || value === '';

/**
 * Checks if the value is empty.
 * @param value any | object | array
 * @returns Returns `true` if the value is an empty array or object.
 */
export const isEmpty = (value: any): boolean => {
    if (isNil(value)) {
        return true;
    }

    if (Array.isArray(value)) {
        return value?.length === 0;
    }

    if (value instanceof Date) {
        return false;
    }

    if (typeof value === 'object') {
        return Object.keys(value).length === 0;
    }

    return false;
};

/**
 * Performs a loose equality check between two values.
 *
 * - Treats null, empty string (''), and 0 as equal.
 * - Treats string numbers (e.g., `'123'`) and numbers (`123`) as equal.
 * - Behaves similarly to JavaScript's `==` but with special handling for `null`, `''` and `0`.
 *
 * @param {*} a - The first value to compare.
 * @param {*} b - The second value to compare.
 * @returns {boolean} Returns `true` if the values are considered equal, else `false`.
 *
 * @example
 * isLooselyEqual(null, '')            // → true
 * isLooselyEqual(0, '')               // → true
 * isLooselyEqual('', null)            // → true
 * isLooselyEqual(123, 123)            // → true
 * isLooselyEqual('123', 123)          // → true
 * isLooselyEqual('abc', 'abc')        // → true
 * isLooselyEqual('123', '124')        // → false
 * isLooselyEqual(null, 'abc')         // → false
 */
export const isLooselyEqual = (a: any, b: any): boolean => {
    const normalize = (v: any) => (v == null || v === '' || v === 0 ? '__empty__' : v);
    // eslint-disable-next-line eqeqeq
    return normalize(a) == normalize(b);
};

export const cleanData = (data: any, removeValues: any[] = [undefined, '']): any => {
    if (typeof data === 'string') {
        return data;
    }
    if (Array.isArray(data)) {
        return data.map((v) => cleanData(v)).filter(Boolean);
    }
    const updated = Object.keys(data).reduce((obj, key) => {
        if (data[key] !== null && (Array.isArray(data[key]) || typeof data[key] === 'object')) {
            const newValue = cleanData(data[key]);
            return { ...obj, [key]: newValue };
        }
        if (removeValues.some((rv) => rv === data[key])) {
            return obj;
        }
        return { ...obj, [key]: data[key] };
    }, {});

    return updated;
};

export const hasDiff = (leftObject: any, rightObject: any, innerObject = false): boolean => {
    const allProps = innerObject ? Object.keys({ ...rightObject, ...leftObject }) : Object.keys(rightObject);

    const data = allProps.some((key: string) => {
        const lValue = leftObject[key];
        const rValue = rightObject[key];
        if (lValue && rValue && Array.isArray(lValue)) {
            if (lValue.length === rValue.length) {
                return rValue.some((r: any, i: number) => hasDiff(r, lValue[i]));
            }
            return true;
        }
        if (lValue && rValue && typeof lValue === 'object') {
            return hasDiff(lValue, rValue, true);
        }

        return lValue !== rValue;
    });
    return data;
};

export const dataDiffInObjs = (
    currentObject: any,
    newObject: any,
    excludedKeys: string[] = [],
    inclusiveKeys: string[] = [],
    innerObject = false,
) => {
    const mandatoryValues = excludedKeys.reduce(
        (acc: any, eKey: string) => ({
            ...acc,
            [eKey]: currentObject[eKey],
        }),
        {},
    );
    const allProps = innerObject ? Object.keys({ ...newObject, ...currentObject }) : Object.keys(newObject);
    const allOtherProps = allProps.filter((key: string) => !excludedKeys.includes(key));

    const result = allOtherProps.reduce((diff: any, key: string) => {
        const currentPropValue = currentObject[key];
        const newPropValue = newObject[key];
        if (currentPropValue && newPropValue && Array.isArray(currentPropValue)) {
            if (currentPropValue.length === newPropValue.length) {
                const isDiff: boolean = newPropValue.some((r: any, i: number) => hasDiff(r, currentPropValue[i]));
                return isDiff ? { ...diff, [key]: newPropValue } : diff;
            }
            return { ...diff, [key]: newPropValue };
        }
        if (currentPropValue && newPropValue && typeof currentPropValue === 'object') {
            const isDiff: boolean = hasDiff(currentPropValue, newPropValue, true);
            const isInclusiveKey: boolean = inclusiveKeys.some((k: string) => k === key);

            if (isDiff && isInclusiveKey) {
                // eslint-disable-next-line no-param-reassign
                diff = {
                    ...diff,
                    [key]: {
                        ...currentPropValue,
                        ...dataDiffInObjs(currentPropValue, newPropValue, [], inclusiveKeys, true),
                    },
                };
            } else if (isDiff && !isInclusiveKey) {
                // eslint-disable-next-line no-param-reassign
                diff = {
                    ...diff,
                    [key]: {
                        ...dataDiffInObjs(currentPropValue, newPropValue, [], inclusiveKeys, true),
                    },
                };
            }
            return diff;
        }

        return currentPropValue !== newPropValue ? { ...diff, [key]: newPropValue } : diff;
    }, {});

    if (!isEmpty(result)) {
        return {
            ...mandatoryValues,
            ...result,
        };
    }
    return undefined;
};

export const dataDiffInArrays = (
    currentArray: any[],
    newArray: any[],
    excludedKeys: string[] = [],
    uniqKey: string = 'id',
): any[] => {
    const result = newArray.reduce((diff: any[], newObject: any) => {
        const currentObject = currentArray.find((curr) => curr[uniqKey] === newObject[uniqKey]);
        if (currentObject) {
            const objDiff = dataDiffInObjs(currentObject, newObject, excludedKeys);
            return [...diff, objDiff];
        }
        return [...diff, newObject];
    }, []);
    return result.filter(Boolean);
};

export const filterUpdates = (leftArray: any[], rightArray: any[], uniqkey = 'id') => {
    const data = rightArray.reduce((result: any[], rValue: any) => {
        const lValue = leftArray.find((l) => l[uniqkey] === rValue[uniqkey]);
        if (lValue) {
            const updatedRValue = { ...rValue };
            if (!updatedRValue.name || updatedRValue.name.trim() === '') {
                updatedRValue.name = lValue.name;
            }

            const diff = hasDiff(lValue, rValue);
            if (diff) {
                return [...result, dataDiffInObjs(lValue, rValue, ['id'], ['details'], true)];
            }
            return result;
        }
        return [...result, rValue];
    }, []);

    return isEmpty(data) ? undefined : data;
};

export const sortEntities = (entity: any) => {
    entity?.sort((a: any, b: any) => {
        const nameA = a.name?.toLowerCase() || a.displayName?.toLowerCase();
        const nameB = b.name?.toLowerCase() || b.displayName?.toLowerCase();

        if (nameA < nameB) {
            return -1;
        }
        if (nameA > nameB) {
            return 1;
        }
        return 0;
    });
    return entity;
};

export const alphabeticalAscOrder = (data: any[], displayName: any) =>
    data.sort((a, b) => a[displayName].toLowerCase().localeCompare(b[displayName].toLowerCase()));

/**
 * Creates a safe wrapper for a function that may be undefined
 * @param fn The function to wrap
 * @param args The arguments to pass to the function
 * @returns A safe version of the function that won't throw if undefined
 */
export const ensureFunction =
    <T extends (...args: any[]) => any>(fn: T | undefined): ((...args: Parameters<T>) => ReturnType<T> | void) =>
    // eslint-disable-next-line consistent-return
    (...args: Parameters<T>) => {
        if (typeof fn === 'function') {
            return fn(...args);
        }
    };
