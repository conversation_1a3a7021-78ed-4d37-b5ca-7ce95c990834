import { MenuItemCheckboxProps } from '@treez-inc/component-library';
import PRODUCT_CATEGORIES from './product-categories-data';

/* eslint-disable */
const PRODUCT_MANAGEMENT_SERVICE_API_URL = process.env.PRODUCT_MANAGEMENT_SERVICE_API_URL;
const PRODUCT_COLLECTION_SERVICE_API_URL = process.env.PRODUCT_COLLECTION_SERVICE_API_URL;

export const ORGANIZATION_MANAGEMENT_API_URL = process.env.ORGANIZATION_MANAGEMENT_API_URL;

const STAGE = process.env.STAGE;

const IMAGE_CDN_URL = `https://cdn.${STAGE === 'prod' ? 'mso' : STAGE}.treez.io`;

// NOTE: These are replaced at compile time
export const VERSION_TAG = '_version_tag_';
export const VERSION_COMMIT = '_version_commit_';
export const VERSION_BUILD_DATE = '_version_build_date_';

export const BASE_PATH: string = '/product-control';
export const ADD_PRODUCT_URL: string = `${BASE_PATH}/create`;
export const EDIT_PRODUCT_URL: string = `${BASE_PATH}/edit`;
export const PRICING_PAGE_URL: string = `${BASE_PATH}/pricing`;

export const DUPLICATE_VARIANT_ERROR_MSG: string = 'Duplicate size or merchandise size are not allowed';
export const ACTIVATE_VARIANT_TOOL_TIP_MSG: string = 'Reactivate the parent product first to reactivate this variant.';
export const MASS_PRICE_UPDATE_MSG: string =
    'Changing the price at the product line level changes the price across all stores and menus of that type. If you want to update store specific, use the store filter to select the stores you would like to update pricing in.';
export const MASS_PRICE_UPDATE_ROW_ID: string = 'massPriceUpdate';
export const PAGE_SIZE_FOR_PRICING_PAGE: number = 10;

export default { PRODUCT_MANAGEMENT_SERVICE_API_URL, STAGE, IMAGE_CDN_URL, PRODUCT_COLLECTION_SERVICE_API_URL };

export const DEBOUNCE_TIME = 500;
// Static Chips Color
export const chipColor: any = {
    Effects: 'gray',
    Aroma: 'yellow',
    Flavor: 'orange',
    Internal: 'peach',
    General: 'blue',
    Ingredients: 'purple',
};

// Common fields for variant types (Sample or Promo)
export const variantDetailFields = [
    'totalMgCbd',
    'totalMgThc',
    'totalFlowerWeight',
    'usableMarijuanaWeight',
    'doses',
    'cbdPerDose',
    'thcPerDose',
    'grossWeight',
    'netWeight',
    'netWeightUom',
    'totalConcentrateWeight',
];

// categories - categoryFields - fields that are shown for each category
//  interface / type / structure: { "category(enum?): string": { ["fieldName(enum?): string"]: boolean }}

export const categories: any = {
    pill: {
        classification: true,
        strain: true,
        size: true,
        totalMgThc: true,
        totalMgCbd: true,
        doses: true,
        thcPerDose: true,
        cbdPerDose: true,
        totalFlowerWeight: true,
        netWeight: true,
        netWeightUom: true,
    },
    tincture: {
        classification: true,
        strain: true,
        size: true,
        totalMgThc: true,
        totalMgCbd: true,
        doses: true,
        thcPerDose: true,
        cbdPerDose: true,
        totalFlowerWeight: true,
        netWeight: true,
        netWeightUom: true,
    },
    cartridge: {
        classification: true,
        strain: true,
        extractionMethod: true,
        size: true,
        totalMgThc: true,
        totalMgCbd: true,
        totalFlowerWeight: true,
        netWeight: true,
        netWeightUom: true,
    },
    misc: {
        classification: true,
        strain: true,
        size: true,
        totalMgThc: true,
        totalMgCbd: true,
        doses: true,
        thcPerDose: true,
        cbdPerDose: true,
        totalFlowerWeight: true,
        netWeight: true,
        netWeightUom: true,
    },
    preroll: {
        classification: true,
        strain: true,
        size: true,
        totalMgThc: true,
        totalMgCbd: true,
        netWeight: true,
        netWeightUom: true,
        totalFlowerWeight: true,
        totalConcentrateWeight: true,
    },
    flower: {
        classification: true,
        strain: true,
        size: true,
        totalMgThc: true,
        totalMgCbd: true,
        netWeight: true,
        netWeightUom: true,
        totalFlowerWeight: true,
        totalConcentrateWeight: true,
    },
    extract: {
        classification: true,
        strain: true,
        extractionMethod: true,
        size: true,
        totalMgThc: true,
        totalMgCbd: true,
        totalFlowerWeight: true,
    },
    edible: {
        classification: true,
        strain: true,
        size: true,
        totalMgThc: true,
        totalMgCbd: true,
        doses: true,
        thcPerDose: true,
        cbdPerDose: true,
        totalFlowerWeight: true,
        netWeight: true,
        netWeightUom: true,
    },
    beverage: {
        classification: true,
        strain: true,
        size: true,
        totalMgThc: true,
        totalMgCbd: true,
        doses: true,
        thcPerDose: true,
        cbdPerDose: true,
        totalFlowerWeight: true,
        netWeight: true,
        netWeightUom: true,
    },
    plant: {
        classification: true,
        strain: true,
        size: true,
    },
    topical: {
        classification: true,
        strain: true,
        size: true,
        totalMgThc: true,
        totalMgCbd: true,
        doses: true,
        thcPerDose: true,
        cbdPerDose: true,
        totalFlowerWeight: true,
        netWeight: true,
        netWeightUom: true,
    },
    merch: {
        merchandiseSize: true,
    },
    noninv: {
        size: true,
    },
};

export enum RegionType {
    Alabama = 'Alabama',
    Alaska = 'Alaska',
    Arizona = 'Arizona',
    Arkansas = 'Arkansas',
    California = 'California',
    Colorado = 'Colorado',
    Connecticut = 'Connecticut',
    Delaware = 'Delaware',
    DistrictOfColumbia = 'District of Columbia',
    Florida = 'Florida',
    Georgia = 'Georgia',
    Hawaii = 'Hawaii',
    Idaho = 'Idaho',
    Illinois = 'Illinois',
    Indiana = 'Indiana',
    Iowa = 'Iowa',
    Kansas = 'Kansas',
    Kentucky = 'Kentucky',
    Louisiana = 'Louisiana',
    Maine = 'Maine',
    Maryland = 'Maryland',
    Massachusetts = 'Massachusetts',
    Michigan = 'Michigan',
    Minnesota = 'Minnesota',
    Mississippi = 'Mississippi',
    Montana = 'Montana',
    Nevada = 'Nevada',
    NewHampshire = 'New Hampshire',
    NewJersey = 'New Jersey',
    NewMexico = 'New Mexico',
    NewYork = 'New York',
    NorthCarolina = 'North Carolina',
    NorthDakota = 'North Dakota',
    Ohio = 'Ohio',
    Oklahoma = 'Oklahoma',
    Oregon = 'Oregon',
    Pennsylvania = 'Pennsylvania',
    RhodeIsland = 'Rhode Island',
    SouthCarolina = 'South Carolina',
    SouthDakota = 'South Dakota',
    Tennessee = 'Tennessee',
    Texas = 'Texas',
    Utah = 'Utah',
    Vermont = 'Vermont',
    Virginia = 'Virginia',
    VirginIslands = 'Virgin Islands',
    Washington = 'Washington',
    WestVirginia = 'West Virginia',
    Wisconsin = 'Wisconsin',
    Wyoming = 'Wyoming',
}

export const statusMap: any = {
    active: 'Active',
    draft: 'Draft',
    inactive: 'Deactivated',
};

export const sizeLabelMap: any = {
    grams: 'g',
    milligrams: 'mg',
    liters: 'l',
    milliliters: 'ml',
    'fluid ounces': 'fl oz',
    ounces: 'oz',
};

export const merchandiseSizeMap: any = {
    small: 'S',
    medium: 'M',
    large: 'L',
    'one size': 'O/S',
};

export enum Status {
    ACTIVE = 'active',
    INACTIVE = 'inactive',
    DRAFT = 'draft',
    MERGE = 'merge',
}

export const productStatusList: MenuItemCheckboxProps[] = [
    {
        key: Status.ACTIVE,
        value: Status.ACTIVE,
        label: 'Active',
        onChange: () => {},
    },
    {
        key: Status.INACTIVE,
        value: Status.INACTIVE,
        label: 'Deactivated',
        onChange: () => {},
    },
    // PLEASE DO NOT REMOVE. NEEDED FOR MANUAL TESTING.
    // {
    //     key: Status.DRAFT,
    //     value: Status.DRAFT,
    //     label: 'Drafts',
    // },
];

export const isHeavilyManufacturedGood = (productCategoryName: string) =>
    [
        PRODUCT_CATEGORIES.Beverage,
        PRODUCT_CATEGORIES.Edible,
        PRODUCT_CATEGORIES.Pill,
        PRODUCT_CATEGORIES.Tincture,
        PRODUCT_CATEGORIES.Topical,
    ].includes(productCategoryName as PRODUCT_CATEGORIES);

export const uomTypes = {
    EACH: 'each',
    FLUID_OUNCES: 'fluid ounces',
    GRAMS: 'grams',
    GALLONS: 'gallons',
    KILOGRAMS: 'kilograms',
    LITERS: 'liters',
    POUNDS: 'pounds',
    MILLIGRAMS: 'milligrams',
    MILLILITERS: 'milliliters',
    OUNCES: 'ounces',
    PINT: 'pint',
    QUART: 'quart',
} as const;

export enum PricingMethodType {
    FLAT = 'FLAT',
    TIER = 'TIER',
}

export const BASE_PRICING_METHODS = [
    {
        displayValue: PricingMethodType.FLAT,
        displayName: 'Flat Pricing',
    },
    {
        displayValue: PricingMethodType.TIER,
        displayName: 'Tier Pricing',
    },
];
