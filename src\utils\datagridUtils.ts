import { MenuItemCheckboxProps } from '@treez-inc/component-library';
import { SkuDto } from '../interfaces/dto/sku';

export const filterKeyFn = (data: any[]): MenuItemCheckboxProps[] =>
    data?.map((d) => ({
        key: d.id,
        label: d.name,
        checked: false,
        value: d.id,
        onChange: () => {},
    }));

export const mapData = (value: string, columnData: any) => columnData[value?.toLowerCase()] || value;

export const formatVariantSku = (variants: SkuDto[]): string[] => {
    const formattedSkus = variants.reduce((skus: string[], variant: SkuDto) => {
        if (variant?.sku) {
            skus.push(variant.sku);
        }
        if (variant?.additionalSku?.length) {
            skus.push(...variant.additionalSku);
        }
        return skus;
    }, [] as string[]);

    return formattedSkus;
};
