// Used to detect click events that happens outside the scope of component passed as argument
export default function detectOutsideClick(
    listening: boolean,
    setListening: any,
    optionsRef: any,
    setToggleOptions: any,
) {
    if (listening) return;
    if (!optionsRef.current) return;
    setListening(true);
    /* eslint-disable @typescript-eslint/no-unused-vars */
    ['click', 'touchstart'].forEach((type) => {
        document.addEventListener('click', (evt) => {
            const cur = optionsRef.current;
            const node = evt.target;
            if (cur?.contains(node)) return;
            setToggleOptions(false);
        });
    });
}
