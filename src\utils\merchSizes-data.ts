import { SizeVariantProps } from '../interfaces/ProductProps';

export const MERCH_SIZE = {
    Small: 'Small',
    Medium: 'Medium',
    Large: 'Large',
    OneSize: 'One Size',
    XS: 'XS',
    XL: 'XL',
    '2XL': '2XL',
    '3XL': '3XL',
    '4XL': '4XL',
    '5XL': '5XL',
} as const;

export const MERCH_SIZES: SizeVariantProps[] = Object.values(MERCH_SIZE).map((size) => ({
    displayName: size,
    displayValue: size,
}));
