import { dollarsToCents } from './priceUtils';

// src/utils/priceUtils.test.ts

describe('dollarsToCents', () => {
    it('should convert a string dollar value to cents', () => {
        expect(dollarsToCents('10.25')).toBe(1025);
        expect(dollarsToCents('0.99')).toBe(99);
        expect(dollarsToCents('100')).toBe(10000);
    });

    it('should convert a number dollar value to cents', () => {
        expect(dollarsToCents(10.25)).toBe(1025);
        expect(dollarsToCents(0.99)).toBe(99);
        expect(dollarsToCents(100)).toBe(10000);
    });

    it('should handle rounding correctly for floating point values', () => {
        expect(dollarsToCents(10.255)).toBe(1026); // Rounded up
        expect(dollarsToCents(10.254)).toBe(1025); // Rounded down
        expect(dollarsToCents('10.255')).toBe(1026); // Rounded up
        expect(dollarsToCents('10.254')).toBe(1025); // Rounded down
    });

    it('should return NaN for invalid string inputs', () => {
        expect(dollarsToCents('abc')).toBeNaN();
        expect(dollarsToCents('')).toBeNaN();
    });

    it('should handle edge cases like zero and negative values', () => {
        expect(dollarsToCents(0)).toBe(0);
        expect(dollarsToCents('0')).toBe(0);
        expect(dollarsToCents(-10.25)).toBe(-1025);
        expect(dollarsToCents('-10.25')).toBe(-1025);
    });

    it('should handle edge cases that previously failed with the new pricing form util function', () => {
        const previousPriceConversionFunction = (v: string | number) => (typeof v === 'number' ? v * 100 : Number(v) * 100);

        expect(dollarsToCents('8.96')).toBe(896);
        expect(previousPriceConversionFunction('8.96')).not.toBe(896);

        expect(dollarsToCents('8.97')).toBe(897);
        expect(previousPriceConversionFunction('8.97')).not.toBe(897);
    });
});

// We recommend installing an extension to run jest tests.