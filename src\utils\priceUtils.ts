// Cents should normally be stored as an integer value
// This function returns the dollar value as a string with two decimal places,

//  or "NaN" if the input value was undefined
export function centsToDollars(cents: number | string): string {
    let floatDollars;

    if (typeof cents === 'string') {
        floatDollars = parseFloat(cents) / 100;
    } else {
        floatDollars = cents / 100;
    }

    const dollars = floatDollars.toFixed(2);
    return dollars;
}

// Dollars should be a string value with two decimal places
// This function returns the cents as an integer value,
//  or NaN if the input value was "NaN" or NaN or undefined
export function dollarsToCents(dollars: string | number): number {
    let floatCents;
    if (typeof dollars === 'number') {
        floatCents = dollars * 100;
    } else {
        floatCents = parseFloat(dollars) * 100;
    }
    // As soon as we have a floating point number we need to include rounding
    const cents = parseInt(floatCents.toFixed(0), 10);
    return cents;
}

export const getPriceDiffs = (oldRow: any, newRow: any) => {
    const keys: string[] = Object.keys(oldRow);
    return keys.reduce((result: any, key: string) => {
        const oldValue = oldRow[key];
        const newValue = newRow[key];
        if (oldValue !== newValue) return { ...result, [key]: newValue };
        return result;
    }, {});
};

export const getPriceRange = (prices: number[]): string | null => {
    const min = prices.length ? Math.min(...prices) : 0;
    const max = prices.length ? Math.max(...prices) : 0;
    if (min && max) {
        if (min !== max) {
            return `${centsToDollars(min)} - ${centsToDollars(max)}`;
        }
        return `${centsToDollars(min)}`;
    }
    if (!min && max) {
        return `${centsToDollars(max)}`;
    }
    return `-`;
};

export const formatPriceInput = (value: string | number | null) => {
    if (value === null) return null;
    const stringValue = typeof value === 'number' ? String(value) : value;
    const numericValue = stringValue?.replace(/[^0-9.]/g, '');
    const parts = numericValue?.split('.');

    // Limit the integer part to 6 digits
    if (parts?.[0]?.length > 6) {
        parts[0] = parts[0].slice(0, 6);
    }
    // If there is a decimal part, limit it to 2 digits
    if (parts?.length > 1) {
        parts[1] = parts[1].slice(0, 2);
    }

    return parts?.join('.');
};

export const STRING_PRICE_ZERO = '0.00';
export const REQUIRED_PRICE_ERROR = `Price cannot be $${STRING_PRICE_ZERO}.`;
