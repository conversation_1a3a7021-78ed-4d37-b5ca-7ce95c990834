/* eslint-disable @typescript-eslint/naming-convention */

import { IconName } from '@treez-inc/component-library/dist/components/Icon/types';

enum PRODUCT_CATEGORIES {
    Beverage = 'Beverage',
    CBD = 'CBD',
    Cartridge = 'Cartridge',
    Edible = 'Edible',
    Extract = 'Extract',
    Flower = 'Flower',
    Merch = 'Merch',
    Misc = 'Misc',
    NonInv = 'Non-Inv',
    Pill = 'Pill',
    Plant = 'Plant',
    Preroll = 'Preroll',
    Tincture = 'Tincture',
    Topical = 'Topical',
}

export enum PRODUCT_PACKAGED_GOODS {
    Edible = 'Edible',
    Beverage = 'Beverage',
    Misc = 'Misc',
    Pill = 'Pill',
    Tincture = 'Tincture',
    Topical = 'Topical',
}

interface ProductCategory {
    iconName: IconName;
    displayName: string;
}

export const PRODUCT_CATEGORIES_ICONS: Record<string, ProductCategory> = {
    Beverage: {
        iconName: 'Beverage',
        displayName: 'BEVERAGE',
    },
    Cartridge: {
        iconName: 'Cartridge',
        displayName: 'CARTRIDGE',
    },
    CBD: {
        iconName: 'CBD',
        displayName: 'CBD',
    },
    Edible: {
        iconName: 'Edibles',
        displayName: 'EDIBLE',
    },
    Extract: {
        iconName: 'Extracts',
        displayName: 'EXTRACT',
    },
    Flower: {
        iconName: 'Flower',
        displayName: 'FLOWER',
    },
    Merch: {
        iconName: 'Merch',
        displayName: 'MERCH',
    },
    Misc: {
        iconName: 'Miscellaneous',
        displayName: 'MISC',
    },
    Pill: {
        iconName: 'Pill',
        displayName: 'PILL',
    },
    Plant: {
        iconName: 'Plant',
        displayName: 'PLANT',
    },
    Preroll: {
        iconName: 'PreRoll',
        displayName: 'PREROLL',
    },
    Tincture: {
        iconName: 'Tincture',
        displayName: 'TINCTURE',
    },
    Topical: {
        iconName: 'Topical',
        displayName: 'TOPICAL',
    },
};

export default PRODUCT_CATEGORIES;

export enum SKU_RUN_TIME_FIELDS {
    SAME_AS_AMOUNT = 'sameAsAmount',
}

export enum COMPLIANCE_FIELDS_ENUM {
    CBD_PER_DOSE = 'cbdPerDose',
    DOSES = 'doses',
    GROSS_WEIGHT = 'grossWeight',
    NET_WEIGHT = 'netWeight',
    NET_WEIGHT_UOM = 'netWeightUom',
    THC_PER_DOSE = 'thcPerDose',
    TOTAL_CONCENTRATE_WEIGHT = 'totalConcentrateWeight',
    TOTAL_FLOWER_WEIGHT = 'totalFlowerWeight',
    TOTAL_MG_THC = 'totalMgThc',
    TOTAL_MG_CBD = 'totalMgCbd',
    USABLE_MARIJUANA_WEIGHT = 'usableMarijuanaWeight',
}
