interface HasLabel {
    label: string;
}

function removeDuplicates<T extends HasLabel>(data: T[]): T[] {
    const seenLabels = new Set();
    const uniqueValues = data.filter((value: any) => {
        const normalizedLabel = value.label.toLowerCase();
        if (seenLabels.has(normalizedLabel)) {
            return false;
        }
        seenLabels.add(normalizedLabel);
        return true;
    });

    return uniqueValues;
}

export default removeDuplicates;
