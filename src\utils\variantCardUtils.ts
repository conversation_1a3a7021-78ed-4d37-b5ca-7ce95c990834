import { ProductData } from '../interfaces/dto/productData';
import { ChildSkuDto, SkuApiDto, SkuBasePriceData, SkuDto, SkuType } from '../interfaces/dto/sku';
import { isEmpty } from './common';
import { MERCH_SIZE } from './merchSizes-data';
import { dollarsToCents } from './priceUtils';
import PRODUCT_CATEGORIES from './product-categories-data';

const uomMapping = {
    each: 'each',
    'fluid ounces': 'fl-oz',
    grams: 'g',
    gallons: 'gal',
    kilograms: 'kg',
    liters: 'l',
    pounds: 'lb',
    milligrams: 'mg',
    milliliters: 'ml',
    ounces: 'oz',
    pint: 'pt',
    quart: 'qt',
} as const;

type UomMappingKey = keyof typeof uomMapping;

// Readme: Preserved old price range algo
export const formatVariantPriceRange = (variants: any) => {
    let priceMin = (0).toLocaleString('en-us', { style: 'currency', currency: 'USD' });
    let priceMax = (0).toLocaleString('en-us', { style: 'currency', currency: 'USD' });

    if (variants?.length) {
        const minimumPrice: any = variants.reduce((pre: any, cur: any) => {
            if (!cur.defaultPrices.base) {
                return pre;
            }
            return Number(pre?.defaultPrices.base) < Number(cur?.defaultPrices.base) ? pre : cur;
        });

        const maximumPrice: any = variants.reduce((pre: any, cur: any) => {
            if (!cur.defaultPrices.base) {
                return pre;
            }
            return Number(pre?.defaultPrices.base) > Number(cur?.defaultPrices.base) ? pre : cur;
        });

        if (Number(minimumPrice.defaultPrices.base) > 0) {
            priceMin = (Number(minimumPrice.defaultPrices.base) / 100).toLocaleString('en-us', {
                style: 'currency',
                currency: 'USD',
            });
        }

        if (Number(maximumPrice.defaultPrices.base) > 0) {
            priceMax = (Number(maximumPrice.defaultPrices.base) / 100).toLocaleString('en-us', {
                style: 'currency',
                currency: 'USD',
            });
        }
    }

    return `${priceMin} - ${priceMax}`;
};

// Modify SKU as per length rule.
export const formatSkuValue = (sku: string) => {
    let modifiedSku = sku;

    if (sku && sku.length > 7) {
        const firstChar = sku.charAt(0);
        const lastThreeChar = sku.slice(-3);
        modifiedSku = `${firstChar}...${lastThreeChar}`;
    } else if (sku && sku.length === 0) {
        modifiedSku = '-';
    }

    return modifiedSku;
};

// Convert UOM to Ounce
export const convertUomSizeToOunce = (variant: Record<string, any>) => {
    let divisor = 28;
    const ratioToOunce = 28.3495;

    // use the simplified ratio
    const gcd: any = (a: number, b: number) => (b === 0 ? a : gcd(b, a % b));

    if (variant.uom.toLowerCase() !== 'grams') {
        return '';
    }
    if (variant.uom.toLowerCase() === 'grams') {
        divisor = gcd(variant.amount, Math.floor(ratioToOunce));
    }

    const numerator = variant.amount ? variant.amount / divisor : 1;
    const denominator = Math.floor(Math.ceil((ratioToOunce / divisor) * 10) / 10);

    return `${numerator}/${denominator}`;
};

export const calculateVariantSizeLabel = (
    variant: Pick<SkuDto, 'amount' | 'uom' | 'unitCount' | 'merchandiseSize' | 'details'>,
    productCategoryName: string,
) => {
    const { amount, uom: variantUom, unitCount: variantUnitCount, merchandiseSize } = variant ?? {};

    const uom = uomMapping[(variantUom ?? '').toLowerCase() as UomMappingKey] ?? variantUom;
    const unitCount = variantUnitCount ?? 1;
    const hasMultipleUnits = unitCount > 1;

    const dashPrefix = hasMultipleUnits ? ' - ' : '';
    const packString = hasMultipleUnits ? `${unitCount} Pack` : '';
    const packOrEach = packString || 'Each';

    switch (productCategoryName) {
        case PRODUCT_CATEGORIES.Merch:
            if (merchandiseSize) {
                return `${merchandiseSize}${dashPrefix}${packString}`;
            }
            return MERCH_SIZE.OneSize;
        case PRODUCT_CATEGORIES.Misc:
            if (amount && uom) {
                return `${amount} ${uom}${dashPrefix}${packString}`;
            }
            if (merchandiseSize) {
                return `${merchandiseSize}${dashPrefix}${packString}`;
            }
            return packOrEach;
        case PRODUCT_CATEGORIES.Cartridge:
        case PRODUCT_CATEGORIES.Extract:
            return `${amount ?? ''} ${uom}${dashPrefix}${packString}`;
        default:
            if (amount && uom) {
                return `${amount} ${uom}${dashPrefix}${packString}`;
            }
            return packOrEach;
    }
};

export const getVariantSizeLabel = (variant: SkuDto, productCategoryName: string) => {
    const details = variant?.details;
    const isSample: boolean | undefined = details?.isSample;
    const isPromo: boolean | undefined = details?.isPromo;

    let variantLabel: string = calculateVariantSizeLabel(variant, productCategoryName).trim();

    if (isSample) {
        variantLabel += ` - Sample`;
    }
    if (isPromo) {
        variantLabel += ` - Promo`;
    }

    return variantLabel.trim();
};

export const getVariantName = (product: ProductData, variant: SkuDto) => {
    const trimmedVariantName = variant.name && variant.name.trim();

    const name =
        trimmedVariantName && trimmedVariantName !== ''
            ? trimmedVariantName
            : `${product.name} - ${getVariantSizeLabel(variant, product?.productCategory?.name ?? '')}`;

    return name;
};

export const sortVariants = (variants: SkuDto[], categoryName: string) => {
    const parents: SkuDto[] = variants
        .filter(
            (v) =>
                (isEmpty(v.parentId) && !v.details?.isSample && !v.details?.isPromo) ||
                (!isEmpty(v.parentId) && !variants.some((p) => p.id === v.parentId)),
        )
        .map((p) => ({
            ...p,
            images: p?.images ? [...p.images].sort((a: any, b: any) => a.order - b.order) : [],
            children: [],
        }));

    variants?.forEach((variant: SkuDto) => {
        if (variant.details?.isSample || variant.details?.isPromo) {
            const generatedLabel = getVariantSizeLabel(variant, categoryName).toLowerCase();
            const parentIndex = parents.findIndex(
                (p) =>
                    p.id === variant.parentId ||
                    `${getVariantSizeLabel(p, categoryName)} - sample`.toLowerCase() === generatedLabel ||
                    `${getVariantSizeLabel(p, categoryName)} - promo`.toLowerCase() === generatedLabel,
            );
            if (parentIndex !== -1) {
                parents.splice(parentIndex + 1, 0, variant);
            }
        }
    });
    return parents;
};

export const isBaseSku = (sku: SkuDto): boolean => !sku.parentId && !sku.details?.isSample && !sku.details?.isPromo;

export const isPromoSku = (sku: SkuDto): boolean => Boolean(sku.details?.isPromo) && !sku.details?.isSample;

export const isSampleSku = (sku: SkuDto): boolean => !sku.details?.isPromo && Boolean(sku.details?.isSample);

export const getSkuType = (sku: SkuDto): SkuType => {
    switch (true) {
        case isBaseSku(sku):
            return SkuType.BASE;
        case isPromoSku(sku):
            return SkuType.PROMO;
        case isSampleSku(sku):
            return SkuType.SAMPLE;
        default:
            return SkuType.NONE;
    }
};

export const getChildSkuData = ({
    additionalSku,
    defaultPrices,
    details,
    referenceIds,
    sku,
    status,
}: SkuDto): ChildSkuDto => ({
    ...(additionalSku && { additionalSku }),
    ...(defaultPrices && { defaultPrices }),
    ...(referenceIds && { referenceIds }),
    sku: sku ?? null,
    ...(status && { status }),
    details: {
        hideFromEcomMenu: Boolean(details?.hideFromEcomMenu),
        ...(details?.menuTitle && { menuTitle: details?.menuTitle }),
        ...(details?.description && { description: details?.description }),
        ...(details?.useCustomName !== undefined && { useCustomName: details.useCustomName }),
    },
});

export const transformSkuSectionData = (
    skus: SkuDto[],
    productCategoryName: string,
): {
    skusToCreate: SkuApiDto[];
    skusToUpdate: Partial<SkuApiDto>[];
} => {
    const skusToCreate: SkuApiDto[] = [];
    const skusToUpdate: Partial<SkuApiDto>[] = [];

    skus.forEach((s: SkuDto) => {
        // check if it is a base sku
        if (isBaseSku(s)) {
            // check if it already has an id
            const hasId: boolean = Boolean(s.id);
            if (hasId) {
                // find if an entry for this id is already present
                const index = skusToUpdate.findIndex((v: Partial<SkuApiDto>) => v.id === s.id);
                // update entry if it already exists, or add entry if not
                if (index !== -1) {
                    const currentValue: Partial<SkuApiDto> = skusToUpdate[index];
                    const updatedValue: Partial<SkuApiDto> = {
                        ...currentValue,

                        ...s,
                    };
                    skusToUpdate[index] = updatedValue;
                } else {
                    skusToUpdate.push({
                        ...s,
                    });
                }
            } else {
                // check if there are child skus as part of the new base sku that needs to be created
                const { children, ...rest } = s;
                const promo: SkuDto | undefined = children?.find((childSku: SkuDto) => isPromoSku(childSku));
                const sample: SkuDto | undefined = children?.find((childSku: SkuDto) => isSampleSku(childSku));

                skusToCreate.push({
                    ...rest,

                    ...(promo && { promo: getChildSkuData(promo) }),
                    ...(sample && { sample: getChildSkuData(sample) }),
                });
            }
        }

        // check if it is a promo sku
        if (isPromoSku(s)) {
            // check if it already has an id or it has the base sku id. In either of these scenarios the update api will be used
            const hasId: boolean = Boolean(s.id);
            const hasBaseSkuId: boolean = Boolean(s.parentId);
            if (hasId || hasBaseSkuId) {
                // find if an entry for base sku of this promo is already present
                const index = skusToUpdate.findIndex((v: Partial<SkuApiDto>) => v.id === s.parentId);
                // update entry if it already exists, or add entry if not
                if (index !== -1) {
                    const currentValue: Partial<SkuApiDto> = skusToUpdate[index];
                    const updatedValue: Partial<SkuApiDto> = {
                        ...currentValue,
                        promo: getChildSkuData(s),
                    };
                    skusToUpdate[index] = updatedValue;
                } else {
                    skusToUpdate.push({
                        id: s.parentId,
                        promo: getChildSkuData(s),
                    });
                }
            } else {
                // compare size fields to check if an entry already exists
                const index = skusToCreate.findIndex(
                    (v: SkuApiDto) =>
                        getVariantSizeLabel(v, productCategoryName).toLowerCase() ===
                        getVariantSizeLabel(s, productCategoryName).toLowerCase(),
                );
                if (index !== -1) {
                    const currentValue: SkuApiDto = skusToCreate[index];
                    const updatedValue: SkuApiDto = {
                        ...currentValue,
                        promo: getChildSkuData(s),
                    };
                    skusToCreate[index] = updatedValue;
                }
            }
        }

        // check if it is a sample sku
        if (isSampleSku(s)) {
            // check if it already has an id or it has the base sku id. In either of these scenarios the update api will be used
            const hasId: boolean = Boolean(s.id);
            const hasBaseSkuId: boolean = Boolean(s.parentId);
            if (hasId || hasBaseSkuId) {
                // find if an entry for base sku of this promo is already present
                const index = skusToUpdate.findIndex((v: Partial<SkuApiDto>) => v.id === s.parentId);
                // update entry if it already exists, or add entry if not
                if (index !== -1) {
                    const currentValue: Partial<SkuApiDto> = skusToUpdate[index];
                    const updatedValue: Partial<SkuApiDto> = {
                        ...currentValue,
                        sample: getChildSkuData(s),
                    };
                    skusToUpdate[index] = updatedValue;
                } else {
                    skusToUpdate.push({
                        id: s.parentId,
                        sample: getChildSkuData(s),
                    });
                }
            } else {
                // compare size fields to check if an entry already exists
                const index = skusToCreate.findIndex(
                    (v: SkuApiDto) =>
                        getVariantSizeLabel(v, productCategoryName).toLowerCase() ===
                        getVariantSizeLabel(s, productCategoryName).toLowerCase(),
                );
                if (index !== -1) {
                    const currentValue: SkuApiDto = skusToCreate[index];
                    const updatedValue: SkuApiDto = {
                        ...currentValue,
                        sample: getChildSkuData(s),
                    };
                    skusToCreate[index] = updatedValue;
                }
            }
        }
    });

    return { skusToCreate, skusToUpdate };
};

export const transformBasePricingSectionData = (
    product: ProductData,
    formData: SkuBasePriceData[] | undefined,
): Partial<SkuApiDto>[] => {
    const baseSkus: SkuDto[] = product?.variants?.filter((sku: SkuDto) => isBaseSku(sku)) || [];
    return baseSkus.reduce((acc: Partial<SkuApiDto>[], baseSku: SkuDto) => {
        const baseSkuPriceData: SkuBasePriceData | undefined = formData?.find(
            (s: SkuBasePriceData) => s.id === baseSku.id,
        );
        const promoSkuPriceData: SkuBasePriceData | undefined = formData?.find(
            (s: SkuBasePriceData) =>
                s.id === product?.variants?.find((sku: SkuDto) => sku.parentId === baseSku.id && isPromoSku(sku))?.id,
        );
        const sampleSkuPriceData: SkuBasePriceData | undefined = formData?.find(
            (s: SkuBasePriceData) =>
                s.id === product?.variants?.find((sku: SkuDto) => sku.parentId === baseSku.id && isSampleSku(sku))?.id,
        );

        let priceData: Partial<SkuApiDto> = {
            id: baseSku.id,
        };

        if (baseSkuPriceData?.defaultPrices?.base) {
            priceData = {
                ...priceData,
                defaultPrices: { base: dollarsToCents(baseSkuPriceData?.defaultPrices.base) }, // convert base price of sku which is entered in $ to cents
            };
        }
        if (promoSkuPriceData && promoSkuPriceData.defaultPrices?.base) {
            priceData = {
                ...priceData,
                promo: {
                    defaultPrices: { base: dollarsToCents(promoSkuPriceData.defaultPrices.base) }, // convert base price of sku which is entered in $ to cents
                },
            };
        }
        if (sampleSkuPriceData && sampleSkuPriceData.defaultPrices?.base) {
            priceData = {
                ...priceData,
                sample: {
                    defaultPrices: { base: dollarsToCents(sampleSkuPriceData.defaultPrices.base) }, // convert base price of sku which is entered in $ to cents
                },
            };
        }

        if (!isEmpty(priceData) && Object.keys(priceData).length > 1) {
            acc.push(priceData);
        }
        return acc;
    }, []);
};

export const transformPricingPageData = (
    variantId: string,
    baseSkuId: string,
    skuType: SkuType,
    newPrice: number,
): Partial<SkuApiDto>[] => {
    switch (skuType) {
        case SkuType.BASE: {
            return [
                {
                    id: variantId,
                    defaultPrices: {
                        base: newPrice,
                    },
                },
            ];
        }
        case SkuType.PROMO: {
            return [
                {
                    id: baseSkuId,
                    promo: {
                        defaultPrices: {
                            base: newPrice,
                        },
                    },
                },
            ];
        }
        case SkuType.SAMPLE: {
            return [
                {
                    id: baseSkuId,
                    sample: {
                        defaultPrices: {
                            base: newPrice,
                        },
                    },
                },
            ];
        }
        default:
            return [];
    }
};

export const transformProductControlData = (skus: SkuDto[], status: string): Partial<SkuApiDto>[] => {
    const skusToUpdate: Partial<SkuApiDto>[] = [];
    skus.forEach((s: SkuDto) => {
        // check if it is a base sku
        if (isBaseSku(s)) {
            // find if an entry for this id is already present
            const index = skusToUpdate.findIndex((v: Partial<SkuApiDto>) => v.id === s.id);
            if (index !== -1) {
                const currentValue: Partial<SkuApiDto> = skusToUpdate[index];
                const updatedValue: Partial<SkuApiDto> = {
                    ...currentValue,
                    status,
                };
                skusToUpdate[index] = updatedValue;
            } else {
                skusToUpdate.push({
                    id: s.id,
                    status,
                });
            }
        }

        // check if it is a promo sku
        if (isPromoSku(s)) {
            // find if an entry for base sku of this promo is already present
            const index = skusToUpdate.findIndex((v: Partial<SkuApiDto>) => v.id === s.parentId);
            if (index !== -1) {
                const currentValue: Partial<SkuApiDto> = skusToUpdate[index];
                const updatedValue: Partial<SkuApiDto> = {
                    ...currentValue,
                    promo: {
                        status,
                    },
                };
                skusToUpdate[index] = updatedValue;
            } else {
                skusToUpdate.push({
                    id: s.parentId,
                    promo: {
                        status,
                    },
                });
            }
        }

        // check if it is a sample sku
        if (isSampleSku(s)) {
            // find if an entry for base sku of this promo is already present
            const index = skusToUpdate.findIndex((v: Partial<SkuApiDto>) => v.id === s.parentId);
            if (index !== -1) {
                const currentValue: Partial<SkuApiDto> = skusToUpdate[index];
                const updatedValue: Partial<SkuApiDto> = {
                    ...currentValue,
                    sample: {
                        status,
                    },
                };
                skusToUpdate[index] = updatedValue;
            } else {
                skusToUpdate.push({
                    id: s.parentId,
                    sample: {
                        status,
                    },
                });
            }
        }
    });

    return skusToUpdate;
};
