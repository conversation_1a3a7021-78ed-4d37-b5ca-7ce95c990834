import { SkuDto } from '../interfaces/dto/sku';
import { isBaseSku, isPromoSku, isSampleSku, transformSkuSectionData } from './variantCardUtils';

jest.mock('./variantCardUtils', () => {
    const actualModule = jest.requireActual('./variantCardUtils');
    return {
        ...actualModule,
        isBaseSku: jest.fn(),
        isPromoSku: jest.fn(),
        isSampleSku: jest.fn(),
    };
});

describe('transformSkuSectionData', () => {
    let productCategoryName: string;

    beforeEach(() => {
        productCategoryName = 'Edible';
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    it('should return empty arrays when no SKUs are provided', () => {
        const result = transformSkuSectionData([], productCategoryName);
        expect(result).toEqual({ skusToCreate: [], skusToUpdate: [] });
    });

    it('should categorize base SKU as skusToCreate if it has no id', () => {
        const skus: SkuDto[] = [
            {
                name: 'Test Base SKU',
                amount: 1,
                uom: 'milligrams',
                merchandiseSize: null,
                unitCount: 1,
                productId: '123',
                status: 'active',
            },
        ];

        (isBaseSku as jest.Mock).mockReturnValue(true);
        (isPromoSku as jest.Mock).mockReturnValue(false);
        (isSampleSku as jest.Mock).mockReturnValue(false);

        const result = transformSkuSectionData(skus, productCategoryName);
        expect(result.skusToCreate).toHaveLength(1);
        expect(result.skusToUpdate).toHaveLength(0);
    });

    it('should categorize base SKU as skusToCreate if it has no id, and it contains sample and promo as children', () => {
        const skus: SkuDto[] = [
            {
                name: 'Test Base SKU',
                amount: 1,
                uom: 'milligrams',
                merchandiseSize: null,
                unitCount: 1,
                productId: '123',
                status: 'active',
                children: [
                    {
                        name: 'Test Promo SKU',
                        amount: 1,
                        uom: 'milligrams',
                        merchandiseSize: null,
                        unitCount: 1,
                        productId: '123',
                        status: 'active',
                        details: {
                            isPromo: true,
                            hideFromEcomMenu: true,
                            menuTitle: 'Promo Menu Title',
                        },
                    },
                    {
                        name: 'Test Sample SKU',
                        amount: 1,
                        uom: 'milligrams',
                        merchandiseSize: null,
                        unitCount: 1,
                        productId: '123',
                        status: 'active',
                        details: {
                            isSample: true,
                            hideFromEcomMenu: true,
                            menuTitle: 'Sample Menu Title',
                        },
                    },
                ],
            },
        ];

        (isBaseSku as jest.Mock).mockReturnValue(true);
        (isPromoSku as jest.Mock).mockReturnValue(false);
        (isSampleSku as jest.Mock).mockReturnValue(false);

        const result = transformSkuSectionData(skus, productCategoryName);
        expect(result.skusToCreate).toHaveLength(1);
        expect(result.skusToCreate[0].promo).toEqual({
            sku: null,
            status: 'active',
            details: { hideFromEcomMenu: true, menuTitle: 'Promo Menu Title' },
        });
        expect(result.skusToCreate[0].sample).toEqual({
            sku: null,
            status: 'active',
            details: { hideFromEcomMenu: true, menuTitle: 'Sample Menu Title' },
        });
        expect(result.skusToUpdate).toHaveLength(0);
    });

    it('should categorize base SKU as skusToUpdate if it has an id', () => {
        const skus: SkuDto[] = [
            {
                id: '123',
                name: 'Test Base SKU',
                amount: 1,
                uom: 'milligrams',
                merchandiseSize: null,
                unitCount: 1,
                productId: '123',
                status: 'active',
            },
        ];

        (isBaseSku as jest.Mock).mockReturnValue(true);
        (isPromoSku as jest.Mock).mockReturnValue(false);
        (isSampleSku as jest.Mock).mockReturnValue(false);

        const result = transformSkuSectionData(skus, productCategoryName);
        expect(result.skusToUpdate).toHaveLength(1);
        expect(result.skusToCreate).toHaveLength(0);
    });

    it('should update existing base SKU in skusToUpdate when duplicate id is found', () => {
        const skus: SkuDto[] = [
            { id: '123', name: 'Test Base SKU' } as SkuDto,
            { id: '123', name: 'Updated Test Base SKU' } as SkuDto,
        ];

        (isBaseSku as jest.Mock).mockReturnValue(true);
        (isPromoSku as jest.Mock).mockReturnValue(false);
        (isSampleSku as jest.Mock).mockReturnValue(false);

        const result = transformSkuSectionData(skus, productCategoryName);
        expect(result.skusToUpdate).toHaveLength(1);
        expect(result.skusToUpdate[0].name).toBe('Updated Test Base SKU');
    });

    it('should categorize promo SKU correctly into skusToUpdate if it has an id', () => {
        const childSkuDetails = {
            isPromo: true,
            hideFromEcomMenu: true,
            menuTitle: 'Promo Menu Title',
        };
        const skus: SkuDto[] = [
            {
                id: '123',
                name: 'Base SKU',
                amount: 1,
                uom: 'milligrams',
                merchandiseSize: null,
                unitCount: 1,
                productId: '999',
                status: 'active',
            },
            {
                id: '456',
                name: 'Promo SKU',
                parentId: '123',
                amount: 1,
                uom: 'milligrams',
                merchandiseSize: null,
                unitCount: 1,
                productId: '999',
                status: 'active',
                details: childSkuDetails,
            },
        ];

        (isBaseSku as jest.Mock).mockReturnValue(false);
        (isPromoSku as jest.Mock).mockReturnValue(true);
        (isSampleSku as jest.Mock).mockReturnValue(false);

        const result = transformSkuSectionData(skus, productCategoryName);
        expect(result.skusToUpdate).toHaveLength(1);
        expect(result.skusToUpdate[0].promo).toEqual({
            sku: null,
            status: 'active',
            details: { hideFromEcomMenu: true, menuTitle: 'Promo Menu Title' },
        });
    });

    it('should categorize sample SKU correctly into skusToUpdate if it has an id', () => {
        const childSkuDetails = {
            isSample: true,
            hideFromEcomMenu: true,
            menuTitle: 'Sample Menu Title',
        };
        const skus: SkuDto[] = [
            {
                id: '123',
                name: 'Base SKU',
                amount: 1,
                uom: 'milligrams',
                merchandiseSize: null,
                unitCount: 1,
                productId: '999',
                status: 'active',
            },
            {
                id: '456',
                name: 'Sample SKU',
                parentId: '123',
                amount: 1,
                uom: 'milligrams',
                merchandiseSize: null,
                unitCount: 1,
                productId: '999',
                status: 'active',
                details: childSkuDetails,
            },
        ];

        (isBaseSku as jest.Mock).mockReturnValue(false);
        (isPromoSku as jest.Mock).mockReturnValue(false);
        (isSampleSku as jest.Mock).mockReturnValue(true);

        const result = transformSkuSectionData(skus, productCategoryName);
        expect(result.skusToUpdate).toHaveLength(1);
        expect(result.skusToUpdate[0].sample).toEqual({
            sku: null,
            status: 'active',
            details: { hideFromEcomMenu: true, menuTitle: 'Sample Menu Title' },
        });
    });
});
