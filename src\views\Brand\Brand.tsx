import React from 'react';
import { Container, styled } from '@mui/material';
import { DataGridPro } from '@mui/x-data-grid-pro';
import useListBrandQuery from '../../queries/useListBrandQuery';
import useBrandDataTableColumns from './useBrandDataTableColumns';

const Table = styled(DataGridPro)`
    height: 100px;
    width: 100%;
`;

export default function Brand() {
    const defaultDataTableProps = {
        disableColumnMenu: true,
        disableSelectionOnClick: true,
        pagination: true,
    };
    const listBrandQuery = useListBrandQuery({});
    const data = listBrandQuery.data as any;
    const columns = useBrandDataTableColumns();
    return (
        <>
            <h1>Brand</h1>
            <Container>
                <Table columns={columns} rows={data?.data || []} {...defaultDataTableProps} autoHeight />
            </Container>
        </>
    );
}
