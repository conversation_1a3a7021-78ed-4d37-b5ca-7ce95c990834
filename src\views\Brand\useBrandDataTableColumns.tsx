import { GridColDef } from '@mui/x-data-grid-pro';
import { Button } from '@treez-inc/component-library';
import React from 'react';
import TableCellBox from '../../components/TableCellBox';

const useBrandDataTableColumns = () => {
    const columns: Array<GridColDef> = [
        {
            field: 'name',
            headerName: 'Name',
            minWidth: 150,
            maxWidth: 250,
        },
        {
            field: '',
            headerName: '',
            minWidth: 180,
            renderCell: () => (
                <TableCellBox>
                    <Button label="Edit" onClick={() => {}} />
                    <Button label="Delete" onClick={() => {}} />
                </TableCellBox>
            ),
        },
    ];
    return columns;
};

export default useBrandDataTableColumns;
