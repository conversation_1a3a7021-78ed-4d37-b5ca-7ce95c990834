import { Box, Typography, styled } from '@mui/material/';
import { ErrorTemplate } from '@treez-inc/component-library';
import React from 'react';
import { useRouteError } from 'react-router-dom';
import ErrorViewBody from './ErrorViewBody';

const Container = styled(Box)`
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    min-height: 100%;
`;

export default function ErrorView() {
    const debugEnabled = Boolean(process.env.DEBUG);
    const error: any = useRouteError();
    const onClick = () => {
        window.location.reload();
    };
    return (
        <Container>
            <ErrorTemplate
                title="Something went wrong..."
                body={<ErrorViewBody />}
                buttonProps={{ onClick, label: 'Refresh' }}
            />
            {debugEnabled &&
                error.stack.split('\n').map((item: any) => (
                    <Box key={`${item}-${Math.random().toFixed(4)}`}>
                        <Typography>{item}</Typography>
                    </Box>
                ))}
        </Container>
    );
}
