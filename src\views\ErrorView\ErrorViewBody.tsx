import React from 'react';
import { useRouteError } from 'react-router-dom';
import { Typography } from '@mui/material';

export default function ErrorViewBody() {
    const debugEnabled = Boolean(process.env.DEBUG);
    const error: any = useRouteError();
    return (
        <>
            {!debugEnabled && <Typography>We’re having trouble loading this page. Refresh to try again. </Typography>}
            {debugEnabled && <Typography>Error Message: {JSON.stringify(error.message)}</Typography>}
        </>
    );
}
