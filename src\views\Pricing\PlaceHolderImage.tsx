import React from 'react';
import { Box, styled } from '@mui/material/';
import { Icon, convertPxToRem } from '@treez-inc/component-library';

const ImageBox = styled(Box)(({ theme }) => ({
    alignItems: 'center',
    background: theme.palette.midribGreen.main,
    borderRadius: convertPxToRem(8),
    display: 'flex',
    flexBasis: convertPxToRem(80),
    flexGrow: 0,
    flexShrink: 0,
    height: convertPxToRem(80),
    justifyContent: 'center',
    width: convertPxToRem(80),
}));

const PlaceholderImage = () => (
    <ImageBox>
        <Icon color="treezGrey" fontSize="large" iconName="Product" />
    </ImageBox>
);

export default PlaceholderImage;
