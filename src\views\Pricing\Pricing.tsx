import React, { ChangeEvent, useEffect, useState } from 'react';
import { Box, styled } from '@mui/material';
import { MenuItemCheckboxProps, PageLoader, convertPxToRem } from '@treez-inc/component-library';
import { IconName } from '@treez-inc/component-library/dist/components/Icon/types';
import ProductsList from './ProductsList';
import Variants from './Variants';
import useProductSearchQuery from '../../queries/useProductSearchQuery';
import { PAGE_SIZE_FOR_PRICING_PAGE, productStatusList } from '../../utils/constants';
import useSnackbarContext from '../../hooks/snackbar/useSnackbarContext';
import { PricingContextProvider } from './PricingContext';
import { PricingPageProduct, ProductSearchResponse } from '../../interfaces/dto/product';
import { ProductCategoryDto } from '../../interfaces/dto/productCategory';
import findCategoryIcon from '../../utils/categoryUtils';
import { filterKeyFn } from '../../utils/datagridUtils';
import FilterComponent from '../../components/FilterComponent';
import { BrandDto } from '../../interfaces/dto/brand';
import brandApiKeyStore from '../../api/brandApiKeyStore';
import useLoadData from '../../hooks/useLoadData';
import productCategoryApiKeyStore from '../../api/productCategoryApiKeyStore';
import { IFilter } from '../../components/FilterComponent/types';
import { sortEntities } from '../../utils/common';
import { StyledLoadingContainer } from '../../styles/globalStyles';
import { EditProductContextProvider } from '../ProductFormV2/Context/EditProductContext';
import Mode from '../ProductFormV2/Types/Mode';
import { ProductSubCategoryDto } from '../../interfaces/dto/productSubCategory';
import { PRODUCT_CLASSIFICATION_OPTIONS } from '../ProductFormV2/ProductInfoSection/ProductInfoForm';
import SearchBarFilterV2 from '../../components/FilterComponent/SearchBarFilterV2';
import useQueryParams from '../../components/FilterComponent/hooks/useQueryParams';
import useFeatureFlag from '../../hooks/useFeatureFlag';
import FeatureFlag from '../../interfaces/featureFlag';
import PreviewOnlyBanner from '../../components/PreviewOnlyBanner';

const PricingBox = styled(Box)(({ theme }) => ({
    border: `${convertPxToRem(1)} solid ${theme.palette.grey04.main}`,
    display: 'flex',
    flexDirection: 'row',
    fontFamily: 'roboto',
    fontWeight: 400,
    height: '95%',
    marginTop: convertPxToRem(15),
    marginBottom: convertPxToRem(15),
}));

const ProductsListBox = styled(Box)(({ theme }) => ({
    borderRight: `${convertPxToRem(1)} solid ${theme.palette.grey04.main}`,
    display: 'flex',
    flexDirection: 'column',
    height: '100%',
    width: '30%',
}));

const VariantSectionBox = styled(Box)({
    height: '100%',
    width: '70%',
});

const ContainerBox = styled(Box)({
    paddingLeft: '3.25em',
    paddingRight: '2em',
});

const SearchWrapperBox = styled(Box)({
    display: 'flex',
    justifyContent: 'flex-end',
    padding: '1em 0 0 0',
    fontSize: 15,
});

const SearchBarFilterBox = styled(Box)({
    width: '18em',
});

const formatProducts = (
    products: ProductSearchResponse[],
    allCategoryOptions: ProductCategoryDto[],
): PricingPageProduct[] => {
    if (!products?.length || !allCategoryOptions?.length) {
        return [];
    }
    const mapProductInfo = (p: ProductSearchResponse): PricingPageProduct => {
        const productCategoryInfo = allCategoryOptions.find(
            (productCategory) => productCategory.id === p.productCategoryId,
        );
        const productCategoryIcon = findCategoryIcon(productCategoryInfo?.name || '') as IconName;
        return { ...p, productCategoryIcon };
    };
    return products.map(mapProductInfo);
};

const Pricing = () => {
    const { searchParams, updateSearchParams } = useQueryParams();
    const [searchTerm, setSearchTerm] = useState<string>(searchParams.get('search') ?? '');
    const searchValue = [searchParams.get('search') || ''];

    const [paginationOptions, setPaginationOptions] = useState<{
        currentPage: number;
        pageCount: number;
    }>({
        currentPage: 0,
        pageCount: 1,
    });
    const [filterState, setFilterState] = useState<IFilter>({
        brand: [],
        category: [],
        status: [],
        subCategory: [],
    });

    const { setSnackbar } = useSnackbarContext();
    const {
        isLoading: isBrandLoading,
        isError: isBrandError,
        data: brands,
    } = useLoadData<BrandDto[]>({
        queryConfig: brandApiKeyStore.getBrands(),
    });
    const {
        isLoading: isCategoryLoading,
        isError: isCategoryError,
        data: categories,
    } = useLoadData<ProductCategoryDto[]>({
        queryConfig: productCategoryApiKeyStore.getAllCategories(),
    });

    const {
        isLoading: isSubCategoryLoading,
        isError: isSubCategoryError,
        data: subCategories,
    } = useLoadData<ProductSubCategoryDto[]>({
        queryConfig: productCategoryApiKeyStore.getAllSubCategories(),
    });

    const productSearchQuery = useProductSearchQuery(
        {
            search: searchTerm || undefined,
            paging: { pageNumber: paginationOptions.currentPage, size: PAGE_SIZE_FOR_PRICING_PAGE },
            sort: { key: 'lastUpdated', isAscending: false },
            filters: {
                ...filterState,
            },
        },
        {
            keepPreviousData: true,
            refetchOnWindowFocus: false,
        },
    );
    const {
        data = { data: [], totalRecords: 0 },
        isError: isSearchError,
        isFetching: isSearchFetching,
        isLoading: isSearchLoading,
        isSuccess: isSearchSuccess,
    } = productSearchQuery;

    const formattedProducts: PricingPageProduct[] = formatProducts(data.data || [], categories || []);
    const categoryValues: MenuItemCheckboxProps[] = filterKeyFn(categories || []);
    const brandValues: MenuItemCheckboxProps[] = filterKeyFn(brands || []);
    const subcategories: MenuItemCheckboxProps[] = filterKeyFn(subCategories || []);
    const classifications: MenuItemCheckboxProps[] = filterKeyFn(PRODUCT_CLASSIFICATION_OPTIONS || []);

    const isCatalogPreviewOnly = useFeatureFlag(FeatureFlag.CATALOG_PREVIEW_ONLY_BANNER);

    const onSearchTermChange = (search: string) => {
        setSearchTerm(search);
    };
    const onFilterModelChange = (newState: IFilter) => {
        setFilterState(newState);
    };
    const onPageChange = (__event: ChangeEvent<unknown>, page: number) => {
        setPaginationOptions((currentOptions) => ({
            ...currentOptions,
            currentPage: page - 1,
        }));
    };

    useEffect(() => {
        if (isSearchError || isCategoryError || isBrandError || isSubCategoryError) {
            setSnackbar({
                message: 'There was an error retrieving data.',
                severity: 'error',
                iconName: 'Error',
            });
        }
    }, [isSearchError, isCategoryError, isBrandError, isSubCategoryError]);

    useEffect(() => {
        if (isSearchSuccess && data.totalRecords) {
            // set pagination options
            setPaginationOptions((currentOptions) => ({
                ...currentOptions,
                pageCount: Math.ceil(data.totalRecords / PAGE_SIZE_FOR_PRICING_PAGE),
            }));
        }
    }, [data.totalRecords, isSearchSuccess, PAGE_SIZE_FOR_PRICING_PAGE]);

    return (
        <>
            {isCatalogPreviewOnly &&
                <PreviewOnlyBanner />
            }
            {(isSearchLoading || isCategoryLoading || isBrandLoading || isSubCategoryLoading) && (
                <StyledLoadingContainer>
                    <PageLoader />
                </StyledLoadingContainer>
            )}
            {!isSearchLoading && !isCategoryLoading && !isBrandLoading && !isSubCategoryLoading && (
                <>
                    <PricingContextProvider>
                        <ContainerBox>
                            <FilterComponent
                                brandData={brandValues}
                                categoryData={sortEntities(categoryValues)}
                                statusData={productStatusList}
                                subCategories={subcategories}
                                classifications={classifications}
                                onFilterChange={onFilterModelChange}
                                onSearchTermChange={onSearchTermChange}
                            />
                            <SearchWrapperBox>
                                <SearchBarFilterBox>
                                    <SearchBarFilterV2 search={searchValue} updateSearchParams={updateSearchParams} />
                                </SearchBarFilterBox>
                            </SearchWrapperBox>
                        </ContainerBox>
                        <PricingBox>
                            <ProductsListBox data-testid="pricing-list-container">
                                <ProductsList
                                    products={formattedProducts}
                                    productsCount={data.totalRecords}
                                    paginationOptions={paginationOptions}
                                    onPageChange={onPageChange}
                                    isProductsLoading={isSearchFetching}
                                />
                            </ProductsListBox>
                            <EditProductContextProvider mode={Mode.EDIT}>
                                <VariantSectionBox>
                                    <Variants productsCount={data.totalRecords} />
                                </VariantSectionBox>
                            </EditProductContextProvider>
                        </PricingBox>
                    </PricingContextProvider>
                </>
            )}
        </>
    );
};

export default Pricing;
