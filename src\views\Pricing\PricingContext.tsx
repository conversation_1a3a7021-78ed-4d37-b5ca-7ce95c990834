import React, { ReactNode, createContext, useMemo } from 'react';
import { PricingStateProps, pricingInitialState, usePricingReducer } from './hooks/usePricingReducer';
import { PricingPageProduct } from '../../interfaces/dto/product';
import { SkuDto } from '../../interfaces/dto/sku';

interface PricingContextProps {
    pricingState: PricingStateProps;
    setSelectedProduct: (product: PricingPageProduct) => void;
    setSelectedVariant: (Variant: SkuDto) => void;
}

interface PricingContextProviderProps {
    children: ReactNode;
}

export const PricingContext = createContext<PricingContextProps>({
    pricingState: pricingInitialState,
    setSelectedProduct: () => {},
    setSelectedVariant: () => {},
});

export const PricingContextProvider = ({ children }: PricingContextProviderProps) => {
    const { state, setSelectedProduct, setSelectedVariant } = usePricingReducer();

    const value = useMemo(
        () => ({
            pricingState: state,
            setSelectedProduct,
            setSelectedVariant,
        }),
        [state],
    );

    return <PricingContext.Provider value={value}>{children}</PricingContext.Provider>;
};
