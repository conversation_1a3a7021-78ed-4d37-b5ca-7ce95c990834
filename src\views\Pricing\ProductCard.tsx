import React from 'react';
import { Box, styled, Typography } from '@mui/material';
import { Icon, convertPxToRem } from '@treez-inc/component-library';
import PlaceholderImage from './PlaceHolderImage';
import { PricingPageProduct } from '../../interfaces/dto/product';

const ProductCardBox = styled(Box, {
    shouldForwardProp: (props) => props !== 'isSelected' && props !== 'isLastItem',
})<{ isLastItem: boolean; isSelected: boolean }>(({ theme, isLastItem, isSelected }) => ({
    backgroundColor: isSelected ? theme.palette.green02.main : theme.palette.primaryWhite.main,
    borderBottom: !isLastItem ? `${convertPxToRem(1)} solid ${theme.palette.grey04.main}` : 'none',
    columnGap: 16,
    display: 'flex',
    flexDirection: 'row',
    paddingLeft: convertPxToRem(16),
    paddingTop: convertPxToRem(10),
    paddingBottom: convertPxToRem(10),
    paddingRight: convertPxToRem(16),
    '&:hover': {
        cursor: 'pointer',
        backgroundColor: theme.palette.green02.main,
    },
}));

// const ProductImageBox = styled(Box)({
//     borderRadius: convertPxToRem(8),
//     height: convertPxToRem(80),
//     width: convertPxToRem(80),
// });

const ProductDetailsWrapper = styled(Box)({
    display: 'flex',
    flexDirection: 'column',
    rowGap: convertPxToRem(8),
});

const ProductDetailsBox = styled(Box)(({ theme }) => ({
    color: theme.palette.secondaryText.main,
    columnGap: 8,
    display: 'flex',
    flexDirection: 'row',
    fontSize: convertPxToRem(14),
    justifyContent: 'space-between',
}));

const ProductDetailsBoxItem = styled(Box)({
    columnGap: 4,
    display: 'flex',
    flexDirection: 'row',
});

const ProductNameBox = styled(Box)({
    fontSize: convertPxToRem(15),
});

interface ProductCardProps {
    product: PricingPageProduct;
    isLastItem: boolean;
    isSelected: boolean;
    onProductSelected: () => void;
}

const ProductCard = ({ product, isLastItem, isSelected, onProductSelected }: ProductCardProps) => {
    const productDetails = [product.brandName, '•', product.productCategoryName, '•', product.productSubCategoryName];

    return (
        <ProductCardBox isLastItem={isLastItem} isSelected={isSelected} onClick={onProductSelected}>
            <PlaceholderImage />
            <ProductDetailsWrapper>
                <ProductNameBox>{product.productName}</ProductNameBox>
                <ProductDetailsBox>
                    {productDetails.map((detail, index) => (
                        <ProductDetailsBoxItem key={`${detail}-${Math.random()}`}>
                            {index === 2 && (
                                <Box>
                                    <Icon
                                        color="treezGrey"
                                        fontSize="medium"
                                        iconName={product?.productCategoryIcon || 'Release'}
                                    />
                                </Box>
                            )}
                            <Typography data-testid="detail">{detail}</Typography>
                        </ProductDetailsBoxItem>
                    ))}
                </ProductDetailsBox>
            </ProductDetailsWrapper>
        </ProductCardBox>
    );
};

export default ProductCard;
