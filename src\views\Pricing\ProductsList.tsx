import React, { ChangeEvent, useEffect } from 'react';
import { Box, Pagination, styled } from '@mui/material/';
import { convertPxToRem } from '@treez-inc/component-library';
import ProductCard from './ProductCard';
import { PricingPageProduct } from '../../interfaces/dto/product';
import usePricingContext from './hooks/usePricingContext';
import TreezLinearProgress from '../../components/TreezLinearProgress';

const ProductCountBox = styled(Box)({
    fontSize: convertPxToRem(14),
    fontWeight: 500,
    paddingLeft: convertPxToRem(20),
    paddingTop: convertPxToRem(20),
    paddingBottom: convertPxToRem(20),
});

const PaginationBox = styled(Box)({
    alignSelf: 'center',
    paddingTop: convertPxToRem(10),
    paddingBottom: convertPxToRem(10),
});

const ScrollableProducts = styled(Box)(({ theme }) => ({
    borderTop: `${convertPxToRem(1)} solid ${theme.palette.grey04.main}`,
    borderBottom: `${convertPxToRem(1)} solid ${theme.palette.grey04.main}`,
    overflowY: 'scroll',
}));

const StyledPagination = styled(Pagination)(({ theme }) => ({
    '& .Mui-selected': {
        background: `${theme.palette.green03.main} !important`,
    },
}));

interface ProductsListProps {
    isProductsLoading: boolean;
    products: PricingPageProduct[];
    productsCount: number;
    paginationOptions: {
        currentPage: number;
        pageCount: number;
    };
    onPageChange: (event: ChangeEvent<unknown>, page: number) => void;
}

const ProductsList = ({
    isProductsLoading,
    products,
    productsCount,
    paginationOptions,
    onPageChange,
}: ProductsListProps) => {
    const { pricingState, setSelectedProduct } = usePricingContext();

    useEffect(() => {
        // set first product in a page as selected by default
        if (!isProductsLoading && productsCount) {
            setSelectedProduct(products[0]);
        }
    }, [isProductsLoading]);

    return (
        <>
            {isProductsLoading && <TreezLinearProgress />}
            <ProductCountBox data-testid="product-count">{productsCount} results</ProductCountBox>
            <ScrollableProducts>
                {products?.map((product, index) => (
                    <ProductCard
                        key={product.productId}
                        product={product}
                        isLastItem={index === products.length - 1}
                        isSelected={product.productId === pricingState.selectedProduct.productId}
                        onProductSelected={() => {
                            setSelectedProduct(product);
                        }}
                    />
                ))}
            </ScrollableProducts>
            {productsCount > 0 && (
                <PaginationBox>
                    <StyledPagination count={paginationOptions.pageCount} onChange={onPageChange} />
                </PaginationBox>
            )}
        </>
    );
};

export default ProductsList;
