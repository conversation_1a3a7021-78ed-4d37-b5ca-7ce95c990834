import React, { useLayoutEffect, useRef } from 'react';
import { GridRenderEditCellParams, useGridApiContext } from '@mui/x-data-grid-pro';
import { Input, styled } from '@mui/material/';
import { Icon, convertPxToRem } from '@treez-inc/component-library';

const VariantGridInput = styled(Input)(({ theme }) => ({
    background: theme.palette.grey03.main,
    borderRadius: convertPxToRem(8),
    color: theme.palette.secondaryText.main,
    fontWeight: 400,
    fontSize: convertPxToRem(14),
    height: convertPxToRem(32),
    width: convertPxToRem(100),
}));

const VariantPriceEditField = (props: GridRenderEditCellParams) => {
    const { id, value: valueProp, field, row, hasFocus } = props;
    const apiRef = useGridApiContext();
    const ref = useRef<HTMLInputElement>(null);

    useLayoutEffect(() => {
        if (hasFocus && ref.current !== null) {
            ref.current.focus();
        }
    }, [hasFocus]);

    return (
        <VariantGridInput
            ref={ref}
            defaultValue={valueProp}
            disableUnderline
            startAdornment={<Icon fontSize="small" color="grey07" iconName="Dollar" />}
            type="text"
            onBlur={(event: any) => {
                const newValue = event.target.value;
                const oldValue = row[field];
                if (newValue !== oldValue && !Number.isNaN(newValue) && newValue > 0) {
                    apiRef.current.setEditCellValue({ id, field, value: newValue });
                }
            }}
            onKeyDown={(event: any) => {
                if (event.key === 'Enter') {
                    const newValue = event.target.value;
                    const oldValue = row[field];
                    if (newValue !== oldValue && !Number.isNaN(newValue) && newValue > 0) {
                        apiRef.current.setEditCellValue({ id, field, value: newValue });
                    }
                }
            }}
        />
    );
};

export default VariantPriceEditField;
