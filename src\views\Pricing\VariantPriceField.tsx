import React, { useState } from 'react';
import { Box, Typography, styled } from '@mui/material/';
import { IconButton } from '@treez-inc/component-library';

interface VariantPriceFieldProps {
    value?: string | null;
    onClick?: any;
}

const Container: any = styled(Box)({
    alignItems: 'center',
    columnGap: '1em',
    display: 'flex',
    flexDirection: 'row',
    flexGrow: 1,
    justifyContent: 'flex-start',
    height: '100%',
    width: '100%',
});

const PriceBox = styled(Box)({
    alignSelf: 'stretch',
    display: 'flex',
    flexDirection: 'column',
    paddingTop: '1em',
});

const Value = styled(Typography)({
    color: '#0f1709',
    fontSize: '0.875em',
    fontStyle: 'normal',
    lineHeight: '20px',
    paddingTop: '0.5em',
});

export default function VariantPriceField({ value, onClick }: VariantPriceFieldProps) {
    const [displayEdit, setDisplayEdit] = useState(false);
    return (
        <Container onMouseEnter={() => setDisplayEdit(true)} onMouseLeave={() => setDisplayEdit(false)}>
            <PriceBox>
                <Value>{value ? `$ ${value}` : '-'}</Value>
            </PriceBox>
            {displayEdit && <IconButton size="small" iconName="Edit" onClick={onClick} />}
        </Container>
    );
}
