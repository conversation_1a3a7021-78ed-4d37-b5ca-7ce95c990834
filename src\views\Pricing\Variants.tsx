import React from 'react';
import { styled, Box } from '@mui/material';
import { convertPxToRem } from '@treez-inc/component-library';
import usePricingContext from './hooks/usePricingContext';
import { PricingPageProduct, ProductDto } from '../../interfaces/dto/product';
import useProduct from '../ProductFormV2/Hooks/useProduct';
import { ProductCategoryDto } from '../../interfaces/dto/productCategory';
import productApiKeyStore from '../../api/productApiKeyStore';
import useLoadData from '../../hooks/useLoadData';
import { SkuDto } from '../../interfaces/dto/sku';
import { EntityPriceDto } from '../../interfaces/dto/entityPrice';
import entityPriceApiKeyStore from '../../api/entityPriceApiKeyStore';
import productCategoryApiKeyStore from '../../api/productCategoryApiKeyStore';
import PricingForm from '../ProductFormV2/DefaultPricingSection/PricingForm';
import { filterForBaseSkus } from '../../utils/productUtils';

const VariantsBox = styled(Box)({
    display: 'flex',
    flexDirection: 'column',
    height: '100%',
    paddingTop: convertPxToRem(20),
    paddingLeft: convertPxToRem(30),
    paddingRight: convertPxToRem(30),
});

const ProductNameBox = styled(Box)({
    fontSize: convertPxToRem(20),
    paddingBottom: convertPxToRem(20),
});

const NoResultsBox = styled(Box)(({ theme }) => ({
    alignItems: 'center',
    color: theme.palette.secondaryText.main,
    display: 'flex',
    justifyContent: 'center',
}));

interface VariantsProps {
    productsCount: number;
}

const Variants = ({ productsCount }: VariantsProps) => {
    const { pricingState } = usePricingContext();
    const { selectedProduct } = pricingState;
    const { variants } = selectedProduct as PricingPageProduct;

    const { productId } = selectedProduct;

    const { setProductUpdate, setEntityPriceUpdate } = useProduct();
    const [, setSortedCategories] = React.useState<ProductCategoryDto[]>([]);
    const [usableSkus, setUsableSkus] = React.useState<SkuDto[]>([]);

    const {
        isLoading,
        isError,
        data: productResponseData,
    } = useLoadData<ProductDto>({
        queryConfig: productApiKeyStore.getProductDetails(productId),
    });

    const skuIds = usableSkus?.map((sku) => sku.id) as string[];

    const {
        isLoading: isEntityLoading,
        data: entityPrices = [],
        isSuccess,
    } = useLoadData<EntityPriceDto[]>({
        queryConfig: entityPriceApiKeyStore.getEntityPrices(skuIds),
    });

    const {
        isError: isCategoryError,
        data: categories,
        isLoading: isLoadingCategories,
    } = useLoadData<ProductCategoryDto[]>({
        queryConfig: productCategoryApiKeyStore.getAllCategories(),
    });

    const isFullyLoaded = !isLoading && !isEntityLoading && !isLoadingCategories;

    React.useEffect(() => {
        if (categories) {
            // sort categories alphabetically
            setSortedCategories(
                categories.sort((a: ProductCategoryDto, b: ProductCategoryDto) => {
                    if (a.name.toLowerCase() < b.name.toLowerCase()) return -1;
                    if (a.name.toLowerCase() > b.name.toLowerCase()) return 1;
                    return 0;
                }),
            );
        }
        if (productResponseData && categories) {
            const productData = {
                ...productResponseData,
                productCategory: categories.find(
                    (c) => c.id === productResponseData.productSubCategory?.productCategoryId,
                ),
                productImages: productResponseData.images?.filter(
                    (i) => i.productId !== undefined && i.variantId === undefined,
                ),
                variants: productResponseData.variants?.map((v) => ({
                    ...v,
                    variantImages: productResponseData.images?.filter((i) => i.variantId === v.id),
                })),
            };

            setUsableSkus(filterForBaseSkus(productResponseData?.variants!));

            setProductUpdate(productData);
        }
    }, [productResponseData, categories]);

    React.useEffect(() => {
        if (productResponseData && isSuccess) {
            setEntityPriceUpdate(entityPrices);
        }
    }, [productResponseData, entityPrices, isSuccess]);

    return (
        <VariantsBox>
            {productsCount === 0 && (
                <NoResultsBox data-testid="no-results-container">No products to display.</NoResultsBox>
            )}
            {productsCount > 0 && !variants && (
                <NoResultsBox>Selected product does not have any variants.</NoResultsBox>
            )}
            {productsCount > 0 && selectedProduct && variants && variants.length > 0 && (
                <>
                    <ProductNameBox>{selectedProduct.productName}</ProductNameBox>
                    {(isError || isCategoryError) && <>Error loading productzz</>}
                    {isFullyLoaded && (
                        <PricingForm enableNavigation={false} usableSkusParent={usableSkus} hasSaveCloseButtons />
                    )}
                </>
            )}
        </VariantsBox>
    );
};

export default Variants;
