import { useReducer } from 'react';
import { PricingPageProduct } from '../../../interfaces/dto/product';
import { SkuDto } from '../../../interfaces/dto/sku';

export enum PricingReducerActionType {
    SET_SELECTED_PRODUCT,
    SET_SELECTED_VARIANT,
}

export interface PricingStateProps {
    selectedProduct: any;
    selectedVariant: any;
}

export interface PricingReducerAction {
    type: PricingReducerActionType;
    payload: any;
}

export const pricingInitialState: PricingStateProps = {
    selectedProduct: {},
    selectedVariant: {},
};

const pricingReducer = (state: PricingStateProps, action: PricingReducerAction) => {
    switch (action.type) {
        case PricingReducerActionType.SET_SELECTED_PRODUCT:
            return { ...state, selectedProduct: action.payload };
        case PricingReducerActionType.SET_SELECTED_VARIANT:
            return { ...state, selectedVariant: action.payload };
        default:
            return state;
    }
};

export const usePricingReducer = () => {
    const [state, dispatch] = useReducer(pricingReducer, pricingInitialState);

    const setSelectedProduct = (product: PricingPageProduct) => {
        dispatch({ type: PricingReducerActionType.SET_SELECTED_PRODUCT, payload: product });
    };

    const setSelectedVariant = (variant: SkuDto) => {
        dispatch({ type: PricingReducerActionType.SET_SELECTED_VARIANT, payload: variant });
    };

    return {
        state,
        setSelectedProduct,
        setSelectedVariant,
    };
};
