import { GridRowSelectionModel } from '@mui/x-data-grid-pro';
import { ProductSearchResponse } from '../../../interfaces/dto/product';
import { Status } from '../../../utils/constants';

interface FilteredProducts {
    totalSelectedActiveProducts: ProductSearchResponse[];
    totalSelectedInActiveProducts: ProductSearchResponse[];
    totalSelectedMergeProducts: ProductSearchResponse[];
}

interface GetBulkActionBarProps {
    totalSelectedActiveProducts: number;
    totalSelectedInActiveProducts: number;
    totalSelectedMergeProducts: number;
    onBulkAction: (actionType: Status) => void;
    onAddCollectionAction: () => void;
    addToCollectionPermission: boolean;
}

interface ButtonProps {
    label: string;
    onClick: () => void;
    iconName: string;
}

const getBulkActionBarProps = ({
    totalSelectedInActiveProducts,
    totalSelectedActiveProducts,
    // totalSelectedMergeProducts,
    onBulkAction,
    onAddCollectionAction,
    addToCollectionPermission,
}: GetBulkActionBarProps): {
    buttonProps: (false | ButtonProps)[];
    variant: string;
} => ({
    buttonProps: [
        addToCollectionPermission &&
            (totalSelectedActiveProducts || totalSelectedInActiveProducts) > 0 && {
                label: 'Add to product collection',
                onClick: () => onAddCollectionAction(),
                iconName: 'FolderCreate',
            },
        totalSelectedInActiveProducts > 1 && {
            label: 'Activate',
            onClick: () => onBulkAction(Status.ACTIVE),
            iconName: 'Checkmark',
        },
        totalSelectedActiveProducts > 1 && {
            label: 'Deactivate',
            onClick: () => onBulkAction(Status.INACTIVE),
            iconName: 'Disturb',
        },
        // Temporarily removing until merge feature is more robust
        // see: https://gitlab.com/treez-inc/engineering/back-of-house/product-control-mfe/-/issues/893
        // totalSelectedMergeProducts > 1 && {
        //     label: 'Merge',
        //     onClick: () => onBulkAction(Status.MERGE),
        //     iconName: 'Merge',
        // },
    ].filter(Boolean),
    variant: 'primary',
});

const groupedDataByStatus = (products: ProductSearchResponse[], selectionModel: GridRowSelectionModel) =>
    products.reduce(
        (result: FilteredProducts, product: ProductSearchResponse) => {
            const { status, ...rest } = product;
            const lowercaseStatus = status.toLowerCase();

            const condition = selectionModel.includes(product.id);
            if (lowercaseStatus === Status.ACTIVE && condition) {
                result.totalSelectedActiveProducts.push({ status: lowercaseStatus, ...rest });
                if (!product.isChild) {
                    result.totalSelectedMergeProducts.push({ status: lowercaseStatus, ...rest });
                }
            } else if (lowercaseStatus === 'deactivated' && condition) {
                result.totalSelectedInActiveProducts.push({ status: lowercaseStatus, ...rest });
            }
            return result;
        },
        { totalSelectedActiveProducts: [], totalSelectedInActiveProducts: [], totalSelectedMergeProducts: [] },
    );

const getRelevantSelectedProductsByStatus = (status: string, products: FilteredProducts): ProductSearchResponse[] => {
    if (status === Status.MERGE) {
        return products.totalSelectedMergeProducts;
    }
    return [...products.totalSelectedActiveProducts, ...products.totalSelectedInActiveProducts];
};

export { getRelevantSelectedProductsByStatus, groupedDataByStatus, getBulkActionBarProps };
