import React, { useEffect, useState } from 'react';
import { GridRowSelectionModel } from '@mui/x-data-grid-pro';
import { Modal, convertPxToRem } from '@treez-inc/component-library';
import { Box } from '@mui/material';
import { ProductSearchResponse } from '../../../interfaces/dto/product';
import ProductCollectionsDropdown, { NEW_COLLECTION_PREFIX } from '../components/ProductCollectionsDropdown';
import productCollectionApiKeyStore from '../../../api/product-collection-api/productCollectionApiKeyStore';
import useUpdateProductCollectionData from '../../../hooks/product-collection/useUpdateCollectionData';
import useCreateProductCollection from '../../../hooks/product-collection/useCreateCollection';

import {
    GetProductCollectionsResponse,
    ProductCollection,
    ProductCollectionItems,
    ProductCollectionMenuItems,
} from '../../../interfaces/dto/productCollection';
import filterMutationErrors from '../../../utils/MutationResponseUtil';
import useSnackbarContext from '../../../hooks/snackbar/useSnackbarContext';
import useLoadCollectionData from '../../../hooks/product-collection/useLoadCollectionData';

interface AddCollectionModalContentProps {
    productCollections: ProductCollectionMenuItems[];
    setProductCollection: React.Dispatch<React.SetStateAction<ProductCollectionMenuItems | undefined>>;
}

const AddCollectionModalContent: React.FC<AddCollectionModalContentProps> = ({
    productCollections,
    setProductCollection,
}) => (
    <>
        <Box sx={{ marginLeft: convertPxToRem(8) }}>
            <ProductCollectionsDropdown
                productCollections={productCollections}
                setProductCollection={setProductCollection}
            />
        </Box>
    </>
);

interface AddCollectionModalProps {
    open: boolean;
    closeAddCollection: () => void;
    selectedProducts: ProductSearchResponse[];
    setSelectionModel: React.Dispatch<React.SetStateAction<GridRowSelectionModel | []>>;
}

const AddProductCollectionModal: React.FC<AddCollectionModalProps> = ({
    open,
    closeAddCollection,
    selectedProducts,
    setSelectionModel,
}) => {
    const { data: productCollections, refetch: refetchProductCollections } =
        useLoadCollectionData<GetProductCollectionsResponse>({
            queryConfig: productCollectionApiKeyStore.getProductCollections(),
        });

    const activeProductCollections = (productCollections?.data ?? []).filter(
        (collection: ProductCollection) => !collection.deletedAt,
    );

    const productCollectionsOptions: ProductCollectionMenuItems[] = (activeProductCollections ?? [])
        .filter((collection) => collection.sync === false)
        .sort((a, b) => (a.name || '').localeCompare(b.name || ''))
        .map((collection: ProductCollection) => ({
            displayName: collection.name || '',
            displayValue: collection.id || '',
        }));

    const [selectedProductCollection, setProductCollection] = useState<ProductCollectionMenuItems | undefined>();
    const [variantIds, setVariantIds] = useState<{ variantId: string | undefined }[]>([]);
    const [confirmationInProgress, setConfirmationInProgress] = useState(false);
    const { setSnackbar } = useSnackbarContext();
    const { data: productCollectionDetails, isLoading } = useLoadCollectionData<any>({
        queryConfig: productCollectionApiKeyStore.getProductCollectionDetails(selectedProductCollection?.displayValue),
    });

    const { mutateAsync } = useUpdateProductCollectionData({
        config: productCollectionApiKeyStore.updateProductCollectionDetails(selectedProductCollection?.displayValue),
    });

    const createMethods = useCreateProductCollection({
        config: productCollectionApiKeyStore.createProductCollection(),
    });

    const variantExists = (variantId: string): boolean => variantIds.some((variant) => variant.variantId === variantId);

    useEffect(() => {
        if (open) {
            refetchProductCollections();
            setProductCollection(undefined);
            setConfirmationInProgress(false);
            const allSelectedVariants: { variantId: string }[] = [];
            allSelectedVariants.push(
                ...selectedProducts.filter((product) => product.isChild).map((variant) => ({ variantId: variant.id })),
            );
            setVariantIds(allSelectedVariants);
        }
    }, [open]);

    const handleConfirmClick = async () => {
        setConfirmationInProgress(true);

        let results = null;
        if (!productCollectionDetails) {
            if (selectedProductCollection?.displayValue.indexOf(NEW_COLLECTION_PREFIX) !== -1) {
                const collectionToCreate = selectedProductCollection?.displayValue.split(':')[1];

                results = await createMethods.mutateAsync({
                    name: collectionToCreate,
                    collectionItems: variantIds,
                });
            } else {
                setSnackbar({
                    message: 'There was an error while getting product collection details.',
                    severity: 'error',
                    iconName: 'Error',
                });
                setConfirmationInProgress(false);
                return;
            }
        } else {
            const existingCollectionItems = productCollectionDetails?.collectionItems;
            const existingVariants: ProductCollectionItems[] = [];

            existingCollectionItems.forEach((collectionItem: any) => {
                if (!variantExists(collectionItem.variantId)) {
                    existingVariants.push({ variantId: collectionItem.variantId });
                }
            });

            const collectionVariants: ProductCollectionItems[] = existingVariants.concat(variantIds);
            const productCollection: ProductCollection = {
                name: selectedProductCollection?.displayName,
                collectionItems: collectionVariants,
            };

            if (collectionVariants.length === 0) {
                setSnackbar({
                    message: 'Empty selected products!',
                    severity: 'error',
                    iconName: 'Error',
                });
                setConfirmationInProgress(false);
                return;
            }

            results = await mutateAsync(productCollection);
        }

        const errors = filterMutationErrors(results);

        if (!errors) {
            if (results) {
                setSnackbar({
                    message: `${variantIds.length} Products successfully added to the ${selectedProductCollection?.displayName} collection`,
                    severity: 'info',
                    iconName: 'Success',
                });
                setSelectionModel([]);
            }
            closeAddCollection();
        } else {
            setSnackbar({
                message: 'There was an error while saving product collection.',
                severity: 'error',
                iconName: 'Error',
            });
            closeAddCollection();
        }
    };

    return (
        <Modal
            content={
                <AddCollectionModalContent
                    productCollections={productCollectionsOptions}
                    setProductCollection={setProductCollection}
                />
            }
            onClose={closeAddCollection}
            open={open}
            primaryButton={{
                label: 'Add to collection',
                onClick: handleConfirmClick,
                disabled: confirmationInProgress || !selectedProductCollection || isLoading,
            }}
            secondaryButton={{
                label: 'Cancel',
                onClick: closeAddCollection,
                disabled: confirmationInProgress,
            }}
            testId="add-collection-confirmation-modal"
            title="Add to collection"
        />
    );
};

export default AddProductCollectionModal;
