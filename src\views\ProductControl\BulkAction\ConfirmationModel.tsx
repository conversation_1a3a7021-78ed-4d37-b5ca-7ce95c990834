import React from 'react';
import styled from '@emotion/styled';
import { Modal, convertPxToRem, Icon } from '@treez-inc/component-library';
import { Typography, Box, ListItem, List } from '@mui/material';
import { ProductSearchResponse } from '../../../interfaces/dto/product';
import { getVariantSizeLabel } from '../../../utils/variantCardUtils';
import { Status } from '../../../utils/constants';

export const StyledListItem = styled(ListItem)(() => ({
    padding: 0,
    margin: 0,
    color: '#0F1709',
    fontWeight: convertPxToRem(400),
    fontSize: convertPxToRem(15),
    lineHeight: convertPxToRem(24),
}));

interface ConfirmationModelContentProps {
    productNames: string[] | undefined;
    bulkActionType: string;
    variantsIncluded: boolean;
    productIncluded: boolean;
}

const ConfirmationModalContent: React.FC<ConfirmationModelContentProps> = ({
    productNames,
    bulkActionType,
    variantsIncluded,
    productIncluded,
}) => (
    <>
        <Typography variant="body2">
            Are you sure you want to {bulkActionType === Status.ACTIVE ? 'activate' : 'deactivate'} these{' '}
            {productIncluded ? 'products' : ''} {productIncluded && variantsIncluded ? 'and' : ''}{' '}
            {variantsIncluded ? 'variants' : ''}?{' '}
        </Typography>
        <Box sx={{ marginLeft: convertPxToRem(8) }}>
            <List>
                {productNames?.map((productName: string) => (
                    <Box key={productName} sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
                        <span style={{ marginRight: '0.2rem' }}>
                            <Icon iconName="Dot" fontSize="extraSmall" />
                        </span>
                        <StyledListItem>{productName}</StyledListItem>
                    </Box>
                ))}
            </List>
        </Box>
        <Typography variant="body2" sx={{ marginTop: '1rem' }}>
            {bulkActionType === Status.ACTIVE &&
                `
        Active products can be associated with inventory and invoiced in Purchasing. 
        You can deactivate it later, if needed.`}
        </Typography>
    </>
);

interface ConfirmationModalProps {
    open: boolean;
    closeConfirmation: () => void;
    bulkActionType: string;
    onConfirm: (products: ProductSearchResponse[]) => void;
    selectedProducts: any[];
}

const ConfirmationModal: React.FC<ConfirmationModalProps> = ({
    open,
    bulkActionType,
    closeConfirmation,
    onConfirm,
    selectedProducts,
}) => {
    let variantIncluded = false;
    let productIncluded = false;
    const productNames: string[] | undefined = bulkActionType
        ? selectedProducts?.map((p: ProductSearchResponse) => {
              let childName = p.productName;
              if (p.isChild) {
                  variantIncluded = true;
                  const sizeLabel = getVariantSizeLabel(p.variants[0], p.productCategoryName);
                  childName = `${childName} ${sizeLabel}`;
              } else {
                  productIncluded = true;
              }
              return childName;
          })
        : [];

    const handleConfirmClick = () => {
        onConfirm(selectedProducts);
    };

    return (
        <Modal
            content={
                <ConfirmationModalContent
                    bulkActionType={bulkActionType}
                    productNames={productNames}
                    variantsIncluded={variantIncluded}
                    productIncluded={productIncluded}
                />
            }
            onClose={closeConfirmation}
            open={open}
            primaryButton={{
                label: 'Confirm',
                onClick: handleConfirmClick,
            }}
            secondaryButton={{
                label: 'Cancel',
                onClick: closeConfirmation,
            }}
            testId="confirmation-modal"
            title={`Confirm  ${bulkActionType === Status.ACTIVE ? 'activation' : 'deactivation'}`}
        />
    );
};

export default ConfirmationModal;
