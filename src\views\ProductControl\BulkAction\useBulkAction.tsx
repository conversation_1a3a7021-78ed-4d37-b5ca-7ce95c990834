import React, { useState } from 'react';
import { GridRowSelectionModel } from '@mui/x-data-grid-pro';
import { ListItem } from '@mui/material';
import { convertPxToRem } from '@treez-inc/component-library';
import styled from '@emotion/styled';
import { ProductDto, ProductSearchResponse } from '../../../interfaces/dto/product';
import useProductControl from '../hooks/useProductControl';
import { SkuApiDto, SkuDto } from '../../../interfaces/dto/sku';
import { transformProductControlData } from '../../../utils/variantCardUtils';

export const StyledListItem = styled(ListItem)(() => ({
    padding: 0,
    margin: 0,
    color: '#0F1709',
    fontWeight: convertPxToRem(400),
    fontSize: convertPxToRem(15),
    lineHeight: convertPxToRem(24),
}));

type SetSelectionModel = React.Dispatch<React.SetStateAction<GridRowSelectionModel>>;

const useBulkAction = () => {
    const [isLoading, setLoading] = useState<boolean>(false);

    const { updateStatuses, updateVariantStatuses } = useProductControl();

    const updateBulkStatus = async (
        selectedProducts: ProductSearchResponse[],
        status: string,
        onRefetchAPI: () => void,
        setSelectionModel: SetSelectionModel,
    ) => {
        const filteredProducts = selectedProducts.filter((product) => !product.isChild);
        const products = filteredProducts?.map((product) => ({ id: product.productId, status })) as ProductDto[];
        let isUpdateProductSuccess = true;
        let isUpdateVariantSuccess = true;

        setLoading(true);
        if (products && products.length > 0 && status) {
            isUpdateProductSuccess = false;
            isUpdateProductSuccess = await updateStatuses(products, status);
        }

        const skusInSearch: ProductSearchResponse[] = selectedProducts.filter((product) => product.isChild);
        const skus: SkuDto[] = skusInSearch.map((v: ProductSearchResponse) => v.variants[0]);
        const skusToUpdate: Partial<SkuApiDto>[] = transformProductControlData(skus, status);

        if (skusToUpdate && skusToUpdate.length > 0 && status) {
            isUpdateVariantSuccess = false;
            isUpdateVariantSuccess = await updateVariantStatuses(skusToUpdate, status);
        }
        if (isUpdateVariantSuccess && isUpdateProductSuccess) {
            setSelectionModel([]);
            onRefetchAPI();
        }
        setLoading(false);
    };

    return {
        updateBulkStatus,
        isSaving: isLoading,
    };
};

export default useBulkAction;
