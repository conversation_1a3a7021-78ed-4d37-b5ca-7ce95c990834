import React, { useState, useEffect, Dispatch, SetStateAction } from 'react';
import { convertPxToRem, Icon, IconButton, MenuItemCheckboxProps, PageLoader, Tooltip } from '@treez-inc/component-library';
import { Alert, AlertTitle, Box, ButtonProps, styled } from '@mui/material';
import {
    DataGridProProps,
    GridFilterModel,
    GridPaginationModel,
    GridRowId,
    GridRowSelectionModel,
    GridSortModel,
    GridTreeNodeWithRender,
    useGridApiContext,
} from '@mui/x-data-grid-pro';
import { nanoid } from 'nanoid';
import useProductControlReducer from './ProductControlReducer';
import useProductSearchQuery, { ProductSearchResult } from '../../queries/useProductSearchQuery';
import useProductControlColumns from './useProductControlColumns';
import useDebounce from './hooks/useDebounce';
import ProductDetailsDrawer from './ProductDetailsDrawer/DetailsDrawer';
import Loader from '../../components/Loader';
import useSnackbarContext from '../../hooks/snackbar/useSnackbarContext';
import { productStatusList, Status, statusMap } from '../../utils/constants';
import { ProductSearchResponse } from '../../interfaces/dto/product';
import useBulkAction from './BulkAction/useBulkAction';
import {
    getBulkActionBarProps,
    getRelevantSelectedProductsByStatus,
    groupedDataByStatus,
} from './BulkAction/ActionBarUtility';
import ConfirmationModal from './BulkAction/ConfirmationModel';
import AddProductCollectionModal from './BulkAction/AddProductCollectionModel';
import ProductCollectionPermissions from '../../permissions/productCollectionPermissions';
import { useUserPermissionsContext } from '../../permissions/userPermissions.context';
import MergeProductDetailsDrawer from './ProductDetailsDrawer/MergeDetailsDrawer';
import { DataGridPro } from '../../components/DataGridPro';
import { filterKeyFn, formatVariantSku, mapData } from '../../utils/datagridUtils';
import FilterComponent from '../../components/FilterComponent';
import { sortEntities } from '../../utils/common';
import useLoadData from '../../hooks/useLoadData';
import { BrandDto } from '../../interfaces/dto/brand';
import brandApiKeyStore from '../../api/brandApiKeyStore';
import { ProductCategoryDto } from '../../interfaces/dto/productCategory';
import productCategoryApiKeyStore from '../../api/productCategoryApiKeyStore';
import AddProductButton from '../../components/search/AddProductBtn';
import { StyledLoadingContainer } from '../../styles/globalStyles';
import isGroupNode from '../../utils/gridNodeUtils';
import { SkuResponseDto } from '../../interfaces/dto/sku';
import { ProductSubCategoryDto } from '../../interfaces/dto/productSubCategory';
import { PRODUCT_CLASSIFICATION_OPTIONS } from '../ProductFormV2/ProductInfoSection/ProductInfoForm';
import { IFilter } from '../../components/FilterComponent/types';
import FeatureFlag from '../../interfaces/featureFlag';
import useFeatureFlag from '../../hooks/useFeatureFlag';

const GROUP = 'group';

const Header = styled(Box)(() => ({
    display: 'flex',
    justifyContent: 'end',
    backgroundColor: '#F5F5F5',
    padding: '1em 2em 1em 1em',
}));

const Container = styled(Box)(() => ({
    display: 'flex',
    flexDirection: 'column',
    paddingLeft: convertPxToRem(52),
    paddingRight: convertPxToRem(32),
}));

const DataGridBox = styled(Box)(() => ({
    display: 'flex',
    flexDirection: 'column',
    height: 'calc(100vh - 250px)',
    overflow: 'hidden',
}));

const DataGridProStyle = styled(DataGridPro)(() => ({
    width: '100%',
    height: '100%',
    '& .MuiDataGrid-virtualScroller': {
        overflow: 'auto',
    },
    '& .MuiDataGrid-main': {
        overflow: 'hidden',
    },
}));

const StyledErrorContainer = styled(Box)(() => ({
    display: 'flex',
    height: '100%',
    justifyContent: 'center',
    width: '100%',
    alignItems: 'center',
}));

const CustomGroupGridTreeContainer = styled(Box)(() => ({
    display: 'flex',
}));

const PreviewOnlyInfoIcon = styled(Box)(() => ({
    display: "flex",
    marginLeft: convertPxToRem(8),
}));

const PreviewOnlyBanner = styled(Box)(({ theme }) => ({
    backgroundColor: '#F9C548',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
    ...theme.typography.body2,
}));

const NoProductsOverlay = () => <StyledErrorContainer>No products to display</StyledErrorContainer>;

function CustomGridTreeDataGroupingCellActionMenu(props: {
    id: GridRowId;
    field: string;
    row: ProductSearchResponse;
    rowNode: GridTreeNodeWithRender;
    isExpandAll: boolean;
    setIsExpandAll: Dispatch<SetStateAction<boolean>>;
}) {
    const { id, field, rowNode, row, isExpandAll, setIsExpandAll } = props;

    const apiRef = useGridApiContext();
    const [isExpanded, setExpanded] = useState(false);

    const handleClick: ButtonProps['onClick'] = (event) => {
        if (!isGroupNode(rowNode)) return;

        const newExpandedState = !isExpanded;
        setExpanded(newExpandedState);
        apiRef.current.setRowChildrenExpansion(id, newExpandedState);
        event.stopPropagation();
    };

    // Expand Individual Product Variant
    useEffect(() => {
        if (rowNode.type !== GROUP) {
            return;
        }

        if (isExpanded !== rowNode.childrenExpanded) {
            apiRef.current.setRowChildrenExpansion(id, !rowNode.childrenExpanded);
            apiRef.current.setCellFocus(id, field);
        }
    }, [rowNode]);

    // Expand All Product Variants
    useEffect(() => {
        if (isGroupNode(rowNode)) {
            setExpanded(isExpandAll);
            apiRef.current.setRowChildrenExpansion(id, isExpandAll);
        }
    }, [isExpandAll]);

    // Check if all individual Rows are Expanded or Collapsed Manually
    useEffect(() => {
        const checkIfAllRowsAreExpandedOrClosed = () => {
            const allRowIds = apiRef.current.getAllRowIds();

            // Check if all rows are collapsed
            const allClosed = allRowIds.every((rowId: GridRowId) => {
                const node = apiRef.current.getRowNode(rowId);
                if (node && node.type === GROUP) {
                    return !node.childrenExpanded;
                }
                return true;
            });

            // Check if all rows are expanded
            const allOpened = allRowIds.every((rowId: GridRowId) => {
                const node = apiRef.current.getRowNode(rowId);
                if (node && node.type === GROUP) {
                    return node.childrenExpanded;
                }
                return true;
            });

            if (allClosed) {
                setIsExpandAll(false);
            }

            if (allOpened) {
                setIsExpandAll(true);
            }
        };

        checkIfAllRowsAreExpandedOrClosed();
    }, [rowNode, isExpandAll]);

    return (
        <CustomGroupGridTreeContainer key={`grid-tree-container-${id}`}>
            {!row.isChild && isGroupNode(rowNode) && (
                <IconButton
                    iconName={isExpanded ? 'ChevronUp' : 'ChevronRight'}
                    onClick={handleClick}
                    size="small"
                    testId="expanddiscountrow-button"
                    variant="secondary"
                />
            )}
        </CustomGroupGridTreeContainer>
    );
}

export default function ProductControl() {
    const [responseStatus, setResponseStatus] = useState<number | null>(null);
    const [bulkActionType, setBulkActionType] = useState<string>('');
    const [selectionModel, setSelectionModel] = useState<GridRowSelectionModel>([]);
    const [isActionModalOpen, toggleActionModalOpen] = useState<boolean>(false);
    const [isExpandAll, setIsExpandAll] = useState<boolean>(false);
    const [isAddCollectionActionModalOpen, toggleAddCollectionActionModalOpen] = useState<boolean>(false);
    const { setSnackbar } = useSnackbarContext();
    const { validateUserPermissions } = useUserPermissionsContext();
    const addToCollectionPermission = validateUserPermissions([
        ProductCollectionPermissions.MANAGE_PRODUCT_COLLECTIONS,
    ]);
    const isCatalogPreviewOnly = useFeatureFlag(FeatureFlag.CATALOG_PREVIEW_ONLY_BANNER);

    let modifiedData: ProductSearchResponse[] = [];

    const {
        isGlobalLoading,
        closeDrawer,
        closeMergeDrawer,
        isDrawerOpen,
        isMergeDrawerOpen,
        drawerProduct,
        openMergeDrawer,
        setSortModel,
        setFilterModel,
        setProductFilterModel,
        sortModel,
        filterModel,
        searchProps,
        setPaginationModel,
    } = useProductControlReducer();

    const { updateBulkStatus, isSaving } = useBulkAction();
    const [searchTerm, setSearchTerm] = useState<string>('');
    const [filter, setFilter] = useState<any>(null);
    const debouncedFilter = useDebounce(filter, 650);
    searchProps.search = searchTerm;

    // Effect to apply stored search term to the filter model on component mount
    useEffect(() => {
        if (searchTerm) {
            setFilterModel({ ...filterModel, quickFilterValues: [searchTerm] });
        }
    }, []);

    // Effect to update stored search term when the debounced search term changes
    const onSearchTermChange = (search: string) => {
        setSearchTerm(search);
    };

    const productSearchQuery = useProductSearchQuery(
        {
            ...searchProps,
            search: searchTerm || undefined,
        },
        {
            initialData: [],
            keepPreviousData: true,
            refetchOnWindowFocus: false,
            placeholderData: [],
        },
    );

    const {
        data: productData,
        isError: isSearchError,
        isFetching: isSearchFetching,
        isLoading: isSearchLoading,
        isSuccess: isSearchSuccess,
        error,
    } = productSearchQuery;

    const onRefetchAPI = () => {
        productSearchQuery.refetch();
    };

    // bulk action bar action
    const showConfirmation = () => toggleActionModalOpen(true);
    const closeConfirmation = () => toggleActionModalOpen(false);
    const onConfirm = (products: ProductSearchResponse[]) => {
        if (bulkActionType === Status.ACTIVE) {
            updateBulkStatus(products, Status.ACTIVE, onRefetchAPI, setSelectionModel);
        } else if (bulkActionType === Status.INACTIVE) {
            updateBulkStatus(products, Status.INACTIVE, onRefetchAPI, setSelectionModel);
        }
        closeConfirmation();
    };
    const onBulkAction = (actionType: Status) => {
        if (actionType === Status.MERGE) {
            openMergeDrawer();
        } else {
            showConfirmation();
        }
        setBulkActionType(actionType);
    };

    const showAddCollectionConfirmation = () => toggleAddCollectionActionModalOpen(true);
    const closeAddCollectionConfirmation = () => toggleAddCollectionActionModalOpen(false);

    const onAddCollectionAction = () => {
        showAddCollectionConfirmation();
        setBulkActionType(Status.INACTIVE);
    };

    useEffect(() => {
        if (isSearchError) {
            setSnackbar({
                message: 'There was an error retrieving products.',
                severity: 'error',
                iconName: 'Error',
            });
        }
        setResponseStatus((error as any)?.response?.status);
    }, [productSearchQuery]);

    const { data: brandData, isLoading: isBrandLoading } = useLoadData<BrandDto[]>({
        queryConfig: brandApiKeyStore.getBrands(),
    });
    const { isLoading: isCategoryLoading, data: categoryData } = useLoadData<ProductCategoryDto[]>({
        queryConfig: productCategoryApiKeyStore.getAllCategories(),
    });

    const { isLoading: isSubCategoryLoading, data: subCategories } = useLoadData<ProductSubCategoryDto[]>({
        queryConfig: productCategoryApiKeyStore.getAllSubCategories(),
    });

    const columns = useProductControlColumns();

    if (productData?.data && Array.isArray(productData?.data)) {
        modifiedData = productData?.data?.map((product: ProductSearchResponse) => ({
            ...product,
            formattedVariantSizes: product?.variants?.map((variant: SkuResponseDto) => variant.label),
            formattedVariantSkus: product.variants ? formatVariantSku(product?.variants) : [],
            status: mapData(product.status, statusMap),
        }));
    }

    const productInfoDataRows: ProductSearchResponse[] = [];
    if (modifiedData && modifiedData.length > 0) {
        modifiedData?.forEach((productDataItem: ProductSearchResponse) => {
            productInfoDataRows.push({
                ...productDataItem,
                hierarchy: [`disc-${productDataItem.productId}`],
                id: productDataItem.productId,
                isChild: false,
            });
            productDataItem?.variants?.forEach((variant: SkuResponseDto) => {
                if (variant) {
                    const key = `store-ux-${nanoid()}`;
                    productInfoDataRows.push({
                        ...productDataItem,
                        formattedVariantSizes: [variant.label],
                        formattedVariantSkus: formatVariantSku([variant]),
                        status: mapData(variant.status, statusMap),
                        variants: [variant],
                        id: variant.id as string,
                        lastUpdated: variant.updatedAt || '',
                        hierarchy: [`disc-${productDataItem.productId}`, key],
                        isChild: true,
                    });
                }
            });
        });
    }

    const categoryIds = filter?.category?.map((item: string) => item) || [];
    const filteredSubcategories = subCategories?.filter((sub) => categoryIds.includes(sub.productCategoryId));

    const categoryValues: MenuItemCheckboxProps[] = filterKeyFn(categoryData || []);
    const brandValues: MenuItemCheckboxProps[] = filterKeyFn(brandData || []);
    const subcategories: MenuItemCheckboxProps[] = filterKeyFn(
        categoryIds.length === 0 ? subCategories || [] : filteredSubcategories || [],
    );
    const classifications: MenuItemCheckboxProps[] = filterKeyFn(PRODUCT_CLASSIFICATION_OPTIONS || []);

    const onFilter = (newFilter: IFilter) => {
        setFilter(newFilter);
    };

    useEffect(() => {
        if (debouncedFilter) {
            setProductFilterModel(debouncedFilter);
        }
    }, [debouncedFilter]);

    if (!isSearchFetching && (responseStatus === 401 || responseStatus === 403)) {
        return (
            <Alert severity="warning" variant="filled">
                <AlertTitle>Access Denied</AlertTitle>You do not have permission to view this page.
            </Alert>
        );
    }

    const getTreeDataPath: DataGridProProps['getTreeDataPath'] = (row) => row.hierarchy;

    const getRowClassName: DataGridProProps['getRowClassName'] = (params) =>
        params.row.isChild ? 'DataGrid-Row-Child' : '';

    const groupingColDef: DataGridProProps['groupingColDef'] = {
        headerName: 'Select',
        maxWidth: 50,
        renderHeader: () => {
            const expandAllRows = () => {
                setIsExpandAll((prev) => !prev);
            };

            return (
                <IconButton
                    iconName={isExpandAll ? 'ChevronUp' : 'ChevronRight'}
                    onClick={expandAllRows}
                    size="small"
                    testId="expanddiscountrow-button"
                    variant="secondary"
                />
            );
        },
        renderCell: (params) => {
            if (params.row?.variants && Array.isArray(params.row.variants)) {
                return (
                    <CustomGridTreeDataGroupingCellActionMenu
                        id={params.id}
                        rowNode={params.rowNode}
                        field={params.field}
                        row={params.row}
                        isExpandAll={isExpandAll}
                        setIsExpandAll={setIsExpandAll}
                    />
                );
            }
            return null;
        },
        align: 'left',
    };

    const handlePageChange = () => {
        setIsExpandAll(false);
    };

    const { totalSelectedActiveProducts, totalSelectedInActiveProducts, totalSelectedMergeProducts } =
        groupedDataByStatus(productInfoDataRows, selectionModel);

    const bulkActionBarProps = getBulkActionBarProps({
        totalSelectedActiveProducts: totalSelectedActiveProducts?.length,
        totalSelectedInActiveProducts: totalSelectedInActiveProducts?.length,
        totalSelectedMergeProducts: totalSelectedMergeProducts?.length,
        onBulkAction,
        onAddCollectionAction,
        addToCollectionPermission,
    });

    const selectedProducts = getRelevantSelectedProductsByStatus(bulkActionType, {
        totalSelectedActiveProducts,
        totalSelectedInActiveProducts,
        totalSelectedMergeProducts,
    });

    const selectedItemCount = selectionModel.length;

    const isLoadingData = isGlobalLoading || isSaving;

    const setSelectedProducts = (
        existingSelectedRowId: GridRowSelectionModel,
        selectedRowId: GridRowSelectionModel,
    ) => {
        existingSelectedRowId.forEach((rowId: GridRowId) => {
            const itemNotFound = selectedRowId.indexOf(rowId) < 0;
            if (itemNotFound) {
                const allProductInfoRows = productInfoDataRows.filter(
                    (productInfo: ProductSearchResponse) =>
                        productInfo.productId === rowId.toString() && productInfo.isChild,
                );
                const allSelectedProductIds: string[] = allProductInfoRows.flatMap((product) => product.id);
                allSelectedProductIds.forEach((id: string) => {
                    if (selectedRowId.indexOf(id) >= 0) {
                        selectedRowId.splice(selectedRowId.indexOf(id), 1);
                    }
                });
                setSelectionModel(selectedRowId);
            }
        });
    };
    const onCheckboxChecked = (selectedRowId: GridRowSelectionModel) => {
        const allSelectedProduct = productInfoDataRows.filter(
            (productInfo: ProductSearchResponse) => selectedRowId.indexOf(productInfo.productId) >= 0,
        );
        if (allSelectedProduct && allSelectedProduct.length > 0) {
            const selectedProductId = allSelectedProduct.flatMap((p) => p.id);
            const existingRowIds = selectedRowId.map((id: GridRowId) => id);
            selectedProductId.forEach((id) => {
                if (existingRowIds.indexOf(id) < 0) {
                    existingRowIds.push(id);
                }
            });
            setSelectionModel(existingRowIds);
        } else {
            setSelectionModel(selectedRowId);
        }
        setSelectedProducts(selectionModel, selectedRowId);
    };

    return (
        <>
            {isCatalogPreviewOnly && (
                <PreviewOnlyBanner data-testid="preview-only-banner">
                    You are in preview only mode
                    <PreviewOnlyInfoIcon>
                        <Tooltip
                            placement="right"
                            themeColor="dark"
                            title={`You're viewing the new product catalog, which is not yet active for your store(s). Your current products in Treez remain unchanged. This page is for preview only-no changes will apply yet.`}
                            variant="multiRow"
                        >
                            <Icon iconName="InfoOutlined" />
                        </Tooltip>
                    </PreviewOnlyInfoIcon>
                </PreviewOnlyBanner>
            )}
            <Header>
                <AddProductButton isDisabled={isSearchFetching || isSearchError} />
            </Header>

            {(isSearchLoading || isCategoryLoading || isBrandLoading || isSubCategoryLoading) && (
                <StyledLoadingContainer>
                    <PageLoader />
                </StyledLoadingContainer>
            )}
            {!isSearchLoading && !isCategoryLoading && !isBrandLoading && !isSubCategoryLoading && (
                <Container>
                    {responseStatus !== 401 && responseStatus !== 403 && isSearchSuccess && (
                        <Box
                            sx={{
                                height: modifiedData?.length > 0 ? '100%' : convertPxToRem(530),
                                width: '100%',
                                paddingBottom: convertPxToRem(24),
                            }}
                        >
                            <Box sx={{ position: 'relative' }}>
                                <FilterComponent
                                    brandData={brandValues}
                                    categoryData={sortEntities(categoryValues)}
                                    statusData={productStatusList}
                                    subCategories={subcategories}
                                    classifications={classifications}
                                    onFilterChange={onFilter}
                                    onSearchTermChange={onSearchTermChange}
                                />
                            </Box>

                            <DataGridBox>
                                <DataGridProStyle
                                    autoHeight={false}
                                    loading={isSearchLoading || isSearchFetching}
                                    rowCount={(productData as ProductSearchResult).totalRecords || 0}
                                    rows={productInfoDataRows}
                                    rowsLoadingMode="server"
                                    columns={columns}
                                    filterMode="server"
                                    filterModel={filterModel}
                                    onFilterModelChange={(model: GridFilterModel) => {
                                        setFilterModel(model);
                                    }}
                                    sortModel={sortModel}
                                    sortingMode="server"
                                    onSortModelChange={(model: GridSortModel) => {
                                        setSortModel(model);
                                    }}
                                    pageSizeOptions={[10, 20, 50, 100]}
                                    pagination
                                    onPaginationModelChange={(model: GridPaginationModel) => {
                                        setPaginationModel(model);
                                        handlePageChange();
                                    }}
                                    paginationMode="server"
                                    treeData
                                    getTreeDataPath={getTreeDataPath}
                                    getRowClassName={getRowClassName}
                                    groupingColDef={groupingColDef}
                                    slots={{
                                        noRowsOverlay: NoProductsOverlay,
                                        noResultsOverlay: NoProductsOverlay,
                                    }}
                                    disableColumnMenu
                                    checkboxSelection
                                    rowSelectionModel={selectionModel}
                                    onRowSelectionModelChange={onCheckboxChecked}
                                    slotProps={{
                                        toolbar: {
                                            ...(bulkActionBarProps.buttonProps.length && { bulkActionBarProps }),
                                            productData,
                                            selectedItemCount,
                                        },
                                    }}
                                />
                            </DataGridBox>
                            <ConfirmationModal
                                open={isActionModalOpen}
                                bulkActionType={bulkActionType}
                                closeConfirmation={closeConfirmation}
                                onConfirm={onConfirm}
                                selectedProducts={selectedProducts}
                            />
                            <ProductDetailsDrawer
                                isOpen={isDrawerOpen}
                                closeDrawer={() => closeDrawer()}
                                currentProductDetails={drawerProduct}
                            />
                            {addToCollectionPermission && (
                                <AddProductCollectionModal
                                    open={isAddCollectionActionModalOpen}
                                    closeAddCollection={closeAddCollectionConfirmation}
                                    selectedProducts={selectedProducts}
                                    setSelectionModel={setSelectionModel}
                                />
                            )}
                            <MergeProductDetailsDrawer
                                isOpen={isMergeDrawerOpen}
                                closeDrawer={() => closeMergeDrawer()}
                                selectedProducts={selectedProducts}
                                onRefetchAPI={onRefetchAPI}
                            />
                        </Box>
                    )}
                    {isLoadingData && <Loader loading={isLoadingData} />}
                </Container>
            )}
        </>
    );
}
