import { useReducer } from 'react';
import { NullableType } from 'joi';
import { GridFilterModel, GridSortModel } from '@mui/x-data-grid-pro';
import { ProductDto } from '../../interfaces/dto/product';
import { ProductSearchQueryProps } from '../../queries/useProductSearchQuery';

enum ProductControlReducerActionType {
    CLOSE_DRAWER,
    OPEN_DRAWER,
    CLOSE_MERGE_DRAWER,
    OPEN_MERGE_DRAWER,
    RESET_FILTER_MODEL,
    RESET_DRAWER_PRODUCT,
    RESET_SORT_MODEL,
    UPDATE_FILTER_MODEL,
    UPDATE_DRAWER_PRODUCT,
    UPDATE_SORT_MODEL,
    UPDATE_PAGINATION_MODEL,
    RESET_PAGINATION_MODEL,
    UPDATE_GLOBAL_LOADER,
    UPDATE_PRODUCT_FILTER_MODEL,
    RESET_SUBCATEGORY_MODEL,
}

export interface ProductControlDefaultStateProps {
    isGlobalLoading: boolean;
    isDrawerOpen: boolean;
    isMergeDrawerOpen: boolean;
    drawerProduct: NullableType<ProductDto>;
    filterModel: GridFilterModel;
    sortModel: GridSortModel;
    productFilterModel: {
        category: string[];
        subCategory: string[];
        status: string[];
        brand: string[];
    };
    paginationModel: {
        page: number;
        pageSize: number;
    };
}

const ProductControlDefaultState: ProductControlDefaultStateProps = {
    isGlobalLoading: false,
    isDrawerOpen: false,
    isMergeDrawerOpen: false,
    drawerProduct: null,
    filterModel: { items: [] },
    sortModel: [],
    productFilterModel: {
        category: [],
        subCategory: [],
        status: ['active'],
        brand: [],
    },
    paginationModel: { page: 0, pageSize: 20 },
};

interface ProductControlReducerAction {
    type: ProductControlReducerActionType;
    payload: any;
}

const ProductControlReducer = (state: ProductControlDefaultStateProps, action: ProductControlReducerAction) => {
    switch (action.type) {
        case ProductControlReducerActionType.UPDATE_GLOBAL_LOADER:
            return { ...state, isGlobalLoading: !state.isGlobalLoading };
        case ProductControlReducerActionType.CLOSE_DRAWER:
            return { ...state, isDrawerOpen: false };
        case ProductControlReducerActionType.OPEN_DRAWER:
            return { ...state, isDrawerOpen: true };
        case ProductControlReducerActionType.UPDATE_DRAWER_PRODUCT:
            return { ...state, drawerProduct: action.payload };
        case ProductControlReducerActionType.RESET_DRAWER_PRODUCT:
            return { ...state, drawerProduct: null };
        case ProductControlReducerActionType.RESET_FILTER_MODEL:
            return { ...state, filterModel: { items: [] } };
        case ProductControlReducerActionType.RESET_SORT_MODEL:
            return { ...state, sortModel: [] };
        case ProductControlReducerActionType.UPDATE_FILTER_MODEL:
            return { ...state, filterModel: action.payload };
        case ProductControlReducerActionType.UPDATE_SORT_MODEL:
            return { ...state, sortModel: action.payload };
        case ProductControlReducerActionType.RESET_PAGINATION_MODEL:
            return { ...state, paginationModel: { page: 0, pageSize: 20 } };
        case ProductControlReducerActionType.UPDATE_PAGINATION_MODEL:
            return { ...state, paginationModel: action.payload };
        case ProductControlReducerActionType.RESET_SUBCATEGORY_MODEL:
            return {
                ...state,
                subCategoryModel: {
                    subCategory: [],
                },
            };
        case ProductControlReducerActionType.UPDATE_PRODUCT_FILTER_MODEL:
            return { ...state, productFilterModel: action.payload };
        case ProductControlReducerActionType.CLOSE_MERGE_DRAWER:
            return { ...state, isMergeDrawerOpen: false };
        case ProductControlReducerActionType.OPEN_MERGE_DRAWER:
            return { ...state, isMergeDrawerOpen: true };
        default:
            return state;
    }
};

export default function useProductControlReducer() {
    const [
        {
            isGlobalLoading,
            isDrawerOpen,
            isMergeDrawerOpen,
            drawerProduct,
            filterModel,
            sortModel,
            productFilterModel,
            paginationModel,
        },
        dispatch,
    ] = useReducer(ProductControlReducer, {
        ...ProductControlDefaultState,
    });

    const manageGlobalLoader = () =>
        dispatch({
            type: ProductControlReducerActionType.UPDATE_GLOBAL_LOADER,
            payload: null,
        });

    const openDrawer = () => dispatch({ type: ProductControlReducerActionType.OPEN_DRAWER, payload: null });
    const closeDrawer = () => dispatch({ type: ProductControlReducerActionType.CLOSE_DRAWER, payload: null });

    const openMergeDrawer = () => dispatch({ type: ProductControlReducerActionType.OPEN_MERGE_DRAWER, payload: null });
    const closeMergeDrawer = () =>
        dispatch({ type: ProductControlReducerActionType.CLOSE_MERGE_DRAWER, payload: null });

    const setDrawerProduct = (payload: any) =>
        dispatch({
            type: ProductControlReducerActionType.UPDATE_DRAWER_PRODUCT,
            payload,
        });

    const clearDrawerProduct = () =>
        dispatch({
            type: ProductControlReducerActionType.RESET_DRAWER_PRODUCT,
            payload: null,
        });

    const setSortModel = (payload: any) => {
        dispatch({ type: ProductControlReducerActionType.UPDATE_SORT_MODEL, payload });
    };

    const setFilterModel = (payload: any) => {
        dispatch({ type: ProductControlReducerActionType.UPDATE_FILTER_MODEL, payload });
    };

    const setPaginationModel = (payload: any) =>
        dispatch({ type: ProductControlReducerActionType.UPDATE_PAGINATION_MODEL, payload });

    const setProductFilterModel = (payload: any) => {
        dispatch({ type: ProductControlReducerActionType.UPDATE_PRODUCT_FILTER_MODEL, payload });
    };

    const searchTerm = filterModel?.quickFilterValues?.join(' ');
    const searchProps: ProductSearchQueryProps = {
        search: searchTerm === '' ? undefined : searchTerm,
        paging: { pageNumber: paginationModel.page, size: paginationModel.pageSize },
        sort: { key: 'lastUpdated', isAscending: false },
        filters: {},
    };
    if (sortModel.length > 0) {
        searchProps.sort = { key: sortModel[0].field, isAscending: sortModel[0].sort === 'asc' };
    }

    if (productFilterModel) {
        const { category, subCategory, status, brand, classification } = productFilterModel;

        searchProps.filters = {
            category,
            subCategory,
            status,
            brand,
            classification,
        };
    }

    return {
        isGlobalLoading,
        clearDrawerProduct,
        closeDrawer,
        closeMergeDrawer,
        filterModel,
        isDrawerOpen,
        isMergeDrawerOpen,
        drawerProduct,
        openDrawer,
        openMergeDrawer,
        searchTerm,
        setFilterModel,
        setDrawerProduct,
        setSortModel,
        setProductFilterModel,
        productFilterModel,
        sortModel,
        searchProps,
        paginationModel,
        setPaginationModel,
        manageGlobalLoader,
    };
}
