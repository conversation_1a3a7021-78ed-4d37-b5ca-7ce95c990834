import React from 'react';
import { convertPxToRem, StaticChip } from '@treez-inc/component-library';
import { Alert, Box, styled } from '@mui/material/';
/** Interfaces */
import Grid from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import { ProductDto, ProductSearchResponse } from '../../../interfaces/dto/product';
import { ProductTitleTypography } from '../../../styles/StyledProductDetailsDrawer';
import { ImageDetailsDto } from '../../../interfaces/dto/imageDetails';
import buildImageUrl from '../../../utils/images';
import { SkuDto } from '../../../interfaces/dto/sku';

const AttributesChipContainer = styled(Box)(() => ({
    display: 'flex',
    flexWrap: 'wrap',
    gap: convertPxToRem(12),
}));

const StyledModalContainer = styled(Box)(() => ({
    display: 'flex',
    justifyContent: 'center',
    flexDirection: 'column',
    gap: convertPxToRem(14),
}));

const StyledImgContainer = styled('img')(() => ({
    borderRadius: '18px',
    border: '1px solid var(--Light-Mode-Grey-03---Fill, #F0F0F0)',
    background: 'var(--black-white-primary-white, #FFF)',
}));

const StyledProductInfoDiv = styled('div')(() => ({
    display: 'flex',
    paddingRight: '10px',
    fontSize: 'x-small',
    color: 'gray',
}));

const StyledBrandNameDiv = styled('div')(() => ({
    paddingRight: '10px',
    textTransform: 'uppercase',
}));

const StyledInfoList = styled('li')(() => ({
    paddingRight: '10px',
    textTransform: 'uppercase',
}));

const ConfirmMergeDetailsDrawer = ({
    targetProduct,
    productQueryData,
}: {
    targetProduct: ProductSearchResponse[];
    productQueryData: ProductDto[];
}) => (
    <StyledModalContainer>
        <Typography variant="largeText">
            {`After merging, this will be the retained parent product and its variants.`}{' '}
            <Typography variant="largeText"> Everything look good? </Typography>
        </Typography>
        <Alert severity="warning">
            <strong>Alert: Merges cannot be undone!</strong>
        </Alert>
        {targetProduct?.map((data: ProductSearchResponse) => {
            const productDetails: ProductDto = productQueryData?.filter((product) => product.id === data.productId)[0];
            const mainImage = productDetails?.images?.find((image: ImageDetailsDto) => image.order === 1);
            const displayImage = mainImage || (data?.images?.length > 0 ? data.images[0] : ({} as ImageDetailsDto));
            const imageUrl = displayImage.imageUrl ?? buildImageUrl(displayImage.organizationId, displayImage.imageId);
            return (
                <Box
                    sx={{
                        boxShadow: 1,
                        p: 1,
                        m: 1,
                        borderRadius: 2,
                    }}
                >
                    <Grid
                        container
                        onClick={(event) => event.stopPropagation()}
                        onFocus={(event) => event.stopPropagation()}
                    >
                        <Grid item xs={2}>
                            <StyledImgContainer src={imageUrl} height="80px" width="60px" />
                        </Grid>
                        <Grid
                            item
                            xs={10}
                            style={{
                                width: 'auto',
                            }}
                        >
                            <StyledProductInfoDiv>
                                <StyledBrandNameDiv>{data.brandName}</StyledBrandNameDiv>
                                <StyledInfoList>{data.productCategoryName}</StyledInfoList>
                                <StyledInfoList>{data.productSubCategoryName}</StyledInfoList>
                                <StyledInfoList>{data?.classification}</StyledInfoList>
                            </StyledProductInfoDiv>
                            <ProductTitleTypography style={{ paddingTop: '5px', paddingBottom: '5px' }}>
                                {data.productName}
                            </ProductTitleTypography>
                            <div style={{ display: 'flex' }}>
                                {data?.variants?.map((variantAttribute: SkuDto) => (
                                    <AttributesChipContainer>
                                        <Box
                                            key={`chip-${variantAttribute?.unitCount}-${Math.random().toFixed(6)}`}
                                            sx={{ marginRight: '4px' }}
                                        >
                                            {variantAttribute?.unitCount && (
                                                <StaticChip
                                                    variant="filled"
                                                    testId="multiplestore-chip"
                                                    // eslint-disable-next-line no-nested-ternary
                                                    label={`${variantAttribute.unitCount} ${
                                                        variantAttribute.uom ? variantAttribute.uom?.toLowerCase() : ''
                                                    }`}
                                                />
                                            )}

                                            {variantAttribute?.merchandiseSize && (
                                                <StaticChip variant="filled" label={variantAttribute.merchandiseSize} />
                                            )}
                                        </Box>
                                    </AttributesChipContainer>
                                ))}
                            </div>
                        </Grid>
                    </Grid>
                </Box>
            );
        })}
    </StyledModalContainer>
);

export default ConfirmMergeDetailsDrawer;
