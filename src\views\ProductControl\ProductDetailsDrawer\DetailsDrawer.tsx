import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Drawer } from '@treez-inc/component-library';
/** Interfaces */
import { ProductDto } from '../../../interfaces/dto/product';
/** Hooks */
import useProductFormMessages from '../../../hooks/product-form/useProductFormMessages';
/** Nested-components */
import ProductImageCarousel from './contentSections/ProductImageCarousel';
import ProductHighlights from './contentSections/ProductHighlights';
import ProductTitle from './contentSections/ProductTitle';
import ProductDescription from './contentSections/ProductDescription';
import ProductVariants from './contentSections/ProductVariants';
import ProductAttributes from './contentSections/ProductAttributes';
import { isEmpty } from '../../../utils/common';
import { EDIT_PRODUCT_URL, PRICING_PAGE_URL } from '../../../utils/constants';

interface IProductDetailModal {
    isOpen: boolean;
    closeDrawer: () => void;
    currentProductDetails: ProductDto;
}

const ProductDetailsDrawer: React.FC<IProductDetailModal> = ({ isOpen, closeDrawer, currentProductDetails }) => {
    let topMgThc: number = 0;
    const navigate = useNavigate();
    const { showBackendError } = useProductFormMessages();
    const productCategoryName: string = currentProductDetails?.category as string;

    currentProductDetails?.variants?.forEach((variant) => {
        if (!isEmpty(variant?.details?.totalMgThc) && topMgThc < (variant?.details?.totalMgThc || 0)) {
            topMgThc = variant?.details?.totalMgThc || 0;
        }
    });

    const onHandleEditProduct = (productId: string | undefined) => {
        if (productId) {
            navigate(`${EDIT_PRODUCT_URL}/${productId}`);
        }
    };

    const onHandleEditPricing = () => {
        const { id: productId } = currentProductDetails;
        if (productId) {
            try {
                navigate(`${PRICING_PAGE_URL}?search=${currentProductDetails.name}`);
            } catch (err) {
                showBackendError(err);
            }
        }
    };

    const onDrawerClose = () => {
        closeDrawer();
    };

    return (
        <Drawer
            onClose={onDrawerClose}
            open={isOpen}
            primaryButtonProps={{
                label: 'Edit Product',
                onClick: () => onHandleEditProduct(currentProductDetails?.id),
            }}
            secondaryButtonProps={{
                label: 'Edit Pricing',
                onClick: () => onHandleEditPricing(),
            }}
            testId="product-details-drawer"
            title="Product Detail"
        >
            <ProductImageCarousel details={currentProductDetails} />
            <ProductHighlights productInfo={currentProductDetails} />
            <ProductTitle product={currentProductDetails} topMgThc={topMgThc} />
            <ProductDescription description={currentProductDetails?.details?.description || ''} />
            <ProductVariants
                variants={currentProductDetails?.variants || []}
                productCategoryName={productCategoryName}
            />
            <ProductAttributes attributes={currentProductDetails?.productAttributes} />
        </Drawer>
    );
};

export default ProductDetailsDrawer;
