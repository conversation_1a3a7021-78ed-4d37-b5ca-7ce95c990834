import React, { useState } from 'react';
import { Drawer, Modal } from '@treez-inc/component-library';
/** Interfaces */
import Typography from '@mui/material/Typography';
import { UseQueryResult } from 'react-query';
import { ProductDto, ProductMergeDto, ProductSearchResponse } from '../../../interfaces/dto/product';
/** Hooks */
import { isEmpty } from '../../../utils/common';
import useProductControl from '../hooks/useProductControl';
import useSnackbarContext from '../../../hooks/snackbar/useSnackbarContext';
import useListProductQuery from '../../../queries/useListProductQuery';
import ConfirmMergeDetailsDrawer from './ConfirmMergeDetailsDrawer';
import MergeProductDetails from './MergeProductDetailsSection';

interface IProductDetailModal {
    isOpen: boolean;
    closeDrawer: () => void;
    selectedProducts: ProductSearchResponse[];
    onRefetchAPI: Function;
}

const MergeProductDetailsDrawer: React.FC<IProductDetailModal> = ({
    isOpen,
    closeDrawer,
    selectedProducts,
    onRefetchAPI,
}) => {
    const { setSnackbar } = useSnackbarContext();
    const [targetProductId, setTargetProductId] = useState<string>('');
    const [filterProduct, setFilterProduct] = useState<ProductSearchResponse[]>([]);

    const { mergeProducts } = useProductControl();
    const [isModalOpen, setModalOpen] = React.useState<boolean>(false);
    const [isDisableMergeButton, SetIsDisableMergeButton] = useState<boolean>(true);

    const productQuery: UseQueryResult<ProductDto[], unknown> = useListProductQuery({
        productIds: selectedProducts.map((productData: ProductSearchResponse) => productData.productId),
        options: {
            enabled: !isEmpty(selectedProducts.map((productData: ProductSearchResponse) => productData.productId)),
        },
    });

    const { data = [] } = productQuery;
    const productQueryData = data;

    const onDrawerClose = () => {
        onRefetchAPI();
        closeDrawer();
        setFilterProduct([]);
        setModalOpen(false);
        setTargetProductId('');
        SetIsDisableMergeButton(true);
    };

    const onHandleEditProduct = async () => {
        if (targetProductId) {
            const filterTargetProduct: ProductSearchResponse[] = selectedProducts.filter(
                (sp) => sp.productId !== targetProductId,
            );
            const ids: string[] = filterTargetProduct.map(
                (productData: ProductSearchResponse) => productData.productId,
            );

            const mergeData: ProductMergeDto = { targetMergeProductId: targetProductId, productIds: ids };
            await mergeProducts(mergeData, onRefetchAPI, onDrawerClose);
        } else {
            setSnackbar({
                message: `something went wrong`,
                severity: 'error',
                iconName: 'Error',
            });
        }
    };

    const handleTargetProductIdChnyType = (event: any, productSearchData: ProductSearchResponse) => {
        setTargetProductId(productSearchData.productId);
        SetIsDisableMergeButton(false);
    };

    const onMergeButtonClick = async () => {
        const filterTargetProduct: ProductSearchResponse[] = selectedProducts.filter(
            (sp) => sp.productId === targetProductId,
        );

        setFilterProduct(filterTargetProduct);
        setModalOpen(true);
    };

    return (
        <Drawer
            onClose={onDrawerClose}
            open={isOpen}
            primaryButtonProps={{
                label: 'Merge',
                onClick: () => onMergeButtonClick(),
                disabled: isDisableMergeButton,
            }}
            secondaryButtonProps={{
                label: 'Cancel',
                onClick: () => closeDrawer(),
            }}
            testId="merge-product-details-drawer"
            title="Merge Products"
        >
            <Typography>
                Choose a master product into which all other selected products will be consolidated or merged
            </Typography>
            {selectedProducts?.map((productSearchData: ProductSearchResponse) => (
                <div data-testid="merge-product-item" key={productSearchData.id}>
                    <MergeProductDetails
                        targetProductId={targetProductId}
                        productQueryData={productQueryData}
                        productSearchData={productSearchData}
                        handleTargetProductIdChnyType={handleTargetProductIdChnyType}
                    />
                </div>
            ))}

            <Modal
                content={
                    <ConfirmMergeDetailsDrawer targetProduct={filterProduct} productQueryData={productQueryData} />
                }
                onClose={() => {
                    setModalOpen(false);
                }}
                primaryButton={{
                    label: 'Merge',
                    onClick: () => onHandleEditProduct(),
                }}
                secondaryButton={{
                    label: 'Cancel',
                    onClick: () => {
                        setModalOpen(false);
                    },
                }}
                testId="merge-product-modal"
                title="Confirm Merge"
                open={isModalOpen}
            />
        </Drawer>
    );
};

export default MergeProductDetailsDrawer;
