import React from 'react';
import { convertPxToRem, StaticChip } from '@treez-inc/component-library';
import { Accordion, AccordionDetails, AccordionSummary, Box, styled } from '@mui/material/';
import Grid from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import { ExpandMore } from '@mui/icons-material';
import { ImageDetailsDto } from '../../../interfaces/dto/imageDetails';
import { ProductDto, ProductSearchResponse } from '../../../interfaces/dto/product';
import buildImageUrl from '../../../utils/images';
import { ProductTitleTypography } from '../../../styles/StyledProductDetailsDrawer';
import ProductAttributes from './contentSections/ProductAttributes';
import { SkuDto } from '../../../interfaces/dto/sku';

const StyledAccordion = styled(Accordion)`
    & .MuiAccordionSummary-root {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    & .MuiAccordionSummary-content {
        margin: 0px;
    }

    & .MuiAccordionDetails-root {
        padding: 1px;
    }

    & .MuiAccordion-root:after {
        height: 1px;
    }
`;

const AttributesChipContainer = styled(Box)(() => ({
    display: 'flex',
    flexWrap: 'wrap',
    gap: convertPxToRem(12),
}));

const StyledProductInfoDiv = styled('div')(() => ({
    display: 'flex',
    paddingRight: '10px',
    fontSize: 'x-small',
    color: 'gray',
}));

const StyledBrandNameDiv = styled('div')(() => ({
    paddingRight: '10px',
    textTransform: 'uppercase',
}));

const SubHeader = styled(Typography)(({ theme }) => ({
    color: theme.palette.grey08.main,
    fontSize: '0.7rem',
}));

const StyledInfoList = styled('li')(() => ({
    paddingRight: '10px',
    textTransform: 'uppercase',
}));

const StyledImgContainer = styled('img')(() => ({
    borderRadius: '18px',
    border: '1px solid var(--Light-Mode-Grey-03---Fill, #F0F0F0)',
    background: 'var(--black-white-primary-white, #FFF)',
}));

const MergeProductDetails = ({
    productQueryData,
    productSearchData,
    targetProductId,
    handleTargetProductIdChnyType,
}: {
    targetProductId: string;
    productQueryData: ProductDto[];
    productSearchData: ProductSearchResponse;
    handleTargetProductIdChnyType: Function;
}) => {
    const productDetails: ProductDto = productQueryData?.filter(
        (product) => product.id === productSearchData.productId,
    )[0];
    const mainImage = productDetails?.images?.find((image: ImageDetailsDto) => image.order === 1);
    const displayImage =
        mainImage || (productSearchData?.images?.length > 0 ? productSearchData.images[0] : ({} as ImageDetailsDto));
    const imageUrl = displayImage.imageUrl ?? buildImageUrl(displayImage.organizationId, displayImage.imageId);

    return (
        <Box
            key={`merge-product-details-drawer-${productSearchData.productId}-${Math.random().toFixed(4)}`}
            sx={{
                boxShadow: 1,
                p: 1,
                m: 1,
                borderRadius: 2,
            }}
        >
            <StyledAccordion style={{ boxShadow: 'none' }}>
                <AccordionSummary expandIcon={<ExpandMore />} id={productSearchData.productId}>
                    <Grid
                        container
                        onClick={(event) => event.stopPropagation()}
                        onFocus={(event) => event.stopPropagation()}
                    >
                        <Grid item xs={1} style={{ alignSelf: 'center' }}>
                            <input
                                key={productSearchData.productId}
                                type="radio"
                                value={targetProductId}
                                checked={targetProductId === productSearchData.productId}
                                onClick={(event) => handleTargetProductIdChnyType(event, productSearchData)}
                            />
                        </Grid>
                        <Grid item xs={2}>
                            <StyledImgContainer src={imageUrl} height="80px" width="60px" />
                        </Grid>
                        <Grid item xs={9}>
                            <StyledProductInfoDiv>
                                <StyledBrandNameDiv>{productSearchData.brandName}</StyledBrandNameDiv>
                                <StyledInfoList>{productSearchData.productCategoryName}</StyledInfoList>
                                <StyledInfoList>{productSearchData.productSubCategoryName}</StyledInfoList>
                                <StyledInfoList>{productSearchData?.classification}</StyledInfoList>
                            </StyledProductInfoDiv>
                            <ProductTitleTypography style={{ paddingTop: '5px', paddingBottom: '5px' }}>
                                {productSearchData.productName}
                            </ProductTitleTypography>
                            <div style={{ display: 'flex' }}>
                                {productSearchData?.variants?.map((variantAttribute: SkuDto) => (
                                    <AttributesChipContainer>
                                        <Box
                                            key={`chip-${variantAttribute?.unitCount}-${Math.random().toFixed(6)}`}
                                            sx={{ marginRight: '4px' }}
                                        >
                                            {variantAttribute?.unitCount && (
                                                <StaticChip
                                                    variant="filled"
                                                    testId="multiplestore-chip"
                                                    // eslint-disable-next-line no-nested-ternary
                                                    label={`${variantAttribute.unitCount} ${
                                                        variantAttribute.uom ? variantAttribute.uom?.toLowerCase() : ''
                                                    }`}
                                                />
                                            )}

                                            {variantAttribute?.merchandiseSize && (
                                                <StaticChip variant="filled" label={variantAttribute.merchandiseSize} />
                                            )}
                                        </Box>
                                    </AttributesChipContainer>
                                ))}
                            </div>
                        </Grid>
                    </Grid>
                </AccordionSummary>
                <AccordionDetails style={{ boxShadow: 'none' }}>
                    <Grid container spacing={2}>
                        <Grid item xs={6} style={{ alignSelf: 'center' }}>
                            <SubHeader>STRAIN</SubHeader>
                            <Typography variant="body2">{productDetails?.details?.strain || '--'}</Typography>
                        </Grid>
                        <Grid item xs={6} style={{ alignSelf: 'center' }}>
                            <SubHeader>EXTRACTION METHOD</SubHeader>
                            <Typography variant="body2">{productDetails?.details?.extractionMethod || '--'}</Typography>
                        </Grid>
                        <Grid item xs={12} style={{ alignSelf: 'center' }}>
                            <SubHeader>Description</SubHeader>
                            <Typography variant="body2">{productDetails?.details?.description || '--'}</Typography>
                        </Grid>
                    </Grid>
                    <ProductAttributes attributes={productDetails?.productAttributes} />
                </AccordionDetails>
            </StyledAccordion>
        </Box>
    );
};

export default MergeProductDetails;
