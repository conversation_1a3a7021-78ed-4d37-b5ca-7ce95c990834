/* eslint-disable react/jsx-no-useless-fragment */
import React from 'react';
import { StaticChip } from '@treez-inc/component-library';
/** Utils */
import { chipColor } from '../../../../utils/constants';
import { isEmpty } from '../../../../utils/common';
/** Styled-Components */
import {
    SectionWrapper,
    SubHeader,
    AttributesChipContainer,
    ProductCustomDivider,
} from '../../../../styles/StyledProductDetailsDrawer';

interface IProductDetailAttributes {
    attributes: any;
}

interface IModifiedAttributes {
    name: string;
    attributes: any[];
    color: any;
}

const ProductAttributes: React.FC<IProductDetailAttributes> = ({ attributes }) => {
    const categories: Record<string, IModifiedAttributes> = {};

    attributes?.forEach(({ attribute }: any) => {
        const attributeCategoryId = attribute.attributeCategory.id;
        if (categories[attributeCategoryId]) {
            categories[attributeCategoryId].attributes.push(attribute);
        } else {
            categories[attributeCategoryId] = {
                name: attribute.attributeCategory.name.split(' ')[0],
                attributes: [attribute],
                color: chipColor[attribute?.attributeCategory?.name.split(' ')[0]] || 'gray',
            };
        }
    });

    const sortedAttributes =
        Object.keys(categories).length > 0
            ? Object.entries(categories)
                  .map(([key, value]) => ({ id: key, attribute: value }))
                  .sort((a, b) => a.attribute.name.localeCompare(b.attribute.name))
            : [];

    return (
        <>
            {!isEmpty(sortedAttributes) ? (
                sortedAttributes.map((data) => (
                    <SectionWrapper>
                        <SubHeader>{data.attribute.name}</SubHeader>
                        <AttributesChipContainer>
                            {data.attribute.attributes.map((attribute: any) => (
                                <StaticChip
                                    variant="filled"
                                    label={attribute.name}
                                    color={data.attribute.color}
                                    key={attribute.id}
                                />
                            ))}
                        </AttributesChipContainer>
                        <ProductCustomDivider />
                    </SectionWrapper>
                ))
            ) : (
                <SectionWrapper>
                    <SubHeader>No attributes available.</SubHeader>
                </SectionWrapper>
            )}
        </>
    );
};

export default ProductAttributes;
