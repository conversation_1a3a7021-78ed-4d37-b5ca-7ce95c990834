import React from 'react';
/** Styled-Components */
import {
    SubHeader,
    SectionWrapper,
    ProductGlobalDescTypography,
    ProductGlobalDescContainer,
    ProductGlobalDescription,
} from '../../../../styles/StyledProductDetailsDrawer';

interface IProductGlobalDescription {
    description: string;
}

const ProductDescription: React.FC<IProductGlobalDescription> = ({ description }) => (
    <SectionWrapper>
        <ProductGlobalDescTypography variant="largeTextStrong">Global Description</ProductGlobalDescTypography>
        <ProductGlobalDescContainer>
            {description.length > 0 ? (
                <ProductGlobalDescription>{description}</ProductGlobalDescription>
            ) : (
                <SubHeader>No description available.</SubHeader>
            )}
        </ProductGlobalDescContainer>
    </SectionWrapper>
);

export default ProductDescription;
