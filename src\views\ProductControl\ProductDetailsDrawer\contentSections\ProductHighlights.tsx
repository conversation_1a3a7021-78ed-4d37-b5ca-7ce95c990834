/* eslint-disable react/no-array-index-key */
import React from 'react';
import Box from '@mui/material/Box';
import { Icon } from '@treez-inc/component-library';
import { IconName } from '@treez-inc/component-library/dist/components/Icon/types';
import convertPxToRem from '@treez-inc/component-library/dist/util-functions/convertPxToRem';
/** Interfaces */
import { ProductDto } from '../../../../interfaces/dto/product';
/** Styled-Components */
import {
    SectionWrapper,
    HighlighterContainer,
    HighlighterTypography,
    HighlighterCustomDotIcon,
} from '../../../../styles/StyledProductDetailsDrawer';
import findCategoryIcon from '../../../../utils/categoryUtils';

interface IHighlightDetailsProps {
    isCate?: IconName;
    title: string | undefined;
}

const HighlightDetails: React.FC<IHighlightDetailsProps> = ({ isCate, title }) => (
    <>
        <Box sx={{ marginLeft: convertPxToRem(!isCate ? 8 : 0), marginRight: convertPxToRem(!isCate ? 8 : 4) }}>
            {!isCate ? <HighlighterCustomDotIcon /> : <Icon iconName={isCate} color="grey08" fontSize="small" />}
        </Box>
        <HighlighterTypography variant="largeCapitalizedText">{title || 'N/A'}</HighlighterTypography>
    </>
);

interface IProductHighlights {
    productInfo: ProductDto;
}

const ProductHighlights: React.FC<IProductHighlights> = ({ productInfo }) => {
    const { category, productSubCategory, brand, details, name } = productInfo;

    const highLightList = [
        { isCate: findCategoryIcon(category || '') as IconName, title: category },
        { title: productSubCategory?.name },
        { title: brand?.name },
        { title: details?.classification },
        { title: name },
    ];

    return (
        <SectionWrapper>
            <HighlighterContainer>
                {highLightList.map((highlight, index) => (
                    <HighlightDetails key={`highligh${index}`} {...highlight} />
                ))}
            </HighlighterContainer>
        </SectionWrapper>
    );
};

export default ProductHighlights;
