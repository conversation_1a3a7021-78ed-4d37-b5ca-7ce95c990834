import React from 'react';
/** Interfaces */
import { ProductDto } from '../../../../interfaces/dto/product';
/** Styled-components */
import { CarouselWrapper, ImageWrapper, SubHeader } from '../../../../styles/StyledProductDetailsDrawer';
import { ImageDetailsDto } from '../../../../interfaces/dto/imageDetails';
import buildImageUrl from '../../../../utils/images';

interface IProductImageCarousel {
    details: ProductDto;
}

const ProductImageCarousel: React.FC<IProductImageCarousel> = ({ details }) => {
    if (details?.images?.length) {
        const mainImage = details?.images?.find((image: ImageDetailsDto) => image.order === 1);
        const displayImage = mainImage ?? details.images[0];
        const imageUrl = displayImage.imageUrl ?? buildImageUrl(displayImage.organizationId, displayImage.imageId);
        return (
            <CarouselWrapper>
                <ImageWrapper>
                    <img src={imageUrl} alt="product-preview" height="100%" />
                </ImageWrapper>
            </CarouselWrapper>
        );
    }

    return (
        <CarouselWrapper>
            <ImageWrapper>
                <SubHeader>No image preview available.</SubHeader>
            </ImageWrapper>
        </CarouselWrapper>
    );
};

export default ProductImageCarousel;
