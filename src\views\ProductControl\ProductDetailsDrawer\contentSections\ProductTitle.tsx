import React from 'react';
/** Styled-Component */
import {
    SectionWrapper,
    ProductTitleTypography,
    ProductMgThcTypography,
} from '../../../../styles/StyledProductDetailsDrawer';
import { GlobalBadge } from '../../../../styles/globalStyles';
import { ProductDto } from '../../../../interfaces/dto/product';

interface IProductTitleProps {
    product: ProductDto;
    topMgThc: number;
}

const ProductTitle: React.FC<IProductTitleProps> = ({ product, topMgThc }) => (
    <SectionWrapper>
        <ProductTitleTypography variant="h2">
            {product.name} {product.verifiedReferenceId && <GlobalBadge fontSize="medium" />}
        </ProductTitleTypography>
        {topMgThc !== 0 && <ProductMgThcTypography variant="h3">{topMgThc} MG THC</ProductMgThcTypography>}
    </SectionWrapper>
);

export default ProductTitle;
