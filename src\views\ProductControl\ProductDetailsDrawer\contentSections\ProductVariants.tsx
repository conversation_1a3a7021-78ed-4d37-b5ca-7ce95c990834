import React, { useRef, useEffect, useState } from 'react';
import convertPxToRem from '@treez-inc/component-library/dist/util-functions/convertPxToRem';
/** Utils */
import { formatSkuValue, convertUomSizeToOunce, getVariantSizeLabel } from '../../../../utils/variantCardUtils';
import { centsToDollars } from '../../../../utils/priceUtils';
/** Styled-Components */
import { VariantPromoSampleBadge, VariantPromoSampleTypo } from '../../../../styles/globalStyles';
import {
    SectionWrapper,
    ProductCustomDivider,
    VariantDetails,
    SubHeader,
    VariantCardsWrapper,
    VariantCardContainer,
    VariantCardTitle,
    VariantCardPricing,
    VariantSkuTypography,
    VariantOunceTypography,
} from '../../../../styles/StyledProductDetailsDrawer';
import { SkuDto } from '../../../../interfaces/dto/sku';

interface IVariantsProps {
    variants: SkuDto[];
    productCategoryName: string;
}

const ProductVariantCard = ({ variant, productCategoryName }: { variant: SkuDto; productCategoryName: string }) => {
    const cardPriceRef: any = useRef(null);
    const [calcWidth, setCalcWidth] = useState(100);

    const { defaultPrices, details, sku } = variant;

    useEffect(() => {
        if (cardPriceRef.current) setCalcWidth(cardPriceRef.current?.getBoundingClientRect().width);
    }, [cardPriceRef.current]);

    const variantSize = getVariantSizeLabel(variant, productCategoryName) || 'N/A';
    const basePrice = defaultPrices?.base || 0;
    const variantPrice = defaultPrices?.base !== 0 ? `$ ${centsToDollars(basePrice)}` : '$ 0';
    const variantThcCount = details?.totalMgThc || '';
    const variantInOunce = variant?.amount ? convertUomSizeToOunce(variant) : '';
    const variantSku = sku ? formatSkuValue(sku) : '-';
    const isPromoVariant = details?.isPromo;
    const isSampleVariant = details?.isSample;
    return (
        <VariantCardContainer>
            {!isPromoVariant && !isSampleVariant ? null : (
                <VariantPromoSampleBadge>
                    <VariantPromoSampleTypo variant="smallTextStrong">
                        {(isPromoVariant && 'Promo') || (isSampleVariant && 'Sample')}
                    </VariantPromoSampleTypo>
                </VariantPromoSampleBadge>
            )}
            <VariantCardTitle variant="largeTextStrong">{variantSize}</VariantCardTitle>
            <VariantCardPricing ref={cardPriceRef} id="variant-price-typo" variant="largeText">
                {variantPrice}
            </VariantCardPricing>
            <ProductCustomDivider style={{ width: convertPxToRem(calcWidth) }} />
            {variantThcCount && (
                <VariantOunceTypography variant="extraSmallText">{variantThcCount} MG THC</VariantOunceTypography>
            )}
            {!variantThcCount && variantInOunce && (
                <VariantOunceTypography variant="extraSmallText">{variantInOunce} Oz</VariantOunceTypography>
            )}
            <VariantSkuTypography variant="extraSmallText">SKU {variantSku}</VariantSkuTypography>
        </VariantCardContainer>
    );
};

const ProductVariants: React.FC<IVariantsProps> = ({ variants, productCategoryName }) => (
    <SectionWrapper>
        <ProductCustomDivider />
        <VariantDetails>
            <SubHeader variant="largeTextStrong">Sizes</SubHeader>
            <VariantCardsWrapper>
                {variants && variants.length > 0 ? (
                    variants.map((variant: SkuDto) => (
                        <ProductVariantCard
                            key={variant?.id}
                            variant={variant}
                            productCategoryName={productCategoryName}
                        />
                    ))
                ) : (
                    <SubHeader>No variants available.</SubHeader>
                )}
            </VariantCardsWrapper>
        </VariantDetails>
        <ProductCustomDivider />
    </SectionWrapper>
);

export default ProductVariants;
