/* eslint-disable @typescript-eslint/no-unused-vars */
import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON>outer } from 'react-router-dom';
import { render } from '@testing-library/react';
import { TreezThemeProvider } from '@treez-inc/component-library';
import SnackbarProvider from '../../../../providers/SnackbarProvider';
import '@testing-library/jest-dom';
import ProductDetailsDrawer from '../DetailsDrawer';
import PRODUCT_CATEGORIES from '../../../../utils/product-categories-data';
import { ProductDto } from '../../../../interfaces/dto/product';
import { SkuDto } from '../../../../interfaces/dto/sku';
import { Status } from '../../../../utils/constants';

jest.mock('../../../../views/ProductFormV2/VariantsSection/VariantsSection', () =>
    jest.fn().mockReturnValue(<>Test</>),
);

describe('Product Modal component', () => {
    const product: ProductDto = {
        id: '87654321',
        name: 'Mock product',
        status: Status.ACTIVE,
    };
    const category: string = 'Mock category';
    const variants: SkuDto[] = [
        {
            id: '12345678',
            name: 'dummy variant',
            defaultPrices: {
                base: 10,
            },
            details: {
                description: 'This is a mock variant.',
            },
            organizationId: 'iitje290joseroj',
            productId: '09wujo23nr4onoifn890',
            referenceIds: [
                {
                    sourceId: 'iklsdfhjowon',
                    sourceName: 'i2h3oi4n23l4no',
                },
            ],
            sku: '238423984723947',
            status: Status.ACTIVE,
            uom: 'mg',
            amount: 10,
            unitCount: 1,
            merchandiseSize: null,
        },
    ];

    const details = {
        details: {
            description: 'This is a product',
        },
        name: 'Test Product 1',
        productSubCategory: {
            createdAt: '2023-02-26T22:17:23.571Z',
            deletedAt: null,
            id: '7f4fd2b5-7252-42f7-931b-3cc690239356',
            isCannabis: true,
            name: 'flower - general',
            productCategoryId: 'fabba501-ea30-4301-9f61-e826e0b19a3e',
            updatedAt: '2023-02-26T22:17:23.571Z',
        },
        variants,
        status: Status.ACTIVE,
    };
    const currentProductDetails = {
        id: '9f10491a-ff2c-45e3-8fac-47d9f448bcdc',
        name: 'Test Product 1',
        productSubCategory: {
            isCannabis: true,
            id: '7f4fd2b5-7252-42f7-931b-3cc690239356',
            name: 'flower - general',
            productCategoryId: 'fabba501-ea30-4301-9f61-e826e0b19a3e',
        },
        status: Status.ACTIVE,
    };

    const categories = [
        {
            id: 'f3c3d751-0b27-4819-9927-9e69ff54de12',
            createdAt: '2023-03-13T22:46:38.593Z',
            updatedAt: '2023-03-13T22:46:38.593Z',
            deletedAt: null,
            name: PRODUCT_CATEGORIES.Beverage,
            isCannabis: true,
        },
        {
            id: '338244bf-04e4-4bfd-b99e-62dcfe795dcd',
            createdAt: '2023-03-13T22:46:38.593Z',
            updatedAt: '2023-03-13T22:46:38.593Z',
            deletedAt: null,
            name: PRODUCT_CATEGORIES.Pill,
            isCannabis: true,
        },
        {
            id: '37bdb9b9-6b04-4f35-acc6-04cc6e4024a0',
            createdAt: '2023-03-13T22:46:38.593Z',
            updatedAt: '2023-03-13T22:46:38.593Z',
            deletedAt: null,
            name: PRODUCT_CATEGORIES.Tincture,
            isCannabis: true,
        },
        {
            id: 'af5a698a-f5b1-44b9-91b9-c4355a99488b',
            createdAt: '2023-03-13T22:46:38.593Z',
            updatedAt: '2023-03-13T22:46:38.593Z',
            deletedAt: null,
            name: PRODUCT_CATEGORIES.Cartridge,
            isCannabis: true,
        },
    ];

    const productAttributes = [
        {
            attribute: {
                attributeCategory: {
                    id: '1d97a24c-9f72-437c-99a7-cc121eaaa1f2',
                    createdAt: '2023-04-13T08:29:43.531Z',
                    updatedAt: '2023-04-13T08:29:43.531Z',
                    deletedAt: null,
                    name: 'Tags',
                },
                attributeId: 'e2a93831-6976-4f9b-b81a-5b030cbab4a1',
                createdAt: '2023-04-13T09:04:39.917Z',
                deletedAt: null,
                id: '16ec0e79-b14e-44b1-b226-55e1f6a259e2',
                organizationId: '62fbdd71-aeb5-48da-8d5a-34b437333405',
                productId: '9f10491a-ff2c-45e3-8fac-47d9f448bcdc',
                updatedAt: '2023-04-13T09:04:39.917Z',
                name: 'Mushroom',
            },
            attributeCategoryId: '1d97a24c-9f72-437c-99a7-cc121eaaa1f2',
            createdAt: '2023-04-13T08:46:44.583Z',
            deletedAt: null,
            id: 'e2a93831-6976-4f9b-b81a-5b030cbab4a1',
            organizationId: '62fbdd71-aeb5-48da-8d5a-34b437333405',
            updatedAt: '2023-04-13T08:46:44.583Z',
        },
    ];

    it('loads and displays product details modal', async () => {
        render(
            <TreezThemeProvider>
                <SnackbarProvider>
                    <BrowserRouter>
                        <ProductDetailsDrawer
                            isOpen
                            closeDrawer={() => null}
                            currentProductDetails={currentProductDetails}
                            // categories={categories}
                        />
                    </BrowserRouter>
                </SnackbarProvider>
            </TreezThemeProvider>,
        );
    });

    it('renders product data in the modal', async () => {
        render(
            <TreezThemeProvider>
                <SnackbarProvider>
                    <BrowserRouter>
                        <ProductDetailsDrawer
                            isOpen
                            closeDrawer={() => null}
                            currentProductDetails={currentProductDetails}
                            // categories={'Flower'}
                        />
                    </BrowserRouter>
                </SnackbarProvider>
            </TreezThemeProvider>,
        );
    });
});
