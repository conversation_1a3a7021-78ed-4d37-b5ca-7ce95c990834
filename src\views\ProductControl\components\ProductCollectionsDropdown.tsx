import React from 'react';
import { allColors, convertPxToRem } from '@treez-inc/component-library';
import { Autocomplete, Box, createTheme, styled, TextField, ThemeProvider, createFilterOptions } from '@mui/material';
import { ProductCollectionMenuItems } from '../../../interfaces/dto/productCollection';
import AddNewOption, { NoOptions } from '../../../components/AddNewOption';
import ProductCollectionPermissions from '../../../permissions/productCollectionPermissions';

export const NEW_COLLECTION_PREFIX = 'NEW_COLLECTION:';

export const AddNewContainer = styled(Box)({
    marginTop: convertPxToRem(12),
});

const ProductCollectionsDropdown: React.FC<{
    productCollections: ProductCollectionMenuItems[];
    setProductCollection: React.Dispatch<React.SetStateAction<ProductCollectionMenuItems | undefined>>;
}> = ({ productCollections, setProductCollection }) => {
    const filter = createFilterOptions<ProductCollectionMenuItems>();
    const handleOptionChange = (data: any) => {
        if (typeof data === 'string') {
            return;
        }

        setProductCollection(data);
    };

    const autoCompleteTheme = createTheme({
        components: {
            MuiAutocomplete: {
                styleOverrides: {
                    root: {
                        marginTop: convertPxToRem(10),
                        background: allColors.gray.main,
                        borderRadius: convertPxToRem(15),
                    },
                    inputRoot: {
                        borderRadius: convertPxToRem(15),
                    },
                    option: {
                        '&:hover': {
                            background: `${allColors.green03.main} !important`,
                        },
                        "&[aria-selected='true']": {
                            background: `${allColors.green03.main} !important`,
                        },
                    },
                },
            },
        },
    });

    return (
        <>
            <ThemeProvider theme={autoCompleteTheme}>
                <Autocomplete
                    autoHighlight
                    clearOnBlur
                    getOptionLabel={(option): string => {
                        if (typeof option === 'string') {
                            return option;
                        }
                        if (option && option.displayValue) {
                            return option.displayName;
                        }
                        return '';
                    }}
                    noOptionsText={<NoOptions>No match found</NoOptions>}
                    handleHomeEndKeys
                    id="select-collection-option"
                    isOptionEqualToValue={(option, selectedOption) =>
                        option?.displayValue === selectedOption?.displayValue
                    }
                    options={productCollections}
                    renderOption={(props, option) => (
                        <li {...props} key={option.displayValue}>
                            {option.displayValue.indexOf(NEW_COLLECTION_PREFIX) !== -1 ? (
                                <AddNewContainer>
                                    <AddNewOption
                                        newSelectValue={option.displayName}
                                        permissions={[ProductCollectionPermissions.MANAGE_PRODUCT_COLLECTIONS]}
                                        showConfirmation={() => {}}
                                    />
                                </AddNewContainer>
                            ) : (
                                option.displayName
                            )}
                        </li>
                    )}
                    selectOnFocus
                    onChange={(_event, newValue) => {
                        if (newValue) handleOptionChange(newValue);
                    }}
                    renderInput={(params) => <TextField {...params} label="Select Collection*" />}
                    freeSolo
                    filterOptions={(options, params) => {
                        const filtered = filter(options, params);

                        const { inputValue } = params;
                        // Suggest the creation of a new value
                        const isExisting = options.some((option) => inputValue === option.displayName);
                        if (inputValue !== '' && !isExisting) {
                            filtered.push({
                                displayValue: `${NEW_COLLECTION_PREFIX}${inputValue}`,
                                displayName: inputValue,
                            });
                        }

                        return filtered;
                    }}
                />
            </ThemeProvider>
        </>
    );
};

export default ProductCollectionsDropdown;
