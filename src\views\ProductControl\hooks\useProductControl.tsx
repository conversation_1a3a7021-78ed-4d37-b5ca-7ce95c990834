import useSnackbarContext from '../../../hooks/snackbar/useSnackbarContext';
import { ProductDto, ProductMergeDto } from '../../../interfaces/dto/product';
import { SkuApiDto, SkuDto } from '../../../interfaces/dto/sku';
import useMergeProductMutation from '../../../mutations/product/useMergeProductMutation';
import useUpdateProductMutation from '../../../mutations/product/useUpdateProductMutation';
import useUpdateSkuMutation from '../../../mutations/sku/useUpdateSkuMutation';
import { Status } from '../../../utils/constants';

const useProductControl = () => {
    const { setSnackbar } = useSnackbarContext();
    const updateProductMutation = useUpdateProductMutation();
    const updateSkuMutation = useUpdateSkuMutation();
    const mergeProductMutation = useMergeProductMutation();
    const updateStatus = async (data: any) => {
        const mutation = updateProductMutation;
        await mutation.mutateAsync([{ ...data }]);
        setSnackbar({
            message: `${data?.name} has been ${data?.status !== Status.ACTIVE ? 'deactivated.' : 'activated.'}`,
            severity: 'info',
            iconName: 'Success',
        });
    };

    const updateVariantStatus = async (data: SkuDto) => {
        const mutation = updateSkuMutation;
        await mutation.mutateAsync([{ ...data }]);
        setSnackbar({
            message: `${data.unitCount || data.merchandiseSize}  ${data.uom}
             has been ${data?.status !== Status.ACTIVE ? 'deactivated.' : 'activated.'}`,
            severity: 'info',
            iconName: 'Success',
        });
    };

    const updateStatuses = async (data: ProductDto[], status: string) => {
        try {
            const mutation = updateProductMutation;
            await mutation.mutateAsync(data);
            setSnackbar({
                message: `Products have been ${status !== Status.ACTIVE ? 'deactivated.' : 'activated.'}`,
                severity: 'info',
                iconName: 'Success',
            });
            return true;
        } catch (error: any) {
            setSnackbar({
                message: `Something went wrong.`,
                severity: 'error',
                iconName: 'Error',
            });
        }
        return false;
    };

    const mergeProducts = async (data: ProductMergeDto, onRefetchAPI: Function, closeDrawer: Function) => {
        try {
            const mutation = mergeProductMutation;
            await mutation.mutateAsync(data);
            setSnackbar({
                message: `products merged successfully`,
                severity: 'info',
                iconName: 'Success',
            });
            onRefetchAPI();
            closeDrawer();
            return true;
        } catch (error: any) {
            setSnackbar({
                message: `${error?.response?.data?.message}`,
                severity: 'error',
                iconName: 'Error',
            });
        }
        return false;
    };

    const updateVariantStatuses = async (data: Partial<SkuApiDto>[], status: string) => {
        try {
            const mutation = updateSkuMutation;
            await mutation.mutateAsync(data);
            setSnackbar({
                message: `Sizes have been ${status !== Status.ACTIVE ? 'deactivated.' : 'activated.'}`,
                severity: 'info',
                iconName: 'Success',
            });
            return true;
        } catch (error: any) {
            setSnackbar({
                message: `Something went wrong.`,
                severity: 'error',
                iconName: 'Error',
            });
        }
        return false;
    };

    return {
        updateStatus,
        mergeProducts,
        updateVariantStatus,
        updateStatuses,
        updateVariantStatuses,
    };
};

export default useProductControl;
