import { SkuDto } from '../../interfaces/dto/sku';

function formatPrice(price: number): string {
    return price ? `$${price.toFixed(2)}` : '-';
}

function priceCentsToDollar(price: number): number {
    return price ? price / 100 : 0;
}

/**
 * @description if variant has = 0 or variants don't have default price , we'll simply return -
 * @description if variant has = 1 variant with default price we'll return single price eg - $1
 * @description if variants has > 1 variants with default price we'll return min and max price range string eg - $1-$2
 * @param productActiveVariants[] product variants is the input
 * @returns label
 */
export default function useBasePriceLabel(productActiveVariants: SkuDto[]): string {
    const label = '-';

    const variantLength = productActiveVariants.length;

    if (variantLength === 0) {
        return label;
    }

    if (variantLength === 1) {
        const basePrice = priceCentsToDollar(
            parseFloat(productActiveVariants[0]?.defaultPrices?.base as unknown as string),
        );
        return formatPrice(basePrice);
    }

    // variantLength > 1, filter retail prices and removed falsy
    const basePrices = productActiveVariants
        .map((productVariant: SkuDto) => Number(priceCentsToDollar(productVariant?.defaultPrices?.base ?? 0)))
        .filter(Boolean);

    if (basePrices.length === 0) {
        return label;
    }

    const min = Math.min(...basePrices);
    const max = Math.max(...basePrices);

    // if two values equal then return only one value as label
    if (min === max) {
        return formatPrice(max);
    }

    return `${formatPrice(min)} - ${formatPrice(max)}`;
}
