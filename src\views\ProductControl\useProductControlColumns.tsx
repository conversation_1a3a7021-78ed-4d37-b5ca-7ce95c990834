import React from 'react';
import { GridColDef } from '@mui/x-data-grid-pro';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import { Box, Typography, styled, Link } from '@mui/material';
import { StaticChip, convertPxToRem } from '@treez-inc/component-library';
import ProductControlTableHeader from './components/ProductControlTableHeader';
import { GlobalBadge } from '../../styles/globalStyles';
import getBasePriceLabel from './useBasePriceLabel';
import { EDIT_PRODUCT_URL, PricingMethodType } from '../../utils/constants';
import useQueryParams from '../../components/FilterComponent/hooks/useQueryParams';
import { isBaseSku } from '../../utils/variantCardUtils';
import { SkuDto } from '../../interfaces/dto/sku';

const ProductNameCell = styled(Box)`
    display: flex;
    flex-direction: row;
    grid-gap: 1em;
    align-items: center;
    justify-content: center;

    :hover {
        cursor: pointer;
        color: green;
    }
`;

const VariantNameCell = styled(Box)`
    display: flex;
    flex-direction: row;
    grid-gap: 1em;
    align-items: center;
    justify-content: center;
`;

const RowLink = styled(Link)(({ theme }) => ({
    color: `${theme.palette.secondary.main}`,

    ':hover': {
        color: `${theme.palette.success.main}`,
    },
}));

const RowLinkBox = styled(Box)(() => ({
    display: 'flex',
    flexDirection: 'row',
    columnGap: convertPxToRem(5),
}));

dayjs.extend(utc);

export default function useProductControlColumns(): Array<GridColDef> {
    const { getFiltersFromSearchParams } = useQueryParams();
    const columns: Array<GridColDef> = [
        {
            renderHeader: () => ProductControlTableHeader({ name: 'Product' }),
            field: 'productName',
            flex: 1.2,
            renderCell: (params) => (
                <>
                    {!params.row.isChild ? (
                        <ProductNameCell>
                            <RowLink
                                data-testid="product-control-product-table-product-name-container"
                                onClick={(e) => {
                                    e.preventDefault();
                                    const currentFilters = getFiltersFromSearchParams();
                                    localStorage.setItem('productControlFilters', JSON.stringify(currentFilters));
                                    localStorage.setItem('fromEditPage', 'true');
                                    window.history.pushState(null, '', `${EDIT_PRODUCT_URL}/${params.row.productId}`);
                                }}
                            >
                                <RowLinkBox>
                                    <Box>{params.row.productName}</Box>
                                    {params.row.verifiedReferenceId && <GlobalBadge fontSize="small" />}
                                </RowLinkBox>
                            </RowLink>
                        </ProductNameCell>
                    ) : (
                        <VariantNameCell>
                            {params.row.variants.map((variantName: SkuDto) => variantName.name)}
                        </VariantNameCell>
                    )}
                </>
            ),
        },
        {
            renderHeader: () => ProductControlTableHeader({ name: 'Size / Amount' }),
            field: 'allSizes',
            flex: 1,
            renderCell: (params) => (
                <Box sx={{ display: 'flex', width: '100%' }}>
                    {params?.row?.formattedVariantSizes &&
                        params.row.formattedVariantSizes.map((item: string) => (
                            <Box key={`chip-${item}-${Math.random().toFixed(6)}`} sx={{ marginRight: '4px' }}>
                                <StaticChip
                                    variant="filled"
                                    testId="multiplestore-chip"
                                    // eslint-disable-next-line no-nested-ternary
                                    label={item}
                                />
                            </Box>
                        ))}
                </Box>
            ),
        },
        {
            renderHeader: () => ProductControlTableHeader({ name: 'Brand' }),
            field: 'brandName',
            flex: 0.5,
            renderCell: ({ row }) => <Typography>{row.brandName}</Typography>,
        },
        {
            renderHeader: () => ProductControlTableHeader({ name: 'Category' }),
            field: 'productCategoryName',
            flex: 0.5,
            renderCell: ({ row }) => <Typography>{row.productCategoryName}</Typography>,
        },
        {
            renderHeader: () => ProductControlTableHeader({ name: 'Subcategory' }),
            field: 'productSubCategoryName',
            flex: 0.5,
            renderCell: ({ row }) => <Typography>{row.productSubCategoryName?.split(' - ').pop()}</Typography>,
        },
        {
            renderHeader: () => ProductControlTableHeader({ name: 'Classification' }),
            field: 'classification',
            flex: 0.7,
            sortable: false,
            renderCell: ({ row }) => <Typography>{row.classification}</Typography>,
        },
        {
            renderHeader: () => ProductControlTableHeader({ name: 'Status' }),
            field: 'status',
            flex: 0.5,
            renderCell: ({ row }) => <Typography>{row.status}</Typography>,
        },
        {
            renderHeader: () => ProductControlTableHeader({ name: 'Price' }),
            field: 'basePrice',
            flex: 1,
            sortable: false,
            renderCell: ({ row }) => {
                // If this product is priced by tier, show the tier label as price
                // NB: promos and samples are never priced by tier
                if (!row.isChild || isBaseSku(row.variants?.[0] ?? {})) {
                    const pricingMethod = row.pricingMethod ?? PricingMethodType.FLAT;
                    const priceTierLabel = row.priceTierLabel ?? '-';

                    if (pricingMethod === PricingMethodType.TIER) {
                        return <Typography>{priceTierLabel}</Typography>;
                    }
                }

                const variants = Array.isArray(row.variants) ? row.variants : [row.variants];
                const basePriceRange = getBasePriceLabel(variants);
                return <Typography>{basePriceRange}</Typography>;
            },
        },
        {
            renderHeader: () => ProductControlTableHeader({ name: 'Last Update' }),
            field: 'lastUpdated',
            flex: 1.25,
            renderCell: ({ row }) => (
                <Typography>{dayjs.utc(row.lastUpdated).local().format('ddd, MMM D, H:mm A').toString()}</Typography>
            ),
        },
    ];

    return columns;
}
