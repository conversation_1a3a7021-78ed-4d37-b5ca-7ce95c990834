import React, { useState } from 'react';
import { Box, Typography } from '@mui/material';
import useLoadData from '../../../hooks/useLoadData';
import attributesApiKeyStore from '../../../api/attributesApiKeyStore';
import LoadingIndicator from '../../../components/LoadingIndicator';
import { AttributeCategoryData } from '../../../interfaces/dto/attributeCategoryData';
import ProductAttributeForm from './ProductAttibuteForm';
import SectionFieldLabel from '../../../components/stepper/SectionFieldLabel';
import useProduct from '../Hooks/useProduct';
import { ProductFormSectionBox } from '../../../styles/globalStyles';

const AttributesSection = () => {
    const { product } = useProduct();
    const [productAttributes] = useState(product.productAttributes ?? []);

    const { isLoading, isError, data } = useLoadData<AttributeCategoryData[]>({
        queryConfig: attributesApiKeyStore.getAttributeCategories(),
    });

    return (
        <ProductFormSectionBox>
            <Box>
                <Typography variant="h6">Attributes</Typography>
            </Box>
            <Box sx={[{ marginBottom: '1rem' }]}>
                <SectionFieldLabel>
                    Attributes help you track and tag products. You can show them on your retail labels and eCommerce
                    menu, and use them to create discounts and reports. Internal Tags are only visible to employees.
                </SectionFieldLabel>
            </Box>
            <LoadingIndicator isLoading={isLoading} />
            {isError && <>Data loading failed</>}
            {data && (
                <ProductAttributeForm
                    attributeCategories={data}
                    productAttributes={productAttributes ?? []}
                    productId={product.id ?? ''}
                    verifiedReferenceId={product.verifiedReferenceId}
                />
            )}
        </ProductFormSectionBox>
    );
};

export default AttributesSection;
