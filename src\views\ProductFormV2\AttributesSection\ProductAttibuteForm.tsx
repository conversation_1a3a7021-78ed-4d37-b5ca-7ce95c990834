import React from 'react';
import { Grid } from '@mui/material/';
import { Controller } from 'react-hook-form';
import useReactHookForm from '../../../hooks/useReactHookForm';
import useProduct from '../Hooks/useProduct';
import CatalogPermissions from '../../../permissions/catalogPermissions';
import { AttributeCategoryData } from '../../../interfaces/dto/attributeCategoryData';
import MultiSelectWithSearch from '../../../components/MultiSelectWithSearch';
import useCreateAttributeMutation from '../../../mutations/attribute/useCreateAttributeMutation';
import { MenuItemsProps } from '../../../interfaces/ProductProps';
import useSaveData from '../../../hooks/useSaveData';
import productAttributeApiKeyStore from '../../../api/productAttributeApiKeyStore';
import { ProductAttributeData } from '../../../interfaces/dto/productAttributeData';
import HookFormErrorBox from '../../../components/hook-form-v2/HookFormErrorBox';
import { isEmpty } from '../../../utils/common';
import ReactHookProductTabForm from '../ReactHookProductTabForm';
import { SubmitCallBack } from '../Types/SubmitFormTypes';
import filterMutationErrors from '../../../utils/MutationResponseUtil';

interface AttributeFormData {
    [key: string]: MenuItemsProps[];
}

const getAtributeCategoryMenuItems = (attitbuteCategory: AttributeCategoryData): MenuItemsProps[] =>
    attitbuteCategory?.attributes?.map((a) => ({ displayValue: a.id, displayName: a.name }));

const getMenuItemData = (productAtt: ProductAttributeData) => ({
    displayValue: productAtt.attribute.id,
    displayName: productAtt.attribute.name,
});

const getDefaultFormData = (
    attributeCategories: AttributeCategoryData[],
    productAttributes?: ProductAttributeData[],
): AttributeFormData => {
    const defaultValue = attributeCategories.reduce(
        (acc, ac) => ({
            ...acc,
            [ac.name]: [],
        }),
        {},
    );

    return productAttributes?.reduce((acc: any, current: ProductAttributeData) => {
        if (acc[current.attribute.attributeCategory.name]) {
            acc[current.attribute.attributeCategory.name].push(getMenuItemData(current));
        } else {
            acc[current.attribute.attributeCategory.name] = [getMenuItemData(current)];
        }
        return acc;
    }, defaultValue);
};

const buildLatestProductAttributeDataFromFormValues = (
    attributeCategories: AttributeCategoryData[],
    values: any,
    productId: string,
    productAttributes?: ProductAttributeData[],
) =>
    attributeCategories.reduce((acc: ProductAttributeData[], category: AttributeCategoryData) => {
        const attributes = values[category.name]?.map((i: MenuItemsProps) => {
            const productAttributeId = productAttributes?.find((pa) => pa.attributeId === i.displayValue);
            const productAtt: ProductAttributeData = {
                id: productAttributeId?.id,
                attributeId: i.displayValue.toString(),
                productId,
                attribute: {
                    id: i.displayValue.toString(),
                    name: i.displayName,
                    attributeCategory: category,
                },
                createdAt: new Date(),
            };

            return productAtt;
        });

        return attributes ? [...acc, ...attributes] : acc;
    }, []);

interface ProductAttributeFormProps {
    productId: string;
    verifiedReferenceId?: string;
    attributeCategories: AttributeCategoryData[];
    productAttributes: ProductAttributeData[];
}

const ProductAttributeForm = ({
    attributeCategories,
    productAttributes,
    productId,
    verifiedReferenceId,
}: ProductAttributeFormProps) => {
    const { setProductUpdate } = useProduct();
    const createAttributeMutation = useCreateAttributeMutation();
    const form = useReactHookForm({
        defaultValues: getDefaultFormData(attributeCategories, productAttributes),
    });

    const { isLoading: isSaving, mutateAsync } = useSaveData<ProductAttributeData[]>({
        mutationConfig: productAttributeApiKeyStore.saveProductAttributes(),
    });

    const getDeleteErrorData = (failedData: any, deletedIds: string[]): { entity: any; error: string }[] => {
        if (isEmpty(failedData?.error)) {
            return [];
        }

        const { error } = failedData;

        if (error.name === 'AxiosError' && !error.response) {
            return deletedIds.map((id) => ({ entity: { id }, error: 'Test' }));
        }

        const getErrorData = () => {
            if (!isEmpty(error.response?.data?.failed)) {
                return error.response.data.failed;
            }
            if (!isEmpty(error)) {
                return error;
            }
            return undefined;
        };

        const deletedItemsFailed: any[] = getErrorData();
        if (!isEmpty(deletedItemsFailed)) {
            return deletedItemsFailed.map((d) => ({ entity: d.entity, error: 'Test' }));
        }

        return [];
    };

    const setFormApiErrors = (response: any, deletedIds: any[], latestAttributes: ProductAttributeData[]) => {
        const failedData = getDeleteErrorData(response, deletedIds);
        attributeCategories?.forEach((category) => {
            const categoryProductAtts = latestAttributes?.filter(
                (p) => p.attribute.attributeCategory.id === category.id,
            );

            const failedItems = categoryProductAtts?.filter(
                (p) => !p.id || failedData.some((f) => f.entity.id === p.id),
            );
            if (!isEmpty(failedItems)) {
                form.setError(category.name, {
                    message: `Failed to add/delete - ${failedItems.map((p) => p.attribute.name).join(', ')}`,
                });
            }
        });
    };

    const updateNewlyCreatedIds = (
        createData: ProductAttributeData[],
        curretProductAttribute: ProductAttributeData[],
    ) => {
        if (!isEmpty(createData)) {
            return curretProductAttribute.map((pa) => {
                if (!pa.id) {
                    const added = createData.find((d) => d.attributeId === pa.attributeId);
                    const newItem = {
                        ...pa,
                        id: added?.id,
                    };
                    return newItem;
                }
                return pa;
            });
        }

        return curretProductAttribute;
    };

    const addDeleteFailureBack = (
        deletedData: any,
        deletedIds: any[],
        curretProductAttribute: ProductAttributeData[],
    ) => {
        const failedData = getDeleteErrorData(deletedData, deletedIds);
        const deleteFailedPA: ProductAttributeData[] | undefined = productAttributes?.filter((pa) =>
            failedData.some((f) => pa.id === f.entity.id),
        );

        if (!isEmpty(deleteFailedPA)) {
            return [...curretProductAttribute, ...(deleteFailedPA || [])];
        }

        return [...curretProductAttribute];
    };

    const findEntityTitle = (entity: any) => {
        if (entity?.id) {
            const productAtt = productAttributes.find((pa) => pa.id === entity.id);
            return `${productAtt?.attribute.attributeCategory.name} / ${productAtt?.attribute.name}`;
        }

        return undefined;
    };

    const handleSubmitWithCallBack = (callBack?: SubmitCallBack) => async (values: any) => {
        const formProductAttributes = buildLatestProductAttributeDataFromFormValues(
            attributeCategories,
            values,
            productId,
            productAttributes,
        );
        const createData = formProductAttributes.filter((pa) => pa.id === undefined);

        const deleteIds = productAttributes
            ?.filter(
                (pa: ProductAttributeData) =>
                    !formProductAttributes.some((la) => pa.id && la.attributeId === pa.attributeId),
            )
            .map((pa) => pa.id)
            .filter(Boolean);

        const mutationData = {
            createData: !isEmpty(createData) ? createData : undefined,
            updateData: undefined,
            ...(!isEmpty(deleteIds) && {
                deleteData: {
                    ids: [...deleteIds],
                },
            }),
        };

        // Return - No data available to save
        if (!mutationData.createData && !mutationData.updateData && !mutationData.deleteData) {
            callBack?.();
            return;
        }

        const result = await mutateAsync(mutationData);

        let updatedProductAttributes: ProductAttributeData[] = [...formProductAttributes];

        if (!isEmpty(result.createData?.data) && result.createData?.data) {
            updatedProductAttributes = updateNewlyCreatedIds(result.createData?.data, formProductAttributes);
        }

        // To set the updated ids to form
        form.reset(getDefaultFormData(attributeCategories, updatedProductAttributes), {
            keepDefaultValues: true,
            keepDirty: true,
            keepErrors: true,
        });

        if (!isEmpty(deleteIds) && !isEmpty(result.deleteData?.error)) {
            updatedProductAttributes = addDeleteFailureBack(result.deleteData, deleteIds, updatedProductAttributes);
        }
        if (!isEmpty(deleteIds) && !isEmpty(result.deleteData?.data)) {
            result.deleteData?.data?.forEach((d) => {
                const index = productAttributes.findIndex((pa) => pa.id === d.id);
                productAttributes.splice(index, 1);
            });
        }
        setFormApiErrors(result.deleteData, deleteIds, updatedProductAttributes);

        setProductUpdate({ productAttributes: updatedProductAttributes.filter((a) => !!a.id) });

        if (callBack) {
            callBack({ errors: filterMutationErrors(result, findEntityTitle) });
        }
    };

    const disableAttributeForGlobalProduct = (attributeCategoryName: string) =>
        !!verifiedReferenceId && attributeCategoryName !== 'Internal Tags';

    return (
        <ReactHookProductTabForm
            formName="ProductAttributeForm"
            onSubmit={handleSubmitWithCallBack}
            formContextProps={form}
            isBusy={isSaving}
        >
            <Grid container spacing={2}>
                {attributeCategories?.map((item: AttributeCategoryData) => (
                    <Grid key={item.id} item xs={12} data-testid="product-attribute-category">
                        <Controller
                            key={item.id}
                            name={item.name}
                            control={form.control}
                            render={({ field: { onChange, onBlur, value, ref } }) => (
                                <MultiSelectWithSearch
                                    onChange={onChange}
                                    onBlur={onBlur}
                                    value={value || []}
                                    ref={ref}
                                    isDisabled={isSaving || disableAttributeForGlobalProduct(item.name)}
                                    dataForNewOption={{
                                        attributeCategoryId: item?.id,
                                    }}
                                    menuItems={getAtributeCategoryMenuItems(item) || []}
                                    label={item.name}
                                    mutation={createAttributeMutation}
                                    createPermissions={[
                                        CatalogPermissions.ADJUST_PRODUCT,
                                        CatalogPermissions.ADJUST_ATTRIBUTE,
                                    ]}
                                    options={{ createNewOption: true }}
                                />
                            )}
                        />
                        <HookFormErrorBox name={item.name} />
                    </Grid>
                ))}
            </Grid>
        </ReactHookProductTabForm>
    );
};

export default ProductAttributeForm;
