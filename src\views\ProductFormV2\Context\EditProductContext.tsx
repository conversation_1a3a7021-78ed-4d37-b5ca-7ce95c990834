import React, { ReactNode, createContext, useMemo, useState } from 'react';
import { produce } from 'immer';
import { ProductData } from '../../../interfaces/dto/productData';
import { EntityPriceDto } from '../../../interfaces/dto/entityPrice';
import Mode from '../Types/Mode';
import PRODUCT_CATEGORIES from '../../../utils/product-categories-data';
import { BulkProductSubCategories } from '../../../interfaces/dto/productSubCategory';

type EditContextType = {
    product: ProductData;
    entityPrices: EntityPriceDto[];
    mode: Mode;
    setProductUpdate: (p: Partial<ProductData>) => void;
    setEntityPriceUpdate: (ep: EntityPriceDto[]) => void;
    removeEntityPriceById: (ids?: string | string[]) => void;
    isBulkProduct: () => boolean;
    isBulkProductWithSingleSku: () => boolean;
    isBulkProductWithMultipleSku: () => boolean;
    isBulkSubCategory: (productSubCategoryName: string) => boolean;
    findEntityPriceByOrgEntAndSkuId: (storeId: string, skuId: string) => EntityPriceDto | null;
};

interface EditContextProviderProps {
    children: ReactNode;
    mode: Mode;
}

const EditProductContext = createContext<EditContextType>({
    product: { name: '' },
    entityPrices: [
        {
            id: '',
            variantId: '',
            type: '',
            price: 0,
            organizationEntityId: '',
        },
    ],
    mode: Mode.CREATE,
    setProductUpdate: () => {},
    setEntityPriceUpdate: () => {},
    removeEntityPriceById: () => {},
    isBulkProduct: () => false,
    isBulkProductWithSingleSku: () => false,
    isBulkProductWithMultipleSku: () => false,
    isBulkSubCategory: (productSubCategoryName: string) => !!productSubCategoryName,
    findEntityPriceByOrgEntAndSkuId: () => null,
});

const EditProductContextProvider = ({ children, mode }: EditContextProviderProps) => {
    const [entityPrices, setEntityPrices] = useState<EntityPriceDto[]>([]);
    const [product, setProduct] = useState<ProductData>({ name: '' });

    const setProductUpdate = (p: Partial<ProductData>) => {
        setProduct((current) =>
            produce(current, (draft) => {
                Object.assign(draft, p);
            }),
        );
    };

    const setEntityPriceUpdate = (ep: EntityPriceDto[]) => {
        setEntityPrices((prev) => {
            const entityMap = new Map(prev.map((epItem) => [epItem.id, epItem]));
            ep.forEach((epItem) => entityMap.set(epItem.id, epItem));
            return Array.from(entityMap.values());
        });
    };

    const removeEntityPriceById = (ids?: string | string[]) => {
        setEntityPrices((prev) => {
            const idsArray = Array.isArray(ids) ? ids : [ids];
            return prev.filter((ep) => !idsArray.includes(ep.id));
        });
    };

    const isBulkSubCategory = (productSubCategoryName: string): boolean =>
        Object.values(BulkProductSubCategories).includes(productSubCategoryName as BulkProductSubCategories);

    const isBulkProduct = (): boolean => {
        const { productSubCategory, productCategory } = product;
        const productCategoryName = productCategory?.name || '';
        const productSubCategoryName: string = productSubCategory?.name || '';

        switch (productCategoryName) {
            case PRODUCT_CATEGORIES.Flower:
                return (
                    productSubCategoryName === BulkProductSubCategories.BULK_FLOWER ||
                    productSubCategoryName === BulkProductSubCategories.SHAKE ||
                    productSubCategoryName === BulkProductSubCategories.STRAIN_SPECIFIC_SHAKE
                );
            case PRODUCT_CATEGORIES.Extract:
                return productSubCategoryName === BulkProductSubCategories.BULK_EXTRACT;
            default:
                return false;
        }
    };

    const isBulkProductWithSingleSku = (): boolean => {
        const { variants } = product;
        return isBulkProduct() && (variants?.length === undefined || variants?.length <= 1);
    };
    const isBulkProductWithMultipleSku = (): boolean => {
        const { variants } = product;
        return isBulkProduct() && variants?.length !== undefined && variants?.length > 1;
    };

    const findEntityPriceByOrgEntAndSkuId = (storeId?: string, variantId?: string) =>
        entityPrices.find((entity) => entity.organizationEntityId === storeId && entity.variantId === variantId) ||
        null;

    const value = useMemo(
        () => ({
            product,
            entityPrices,
            mode,
            setProductUpdate,
            setEntityPriceUpdate,
            removeEntityPriceById,
            isBulkProduct,
            isBulkProductWithSingleSku,
            isBulkProductWithMultipleSku,
            isBulkSubCategory,
            findEntityPriceByOrgEntAndSkuId,
        }),
        [product, entityPrices],
    );

    return <EditProductContext.Provider value={value}>{children}</EditProductContext.Provider>;
};

export { EditProductContext, EditProductContextProvider };
