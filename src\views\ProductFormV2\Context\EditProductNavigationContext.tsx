/* eslint-disable @typescript-eslint/lines-between-class-members */
// eslint-disable-next-line max-classes-per-file
import React, { ReactNode, createContext, useEffect, useMemo, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { TabInfo } from '../Types/TabInfo';
import getAllTabs from '../ProductTabConfig';
import TabName from '../Types/TabNames.enum';
import useProduct from '../Hooks/useProduct';
import { PageName, TabNavigation } from '../Types/Navigation.enum';
import { BaseNavigationOption, PageNavigationOption, TabNavigationOption } from '../Types/NavigationOption';
import { BASE_PATH, PRICING_PAGE_URL } from '../../../utils/constants';

type NavigationContextType = {
    tabs: TabInfo[];
    isTabDirty: boolean;
    confirmNavigation?: BaseNavigationOption;
    setTabDirty: (isTabDirty: boolean) => void;
    isMissingPrices: boolean;
    setMissingPrices: (isMissing: boolean) => void;
    isIncompleteDialogOpen: boolean;
    setIncompleteDialogOpen: (open: boolean) => void;
    navigate: (options: BaseNavigationOption) => any;
};

const EditProductTabNavigationContext = createContext<NavigationContextType>({
    tabs: [],
    isTabDirty: false,
    setTabDirty: () => {},
    isMissingPrices: false,
    setMissingPrices: () => {},
    isIncompleteDialogOpen: false,
    setIncompleteDialogOpen: () => {},
    navigate: () => {},
});

interface EditProductTabNavigationContextProviderProps {
    children: ReactNode;
}

const EditProductNavigationContextProvider = ({ children }: EditProductTabNavigationContextProviderProps) => {
    const pageNavigate = useNavigate();
    const [, setSearchParams] = useSearchParams();
    const { product } = useProduct();
    const [isTabDirty, setIsTabDirty] = useState<boolean>(false);
    const [confirmNavigation, setConfirmNavigation] = useState<BaseNavigationOption>();
    const [tabs, setTabs] = useState<TabInfo[]>(getAllTabs(TabName.PRODUCT_INFO));
    const [isMissingPrices, setIsMissingPrices] = useState(false);
    const [isIncompleteDialogOpen, setIsIncompleteDialogOpen] = useState(false);

    useEffect(() => {
        if (product && product.id) {
            const updatedTabs = tabs.map((t) => ({ ...t, isEnabled: true }));
            setTabs(updatedTabs);
        }
    }, [product]);

    const setTabDirty = (dirty: boolean) => {
        setIsTabDirty(dirty);
    };

    const setMissingPrices = (value: boolean) => {
        setIsMissingPrices(value);
    };

    const setIncompleteDialogOpen = (open: boolean) => {
        setIsIncompleteDialogOpen(open);
    };

    const hasNext = () => {
        const selectedIndex = tabs.findIndex((t) => t.isSelected);
        return selectedIndex < tabs.length - 1;
    };

    const hasPrev = () => {
        const selectedIndex = tabs.findIndex((t) => t.isSelected);
        return selectedIndex > 0;
    };

    const goTo = (name: TabName) => {
        setConfirmNavigation(undefined);
        setTabDirty(false);
        setTabs(getAllTabs(name));
        setSearchParams({ tab: name });
    };

    const navigateToOutSidePage = (options: PageNavigationOption) => {
        const { pageName, productdata } = options;

        if (pageName === PageName.PRICING_PAGE) {
            const latestProductData = productdata ?? product;
            pageNavigate(`${PRICING_PAGE_URL}?search=${latestProductData.name}`);
        } else if (pageName === PageName.PRODUCT_CONTROL_PAGE) {
            pageNavigate(BASE_PATH, { replace: true });
        }
    };

    const navigateToTab = (options: TabNavigationOption) => {
        const { navigation, tabName } = options;
        if (navigation === TabNavigation.SELECT && tabName) {
            goTo(tabName);
        } else if (navigation === TabNavigation.NEXT && hasNext()) {
            const selectedIndex = tabs.findIndex((t) => t.isSelected);
            const nextTab = tabs[selectedIndex + 1];
            goTo(nextTab.tab);
        } else if (navigation === TabNavigation.PREV && hasPrev()) {
            const selectedIndex = tabs.findIndex((t) => t.isSelected);
            const prevTab = tabs[selectedIndex - 1];
            goTo(prevTab.tab);
        }
    };

    const navigate = (options: BaseNavigationOption) => {
        const { type, force } = options;
        if (type === 'Ignore') {
            setConfirmNavigation(undefined);
        } else if (force || !isTabDirty) {
            if (type === 'Page') {
                navigateToOutSidePage(options as PageNavigationOption);
            } else if (type === 'Tab') {
                navigateToTab(options as TabNavigationOption);
            }
        } else if (isTabDirty) {
            setConfirmNavigation({ ...options });
        }
    };

    const value = useMemo(
        () => ({
            tabs,
            confirmNavigation,
            isTabDirty,
            setTabDirty,
            isMissingPrices,
            setMissingPrices,
            isIncompleteDialogOpen,
            setIncompleteDialogOpen,
            navigate,
        }),
        [tabs, confirmNavigation, isTabDirty],
    );

    return (
        <EditProductTabNavigationContext.Provider value={value}>{children}</EditProductTabNavigationContext.Provider>
    );
};

export { EditProductTabNavigationContext, EditProductNavigationContextProvider };
