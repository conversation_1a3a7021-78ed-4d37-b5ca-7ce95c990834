import React from 'react';
import { Box, styled, Typography } from '@mui/material';
import { ProductFormSectionBox } from '../../../styles/globalStyles';
import PricingForm from './PricingForm';

const SectionHeaderContainer = styled(Box)(() => ({
    display: 'flex',
    flexDirection: 'column',
    rowGap: '1em',
}));

const DefaultPricingSection = () => (
    <ProductFormSectionBox>
        <SectionHeaderContainer data-testid="product-form-section-header">
            <Typography variant="h6">Pricing</Typography>
            <PricingForm />
        </SectionHeaderContainer>
    </ProductFormSectionBox>
);

export default DefaultPricingSection;
