import Joi from 'joi';
import { REQUIRED_PRICE_ERROR, STRING_PRICE_ZERO } from '../../../utils/priceUtils';
import PRODUCT_CATEGORIES from '../../../utils/product-categories-data';

interface PricingFormSchemaOptions {
    productCategoryName?: string;
}

const pricingFormSchema = ({ productCategoryName }: PricingFormSchemaOptions) => {
    const base =
        productCategoryName === PRODUCT_CATEGORIES.NonInv
            ? Joi.any().not(null)
            : Joi.any().not('0', 0, STRING_PRICE_ZERO).messages({
                  'any.invalid': REQUIRED_PRICE_ERROR,
              });

    return Joi.object({
        pricingMethod: Joi.string().required(),
        priceTierId: Joi.when('pricingMethod', {
            is: 'TIER',
            then: Joi.string().required().messages({
                'string.base': 'Product Price tier is required if pricing method is Tier Pricing.',
            }),
            otherwise: Joi.string().allow(null, '').optional(),
        }),
        defaultPrices: Joi.array()
            .items(
                Joi.object({
                    base,
                }),
            )
            .required(),
        stores: Joi.array()
            .items(
                Joi.object({
                    id: Joi.string().optional(),
                    skus: Joi.array()
                        .items(
                            Joi.object({
                                id: Joi.string().optional(),
                                defaultPrices: Joi.object({
                                    base,
                                }).required(),
                                amount: Joi.number().required().allow(null),
                                unitCount: Joi.number().required(),
                                merchandiseSize: Joi.string().required().allow(null),
                                uom: Joi.string().required().allow(null),
                                priceTierId: Joi.when('pricingMethod', {
                                    is: 'TIER',
                                    then: Joi.string().not(null, '').required(),
                                    otherwise: Joi.string().allow(null, '').optional(),
                                }),
                                isCustomPrice: Joi.boolean().optional(),
                            }),
                        )
                        .required(),
                }),
            )
            .required(),
    });
};
export default pricingFormSchema;
