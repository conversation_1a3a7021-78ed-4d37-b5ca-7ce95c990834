import React, { useEffect, useState } from 'react';
import {
    Box,
    Grid,
    TableCell,
    TableRow,
    TableBody,
    Table,
    TableHead,
    useTheme,
    Typography,
    styled,
} from '@mui/material';
import { useWatch } from 'react-hook-form';
import { Button } from '@treez-inc/component-library';
import { BASE_PRICING_METHODS, PricingMethodType } from '../../../utils/constants';
import ReactHookProductTabForm from '../ReactHookProductTabForm';
import { useOrganizationEntityInfoContext } from '../../../hooks/organization-entity/useOrganizationEntityInfoContext';
import SKUSelector, { SKU } from './SKUSelector';
import useReactHookForm from '../../../hooks/useReactHookForm';
import PricingFormSchema from './PricingForm.schema';
import { SkuApiDto, SkuDto } from '../../../interfaces/dto/sku';
import useDeleteEntityPriceMutation from '../../../mutations/entityPrice/useDeleteEntityPriceMutation';
import useCreateEntityPriceMutation from '../../../mutations/entityPrice/useCreateEntityPriceMutation';
import { PriceTier, PricingFormData } from '../Types/PricingTypes';
import { EntityPriceDto } from '../../../interfaces/dto/entityPrice';
import useProduct from '../Hooks/useProduct';
import useListPriceTiers from '../../../queries/useListPriceTiers';
import useUpdateEntityPriceMutation from '../../../mutations/entityPrice/useUpdateEntityPriceMutation';
import useUpdateSkuMutation from '../../../mutations/sku/useUpdateSkuMutation';
import { centsToDollars, dollarsToCents, formatPriceInput } from '../../../utils/priceUtils';
import HookFormSelect from '../../../components/hook-form-v2/HookFormSelect';
import useSaveData from '../../../hooks/useSaveData';
import { ProductData } from '../../../interfaces/dto/productData';
import productApiKeyStore from '../../../api/productApiKeyStore';
import HookFormCustomPrice from '../../../components/hook-form-v2/HookFormCustomPrice';
import { StyledSubHeader } from '../../../styles/globalStyles';
import FeatureFlag from '../../../interfaces/featureFlag';
import useFeatureFlag from '../../../hooks/useFeatureFlag';
import { currencyFormatter } from '../../../hooks/currencyConverter';
import PRODUCT_CATEGORIES from '../../../utils/product-categories-data';
import { filterForBaseSkus } from '../../../utils/productUtils';
import { CallbackProps } from '../Types/SubmitFormTypes';
import { MutationErrorData } from '../../../utils/MutationResponseUtil';

const BulkProductBox = styled(Box)(({ theme }) => ({
    backgroundColor: theme.palette.info.light,
    borderRadius: `0.9375rem`,
    color: `rgba(89, 89, 89, 1)`,
    fontSize: `0.9375rem`,
    fontStyle: 'normal',
    lineHeight: `1.4375rem`,
    padding: `1rem 1.25rem`,
    margin: `0.75rem 0 0.5rem 0`,
}));

interface PricingFormProps {
    hasSaveCloseButtons?: boolean;
    usableSkusParent?: SkuDto[];
    enableNavigation?: boolean;
}

export default function PricingForm({
    hasSaveCloseButtons,
    usableSkusParent,
    enableNavigation = true,
}: PricingFormProps) {
    const [skuIdx, setSKUIndex] = useState(0);
    const isDeliEnabled = useFeatureFlag(FeatureFlag.DELI_ENABLED);

    const [previousSKUIndex, setPreviousSKUIndex] = useState<number | null>(null);

    const transformCurrencyToDollars = (v: string | number | null | undefined) => {
        if ([null, undefined, ''].includes(v as null | undefined)) {
            return null;
        }

        return currencyFormatter.format(Number(centsToDollars(v as string | number)));
    };

    const stringPriceToFixedDecimals = (s: string | null | undefined) => {
        if (s === null || s === undefined) {
            return null;
        }
        return Number(s).toFixed(2);
    };

    const [missingPrices, setMissingPrices] = useState(false);
    const [priceDirty, setPriceDirty] = useState(false);

    const [pendingEntityPriceDeletionIds, setPendingEntityPriceDeletionIds] = useState<Set<string>>(new Set());

    const {
        product,
        setProductUpdate,
        isBulkProduct,
        isBulkProductWithMultipleSku,
        isBulkProductWithSingleSku,
        findEntityPriceByOrgEntAndSkuId,
        entityPrices,
        setEntityPriceUpdate,
        removeEntityPriceById,
    } = useProduct();

    const productCategoryName = product.productCategory?.name || '';
    const productSubCategoryName = product.productSubCategory?.name || '';

    // this comma hack is necessary because we are using ts < 4.4.0
    const updateNestedMap = <T,>(prev: Map<string, Map<string, T>>, storeId: string, skuId: string, value: T) => {
        const newMap = new Map(prev);
        const storeMap = newMap.get(storeId) || new Map();
        storeMap.set(skuId, value);
        newMap.set(storeId, storeMap);
        return newMap;
    };

    // this is a Map to call and compare pricingMethod (which can be changed using dropdown)
    // with product.pricingMethod allowing to flip input/select inside hookFormCustomPrice
    // it has this structure:
    /**
     * storeId : {
     *  skuId: PricingMethodType | undefined
     * }
     */
    const [pricingMethodOverrideMap, setPricingMethodOverrideMap] = useState<
        Map<string, Map<string, PricingMethodType | undefined>>
    >(new Map());

    // util function to make easier to use
    const updatePricingMethodOverrideMap = (storeId: string, skuId: string, method: PricingMethodType | undefined) => {
        setPricingMethodOverrideMap((prev) => updateNestedMap(prev, storeId, skuId, method));
    };

    // this is an override that follows the same principle as
    // above state pricingMethodOverrideMap, but instead of toggling
    // the method, it only brute forces the custom price to be shown
    // bellow a possible custom price which will be flagged by this function
    // it has this structure:
    /**
     * storeId: {
     *  skuId: boolean;
     * }
     */
    const [forceHiddenCustomPrice, setForceHiddenCustomPrice] = useState<Map<string, Map<string, boolean>>>(new Map());

    // util function to make easier to use
    const toggleHiddenCustomPrice = (storeId: string, skuId: string, bool = false) => {
        setForceHiddenCustomPrice((prev) => updateNestedMap(prev, storeId, skuId, bool));
    };

    const { stores } = useOrganizationEntityInfoContext();
    const theme = useTheme();

    // remove promo and sample
    const usableSkus = usableSkusParent || filterForBaseSkus(product?.variants!);

    const currentSkuId = usableSkus?.[skuIdx]?.id as string; // This is a derived state;

    const { data: priceTiers, isLoading: isPriceTiersLoading } = useListPriceTiers({ isBulkProduct: isBulkProduct() });

    const { mutateAsync: deleteEntityPriceMutation, isLoading: isEntityPriceDeleteLoading } =
        useDeleteEntityPriceMutation();

    const { mutateAsync: createEntityPriceMutation, isLoading: isEntityPriceCreateLoading } =
        useCreateEntityPriceMutation();

    const { mutateAsync: updateEntityPriceMutation, isLoading: isEntityPriceUpdateLoading } =
        useUpdateEntityPriceMutation();

    const { mutateAsync: updateSkuMutation, isLoading: isUpdateSkuMutationLoading } = useUpdateSkuMutation();

    const { isLoading: isProductLoading, mutateAsync: mutateProductAsync } = useSaveData<ProductData[]>({
        mutationConfig: productApiKeyStore.saveProductDetails(),
    });

    const isLoading =
        isPriceTiersLoading ||
        isEntityPriceDeleteLoading ||
        isEntityPriceCreateLoading ||
        isEntityPriceUpdateLoading ||
        isUpdateSkuMutationLoading ||
        isProductLoading;

    const SKU_NAMES_ARRAY = usableSkus
        ?.map((variant) => ({
            id: variant.id || '',
            name: variant.label,
        }))
        .filter(Boolean) as SKU[];

    const generateFormValues = () => ({
        pricingMethod: product.pricingMethod ?? PricingMethodType.FLAT,
        priceTierId: product.priceTierId ?? null,
        defaultPrices: usableSkus?.map((variant: SkuDto) => ({
            base:
                variant.defaultPrices?.base !== undefined
                    ? transformCurrencyToDollars(variant.defaultPrices.base)
                    : null,
        })),

        stores: stores?.map((store) => ({
            id: store?.id,
            skus: usableSkus?.map((sku: SkuDto) => {
                const { id, unitCount, uom, amount, merchandiseSize } = sku;

                const priceFromVariant =
                    sku.defaultPrices?.base !== undefined ? transformCurrencyToDollars(sku.defaultPrices.base) : null;

                const defaultPrices = {
                    base: priceFromVariant,
                };

                const existingEntity = findEntityPriceByOrgEntAndSkuId(store.id, sku.id!);

                return {
                    id,
                    unitCount,
                    uom,
                    amount,
                    merchandiseSize,
                    defaultPrices,
                    priceTierId: existingEntity?.pricingMethod === 'TIER' ? product.priceTierId : null,
                    isCustomPrice: Boolean(existingEntity),
                };
            }),
        })),
    });

    const form = useReactHookForm<PricingFormData>({
        joiSchema: PricingFormSchema({ productCategoryName }),
        defaultValues: generateFormValues(),
    });
    const { control } = form;

    const { stores: storeValues } = form.getValues() as PricingFormData;

    const pricingMethod = useWatch({ control, name: 'pricingMethod' });
    const priceTierId = useWatch({ control, name: 'priceTierId' });

    const skuHasEntityPrice = (storeId?: string, variantId?: string) => {
        const fromForm = form.getValues() as PricingFormData;
        const currentStore = fromForm.stores.find((store) => store.id === storeId);
        const existingEntityPrice = findEntityPriceByOrgEntAndSkuId(storeId!, variantId!);
        const skuHasCustomPrice = currentStore?.skus?.find((sku) => sku.id === variantId && sku.isCustomPrice);

        if (pricingMethod) {
            return Boolean(existingEntityPrice?.price || existingEntityPrice?.priceTierId) || skuHasCustomPrice;
        }

        return false;
    };

    const skuHasPriceTier = (storeId?: string, variantId?: string) =>
        Boolean(findEntityPriceByOrgEntAndSkuId(storeId!, variantId!)?.priceTierId);

    // handles update or create, use optimistic ui to update the custom price / reset
    const handleEntityPriceMutation = async (entities: EntityPriceDto[]) => {
        const createEntities: EntityPriceDto[] = [];
        const updateEntities: Partial<EntityPriceDto>[] = [];

        try {
            entities.forEach((entity: EntityPriceDto) => {
                // this has to be adressed later
                // because if you reset then type a custom price
                // instead of updating you would be deleting and
                // creating a new entity, which can be costy
                if (entity.id && !pendingEntityPriceDeletionIds.has(entity.id)) {
                    updateEntities.push(entity);
                } else {
                    const { id, ...entityWithoutId } = entity;
                    createEntities.push(entityWithoutId);
                }
            });

            // optimistic with response
            const updateResponse =
                updateEntities.length > 0 ? await updateEntityPriceMutation(updateEntities) : { data: [] };
            const createResponse =
                createEntities.length > 0 ? await createEntityPriceMutation(createEntities) : { data: [] };

            setEntityPriceUpdate([...createResponse.data, ...updateResponse.data]);
        } catch (error) {
            // optimistic rollback
            setEntityPriceUpdate(entityPrices.filter((p) => entities.some((ep: EntityPriceDto) => p.id !== ep.id)));
        }
    };

    const queueEntityPriceForDeletion = (entityId: string) => {
        setPendingEntityPriceDeletionIds((prev) => new Set(prev).add(entityId));
    };

    const entityPricesDeleteMutation = async () => {
        try {
            const entityIdsToDelete = Array.from(pendingEntityPriceDeletionIds);
            if (entityIdsToDelete.length > 0) {
                await deleteEntityPriceMutation(entityIdsToDelete);
                entityIdsToDelete.forEach((id) => removeEntityPriceById(id));
                setPendingEntityPriceDeletionIds(new Set());
                setPricingMethodOverrideMap(new Map());
            }
        } catch (error) {
            // eslint-disable-next-line no-console
            console.warn('Failed to process entity price deletions:', error);
        }
    };

    const handleProductSubmit = async (formPriceTierId: string | null = null) => {
        const { pricingMethod: productPricingMethod, priceTierId: productPriceTierId } = product;

        if (productPricingMethod !== pricingMethod || formPriceTierId !== productPriceTierId) {
            const priceTierData = {
                pricingMethod,
                priceTierId: pricingMethod === PricingMethodType.FLAT ? null : formPriceTierId,
            };

            // optimistic update
            setProductUpdate({
                ...priceTierData,
            });

            await mutateProductAsync({
                updateData: [
                    {
                        id: product.id,
                        ...priceTierData,
                    },
                ],
            });
        }
    };

    // both functions bellow needs to be merged, while you are in one
    // or the other pricing method, you're able to set flat or tier depending
    // if you use custom flat price or not.
    const handleFlatPriceSubmit = async (data: PricingFormData) => {
        const skuUpdates: Partial<SkuApiDto>[] = [];
        const entityPriceUpdates: EntityPriceDto[] = [];

        // transforming the form values into a store
        // with base prices as an array containing base for each sku
        // e.g. base: [15, 20, 25] = (1mg, 10mg, 20mg)
        const priceFormStores = data.stores.map((store) => ({
            ...store,
            base: data.defaultPrices.map((price) => price.base && dollarsToCents(price.base)),
        }));

        priceFormStores.forEach(async (store) => {
            store.skus.forEach((sku, idx) => {
                // these are the values from the form
                const storeBasePrice = store.base[idx];
                const storeSkuBasePrice = sku.defaultPrices.base && dollarsToCents(sku.defaultPrices.base);
                const formattedPrice = storeSkuBasePrice === '' ? null : storeSkuBasePrice;
                const selectedPriceTierId = sku.priceTierId;
                const { isCustomPrice } = sku;
                const type = 'base';

                // this is the value from useProducts (already in db)
                const savedSkuPrice = usableSkus?.[idx]?.defaultPrices?.base;
                const existingEntity = findEntityPriceByOrgEntAndSkuId(store.id!, sku.id!);

                // this section is for creating entity prices
                // so we should check form values against the
                // existingEntity price or priceTierId to avoid
                // uncessary calls to the backend
                if ((selectedPriceTierId || storeSkuBasePrice) && isCustomPrice) {
                    // this is to check existing form values against db values
                    if (
                        storeSkuBasePrice !== existingEntity?.price ||
                        selectedPriceTierId !== existingEntity?.priceTierId
                    ) {
                        entityPriceUpdates.push({
                            ...(existingEntity?.id && { id: existingEntity.id }),
                            price: formattedPrice !== undefined ? formattedPrice : null,
                            ...(selectedPriceTierId !== null && { priceTierId: selectedPriceTierId }),
                            organizationEntityId: store.id as string,
                            variantId: sku.id as string,
                            type,
                            pricingMethod:
                                (selectedPriceTierId ? PricingMethodType.TIER : PricingMethodType.FLAT) || null,
                        });
                    }
                }

                const updateSkuPrice = () => {
                    const skuIndex = skuUpdates.findIndex((s: Partial<SkuApiDto>) => s.id === sku.id);
                    if (skuIndex === -1) {
                        if (storeBasePrice !== savedSkuPrice) {
                            skuUpdates.push({
                                id: sku.id as string,
                                defaultPrices: {
                                    base: storeBasePrice as number,
                                },
                            });
                        }
                    }
                };

                // then check if form base exists and then if is
                // not the same as the already existing value (default)
                if (storeBasePrice) {
                    // skip if the entry already exists for the same sku
                    updateSkuPrice();
                }

                // allowing non-inv category to save base price as 0
                if (storeBasePrice === 0 && productCategoryName === PRODUCT_CATEGORIES.NonInv) {
                    updateSkuPrice();
                }
            });
        });

        // to avoid unecessary calls again
        if (skuUpdates.length > 0) {
            await updateSkuMutation(skuUpdates);

            const mergedVariants =
                product.variants?.map((variant) => {
                    const updated = skuUpdates.find((u) => u.id === variant.id);
                    return updated ? { ...variant, ...updated } : variant;
                }) ?? [];

            // optimistic update for sku/base price
            setProductUpdate({
                variants: mergedVariants,
            });
        }

        if (entityPriceUpdates.length > 0) {
            await handleEntityPriceMutation(entityPriceUpdates);
        }
    };

    const handlePriceTierSubmit = async (data: PricingFormData) => {
        const entityPriceUpdates: EntityPriceDto[] = [];

        data.stores.forEach((store) => {
            store.skus.forEach((sku) => {
                // these are the values from the form
                const formBasePrice = sku.defaultPrices.base && dollarsToCents(sku.defaultPrices.base);
                const formPriceTierId = sku.priceTierId;
                const formattedPrice = formBasePrice === '' ? null : formBasePrice;
                const { isCustomPrice } = sku;
                const type = 'base';

                const existingEntity = findEntityPriceByOrgEntAndSkuId(store.id!, sku.id!);

                if ((formPriceTierId || formBasePrice) && isCustomPrice) {
                    // checks if is trying to save a value that's already in the entity
                    // prevents unecessary calls;
                    if (!(formBasePrice === existingEntity?.price && formPriceTierId === existingEntity.priceTierId)) {
                        entityPriceUpdates.push({
                            // pushes id or not to the entity
                            ...(existingEntity?.id && { id: existingEntity.id }),
                            organizationEntityId: store.id as string,
                            variantId: sku.id as string,
                            priceTierId: formPriceTierId || null,
                            pricingMethod: formPriceTierId ? PricingMethodType.TIER : PricingMethodType.FLAT,
                            price: formPriceTierId ? null : formattedPrice,
                            type,
                        });
                    }
                }
            });
        });

        if (entityPriceUpdates.length > 0) {
            await handleEntityPriceMutation(entityPriceUpdates);
        }
    };

    const handleSubmit = async (data: PricingFormData) => {
        // Check for pending deletion before anything
        if (Array.from(pendingEntityPriceDeletionIds).length > 0) {
            await entityPricesDeleteMutation();
        }

        await handleProductSubmit(data.priceTierId);

        if (pricingMethod === PricingMethodType.FLAT) {
            await handleFlatPriceSubmit(data);
        } else if (pricingMethod === PricingMethodType.TIER) {
            await handlePriceTierSubmit(data);
        }

        form.reset(data);
        setPriceDirty(false);
    };

    const deleteEntityPrice = async (
        ev: React.MouseEvent<HTMLButtonElement, MouseEvent>,
        storeId: string,
        skuId: string,
    ) => {
        ev.stopPropagation();
        const currentValues = form.getValues() as PricingFormData;

        const storeIndex = currentValues.stores.findIndex((store) => store.id === storeId);
        const skuIndex = currentValues.stores[storeIndex].skus.findIndex((sku) => sku.id === skuId);

        const formSkuPath = `stores.${storeIndex}.skus.${skuIndex}`;
        const formDefaultPricesBase = form.getValues(`stores.defaultPrices.base`);
        const formDefaultPricesBasePath = `${formSkuPath}.defaultPrices.base`;
        const formPriceTierIdPath = `${formSkuPath}.priceTierId`;
        const formSkuIsCustom = `${formSkuPath}.isCustomPrice`;

        const currentProductPriceBase = transformCurrencyToDollars(product?.variants?.[skuIdx].defaultPrices?.base);

        // Find the entity price for the specific store and SKU
        const entity = findEntityPriceByOrgEntAndSkuId(storeId, skuId);

        // Queue the entity for deletion if it exists
        if (entity?.id) queueEntityPriceForDeletion(entity.id);

        if (storeIndex !== -1 && skuIndex !== -1) {
            form.setValue(formSkuIsCustom, false);

            if (pricingMethod === PricingMethodType.FLAT) {
                form.setValue(
                    formDefaultPricesBasePath,
                    stringPriceToFixedDecimals(formDefaultPricesBase) || currentProductPriceBase,
                );
                form.setValue(formPriceTierIdPath, null);
            } else {
                form.setValue(formDefaultPricesBasePath, null);
                form.setValue(formPriceTierIdPath, product.priceTierId || priceTierId);
            }
        }

        // Set the reset flip flag for the specific store and SKU
        updatePricingMethodOverrideMap(
            storeId,
            skuId,
            pricingMethod === PricingMethodType.FLAT ? PricingMethodType.FLAT : PricingMethodType.TIER,
        );

        toggleHiddenCustomPrice(storeId, skuId, true);
        setPriceDirty(true);
    };

    // This is for controlling all data flipping/swapping/modifications;
    // This is the main useEffect for this component, if there's any bug
    // it'll probably be in here
    useEffect(() => {
        if (isLoading) return;

        stores.forEach((store, storeIndex) => {
            const currentStore = storeValues.find((st) => st.id === store.id);
            // local values
            usableSkus?.forEach((sku, skuIndex) => {
                const paths = {
                    price: `stores.${storeIndex}.skus.${skuIndex}.defaultPrices.base`,
                    tier: `stores.${storeIndex}.skus.${skuIndex}.priceTierId`,
                    method: `stores.${storeIndex}.skus.${skuIndex}.priceMethod`,
                    customPrice: `stores.${storeIndex}.skus.${skuIndex}.isCustomPrice`,
                };

                const currentSku = currentStore?.skus.find((s) => s.id === sku.id);
                const isCurrentSkuCustomPrice = Boolean(currentSku?.isCustomPrice);

                const entity = findEntityPriceByOrgEntAndSkuId(store.id, sku.id!);

                const hasNoPendingDeletion = entity?.id && !pendingEntityPriceDeletionIds.has(entity.id);

                // SKU price level
                if (sku.defaultPrices?.base && !entity?.price && !priceDirty) {
                    form.setValue(paths.price, transformCurrencyToDollars(sku.defaultPrices.base));
                    form.setValue(paths.method, PricingMethodType.FLAT);
                }

                // non-inv category allowing zero to be set
                if (
                    sku.defaultPrices?.base !== undefined &&
                    !entity?.price &&
                    !priceDirty &&
                    productCategoryName === PRODUCT_CATEGORIES.NonInv
                ) {
                    form.setValue(paths.price, transformCurrencyToDollars(sku.defaultPrices.base));
                    form.setValue(paths.method, PricingMethodType.FLAT);
                }

                // Entity prices override SKU defaults if not pending deletion, also flips input/select
                if (hasNoPendingDeletion) {
                    if (entity.price) {
                        form.setValue(paths.price, transformCurrencyToDollars(entity.price));
                        form.setValue(paths.method, entity.pricingMethod);
                        form.setValue(paths.customPrice, true);
                        toggleHiddenCustomPrice(store.id, sku.id as string, false);
                    } else if (entity.priceTierId) {
                        form.setValue(paths.price, null);
                        form.setValue(paths.tier, entity.priceTierId);
                        form.setValue(paths.method, entity.pricingMethod);
                        form.setValue(paths.customPrice, true);
                        toggleHiddenCustomPrice(store.id, sku.id as string, false);
                    }
                }
                // if nothing else, use PriceTierId from product level to determine everything;
                if (product.priceTierId && !entity?.id && pricingMethod === PricingMethodType.TIER) {
                    form.setValue(paths.price, null);
                    form.setValue(paths.tier, product.priceTierId);
                    form.setValue(paths.method, product.pricingMethod);
                }

                // if a second/third flip happens we want to append their values back;
                if (pricingMethod === PricingMethodType.FLAT && sku.defaultPrices?.base) {
                    // checking if there's any overridemap avoids reseting
                    // an entity price that's pending deletion
                    if (entity?.id && !pricingMethodOverrideMap.get(store.id)?.has(sku.id!)) {
                        form.setValue(paths.price, transformCurrencyToDollars(entity.price));
                    } else if (!entity?.price) {
                        form.setValue(paths.price, transformCurrencyToDollars(sku.defaultPrices.base));
                    }
                    form.setValue(paths.method, pricingMethod);
                }

                // if is dirty and pricingMethod is tier and is a customPrice
                if (pricingMethod === PricingMethodType.TIER && priceDirty && isCurrentSkuCustomPrice) {
                    // then we save the forceMethod based if the dirty value is base or tier;
                    updatePricingMethodOverrideMap(
                        store.id,
                        sku.id!,
                        // eslint-disable-next-line no-nested-ternary
                        currentSku?.defaultPrices.base
                            ? PricingMethodType.FLAT
                            : currentSku?.priceTierId
                            ? PricingMethodType.TIER
                            : undefined,
                    );
                }
            });
        });
    }, [entityPrices, pendingEntityPriceDeletionIds, isLoading, pricingMethod]);

    // This is for keeping sync on next/previous tabs for priceTiers
    // this probably can be removed later - old logic
    useEffect(() => {
        if (pricingMethod === PricingMethodType.TIER && previousSKUIndex !== null) {
            stores.forEach((_, storeIndex) => {
                const currentValue = form.getValues(`stores.${storeIndex}.skus.${skuIdx}.priceTierId`);
                if (!currentValue) {
                    form.setValue(`stores.${storeIndex}.skus.${skuIdx}.priceTierId`, null);
                }
            });
        }
        setPreviousSKUIndex(skuIdx);
    }, [skuIdx, pricingMethod]);

    // set if has missing prices - incomplete alert
    useEffect(() => {
        const values = form.getValues() as PricingFormData;
        const hasMissingPrices = values.stores?.some((store) =>
            store.skus?.some((sku) => {
                const entityPrice = findEntityPriceByOrgEntAndSkuId(store.id!, sku.id!);

                if (entityPrice) {
                    // Check if entity price is missing both priceTierId and price
                    return !entityPrice.priceTierId && !entityPrice.price;
                }
                // Check if SKU is missing base price and product has no priceTierId
                return !sku.defaultPrices?.base && !product.priceTierId;
            }),
        );

        setMissingPrices(Boolean(hasMissingPrices));
    }, [usableSkus]);

    const handlePrice = (
        e: React.ChangeEvent<HTMLInputElement> | React.FocusEvent<HTMLInputElement>,
        isBlur = false,
    ) => {
        const selectedValue = isBlur ? formatPriceInput(e.target.value) : e.target.value;
        stores.forEach((_, storeIndex) => {
            const hasEntityPrice = skuHasEntityPrice(stores[storeIndex].id, currentSkuId);
            const entityPrice = findEntityPriceByOrgEntAndSkuId(stores[storeIndex].id, currentSkuId);

            if (!hasEntityPrice || forceHiddenCustomPrice.get(stores?.[storeIndex]?.id)?.get(currentSkuId)) {
                if (!(entityPrice?.pricingMethod === PricingMethodType.TIER)) {
                    form.setValue(
                        `stores.${storeIndex}.skus.${skuIdx}.defaultPrices.base`,
                        isBlur ? stringPriceToFixedDecimals(selectedValue) : selectedValue,
                    );
                }
            }
        });
    };

    const isTierPriceDisabled =
        (!isDeliEnabled && isBulkProduct()) ||
        isBulkProductWithMultipleSku() ||
        (priceTiers as PriceTier[])?.length <= 0;

    const handlePricingMethodChange = (selectedPricingMethod: PricingMethodType) => {
        if (product.pricingMethod === selectedPricingMethod) {
            form.reset();
            setPriceDirty(false);
        } else if (pricingMethod) {
            setPriceDirty(true);
        }

        storeValues.forEach((store, storeIndex) => {
            store.skus.forEach((_, skuIndex) => {
                const tier = `stores.${storeIndex}.skus.${skuIndex}.priceTierId`;

                if (pricingMethod === PricingMethodType.FLAT) {
                    form.setValue(tier, null);
                }
            });
        });
    };

    const wrappedOnSubmit =
        (callback?: (data?: CallbackProps) => void) =>
        async (formData: PricingFormData): Promise<void> => {
            try {
                await handleSubmit(formData);

                callback?.({
                    formValue: formData,
                    productData: product,
                    errors: undefined,
                    isBlockingError: false,
                });
            } catch (err) {
                callback?.({
                    formValue: formData,
                    productData: product,
                    errors: err as MutationErrorData[],
                    isBlockingError: true,
                });
            }
        };

    return (
        <>
            <StyledSubHeader>
                {pricingMethod === PricingMethodType.TIER && (
                    <>
                        For stores set to Pre-Tax, the price assigned to the price tier is the price before tax. For
                        store set to Post-Tax, this is the price including tax.
                    </>
                )}
                {pricingMethod === PricingMethodType.FLAT && (
                    <>
                        For stores set to Pre-Tax, this is the price before tax. For store set to Post-Tax, this is the
                        price including tax.
                    </>
                )}
            </StyledSubHeader>
            {usableSkus?.length > 0 ? (
                <ReactHookProductTabForm
                    isBusy={isLoading}
                    formName="ProductPricingForm"
                    onSubmit={wrappedOnSubmit}
                    formContextProps={form}
                    hasMissingPrices={missingPrices}
                    // necessary to check if should use isValid for blocking the button
                    isCustomPriceDirty={priceDirty}
                    enableNavigation={enableNavigation}
                >
                    <Grid container spacing={1} width="100%">
                        <Grid item xs={6}>
                            <HookFormSelect
                                name="pricingMethod"
                                label="Pricing Method"
                                menuItems={BASE_PRICING_METHODS}
                                disabled={isTierPriceDisabled}
                                onChange={(e) => {
                                    handlePricingMethodChange(e.target.value as PricingMethodType);
                                }}
                                required
                            />
                        </Grid>
                        {pricingMethod === PricingMethodType.TIER && (
                            <Grid item xs={6}>
                                <HookFormCustomPrice
                                    name="priceTierId"
                                    handleIsDirty={setPriceDirty}
                                    priceTiers={priceTiers as PriceTier[]}
                                    required
                                    onChange={(e) => {
                                        const selectedValue = e.target.value;
                                        stores.forEach((store, storeIndex) => {
                                            usableSkus?.forEach((sku, localSkuIndex) => {
                                                const entityPriceTierId = findEntityPriceByOrgEntAndSkuId(
                                                    store.id,
                                                    sku.id!,
                                                )?.id;

                                                const currentSkuIsCustom = form.getValues(
                                                    `stores.${storeIndex}.skus.${localSkuIndex}.isCustomPrice`,
                                                );
                                                const method = `stores.${storeIndex}.skus.${localSkuIndex}.priceMethod`;

                                                if (!currentSkuIsCustom || !entityPriceTierId) {
                                                    form.setValue(
                                                        `stores.${storeIndex}.skus.${localSkuIndex}.priceTierId`,
                                                        selectedValue,
                                                    );
                                                    form.setValue(method, PricingMethodType.TIER);
                                                    toggleHiddenCustomPrice(store.id, sku.id as string, true);
                                                }
                                            });
                                        });
                                    }}
                                />
                            </Grid>
                        )}
                    </Grid>
                    {isBulkProductWithSingleSku() && (
                        <BulkProductBox>
                            {productSubCategoryName} products can use Bulk Weight pricing, offering lower prices for
                            larger quantities, or Flat pricing, with a fixed price per gram.
                        </BulkProductBox>
                    )}
                    {isBulkProductWithMultipleSku() && (
                        <BulkProductBox>
                            {productSubCategoryName} product contains multiple SKUs. {productSubCategoryName} products
                            must have only one SKU to use Bulk Weight Price Tiers. Please recreate the product with a
                            single SKU.
                        </BulkProductBox>
                    )}
                    <Box sx={{ marginBottom: '1em' }}>
                        {SKU_NAMES_ARRAY.length > 0 && !isBulkProduct() && (
                            <SKUSelector skus={SKU_NAMES_ARRAY} setSKUIndex={setSKUIndex} />
                        )}
                        {hasSaveCloseButtons && (
                            <Box sx={{ marginTop: '1em', display: 'flex', gap: '1em' }}>
                                <Button
                                    type="reset"
                                    variant="secondary"
                                    label="Cancel"
                                    disabled={!priceDirty}
                                    onClick={() => {
                                        const initialValues = generateFormValues();
                                        initialValues.priceTierId = product.priceTierId || null;

                                        initialValues.stores = initialValues.stores.map((store) => ({
                                            ...store,
                                            skus: store.skus.map((sku) => {
                                                const entityPrice = findEntityPriceByOrgEntAndSkuId(store.id, sku.id!);
                                                return {
                                                    ...sku,
                                                    priceTierId:
                                                        entityPrice?.priceTierId || product.priceTierId || null,
                                                };
                                            }),
                                        }));

                                        form.reset(initialValues);
                                    }}
                                />
                                <Button
                                    type="submit"
                                    label="Apply Changes"
                                    onClick={() => handleSubmit}
                                    // necessary to check if should use !isValid for blocking the button
                                    disabled={isLoading || !priceDirty}
                                />
                            </Box>
                        )}
                    </Box>
                    {pricingMethod === PricingMethodType.FLAT ? (
                        // Flat price
                        <Table>
                            <TableHead style={{ backgroundColor: theme.palette.midribGreen.main }}>
                                <TableRow>
                                    <TableCell style={{ width: '30%' }}>Store</TableCell>
                                    <TableCell align="left">Price</TableCell>
                                </TableRow>
                            </TableHead>
                            <TableBody>
                                <TableRow>
                                    <TableCell style={{ marginTop: '1em', backgroundColor: theme.palette.info.light }}>
                                        Base price
                                    </TableCell>
                                    <TableCell style={{ marginTop: '1em', backgroundColor: theme.palette.info.light }}>
                                        <HookFormCustomPrice
                                            indexes={{
                                                storeIdx: 0,
                                                skuIdx,
                                            }}
                                            key={`defaultPrices.${skuIdx}.base`}
                                            name={`defaultPrices.${skuIdx}.base`}
                                            currentSkuPrice={usableSkus?.[skuIdx]?.defaultPrices?.base}
                                            label="Price"
                                            handleIsDirty={setPriceDirty}
                                            onChange={(e: any) => {
                                                const { value } = e.target;

                                                form.setValue(`stores.defaultPrices.base`, value);

                                                handlePrice(e);
                                            }}
                                            onBlur={(e) => handlePrice(e, true)}
                                            inputMode
                                        />
                                    </TableCell>
                                </TableRow>
                                {stores.map((store, storeIdx) => (
                                    <TableRow key={`${store.id}-${currentSkuId}`}>
                                        <TableCell>{store.name}</TableCell>
                                        <TableCell>
                                            <HookFormCustomPrice
                                                indexes={{
                                                    storeIdx,
                                                    skuIdx,
                                                    forcedMethod: pricingMethodOverrideMap
                                                        .get(store.id)
                                                        ?.get(currentSkuId),
                                                }}
                                                priceTiers={priceTiers as PriceTier[]}
                                                currentSkuPrice={usableSkus?.[skuIdx]?.defaultPrices?.base}
                                                pricingMethodOverride={pricingMethod}
                                                handleIsDirty={setPriceDirty}
                                                toggleCustomPrice={toggleHiddenCustomPrice}
                                                queueEntityPriceForDeletion={queueEntityPriceForDeletion}
                                                onChange={() => {
                                                    const customPricePath = `stores.${storeIdx}.skus.${skuIdx}.isCustomPrice`;
                                                    form.setValue(customPricePath, true);
                                                    toggleHiddenCustomPrice(store.id, currentSkuId, false);
                                                }}
                                            />
                                            {(skuHasEntityPrice(store.id, currentSkuId) ||
                                                skuHasPriceTier(store.id, currentSkuId)) &&
                                                !forceHiddenCustomPrice.get(store.id)?.get(currentSkuId) && (
                                                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                                        <Typography style={{ color: theme.palette.warning.main }}>
                                                            Custom Price
                                                        </Typography>
                                                        <Button
                                                            variant="text"
                                                            type="reset"
                                                            onClick={(ev) => {
                                                                deleteEntityPrice(ev, store.id, currentSkuId as string);
                                                            }}
                                                            label="Reset"
                                                        />
                                                    </Box>
                                                )}
                                        </TableCell>
                                    </TableRow>
                                ))}
                            </TableBody>
                        </Table>
                    ) : (
                        // Price Tier
                        <>
                            <Table>
                                <TableHead style={{ backgroundColor: theme.palette.midribGreen.main }}>
                                    <TableRow>
                                        <TableCell style={{ width: '30%' }}>Store</TableCell>
                                        <TableCell align="left">Price</TableCell>
                                    </TableRow>
                                </TableHead>
                                <TableBody>
                                    {stores.map((store, storeIdx) => (
                                        <TableRow key={`${store.id}-${product.variants?.[skuIdx].id}`}>
                                            <TableCell style={(storeIdx === 0 && { marginTop: '1em' }) || undefined}>
                                                {store.name}
                                            </TableCell>
                                            <TableCell style={(storeIdx === 0 && { marginTop: '1em' }) || undefined}>
                                                <HookFormCustomPrice
                                                    indexes={{
                                                        storeIdx,
                                                        skuIdx,
                                                        forcedMethod: pricingMethodOverrideMap
                                                            .get(store.id)
                                                            ?.get(currentSkuId),
                                                    }}
                                                    priceTiers={priceTiers as PriceTier[]}
                                                    currentSkuPrice={usableSkus?.[skuIdx]?.defaultPrices?.base}
                                                    pricingMethodOverride={pricingMethod}
                                                    handleIsDirty={setPriceDirty}
                                                    toggleCustomPrice={toggleHiddenCustomPrice}
                                                    queueEntityPriceForDeletion={queueEntityPriceForDeletion}
                                                    onChange={() => {
                                                        form.setValue(
                                                            `stores.${storeIdx}.skus.${skuIdx}.isCustomPrice`,
                                                            true,
                                                        );
                                                        toggleHiddenCustomPrice(store.id, currentSkuId, false);
                                                    }}
                                                />
                                                {(skuHasEntityPrice(store.id, currentSkuId) ||
                                                    skuHasPriceTier(store.id, currentSkuId)) &&
                                                    !forceHiddenCustomPrice.get(store.id)?.get(currentSkuId) && (
                                                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                                            <Typography style={{ color: theme.palette.warning.main }}>
                                                                Custom Price
                                                            </Typography>
                                                            <Button
                                                                variant="text"
                                                                type="reset"
                                                                onClick={(ev) => {
                                                                    deleteEntityPrice(
                                                                        ev,
                                                                        store.id,
                                                                        currentSkuId as string,
                                                                    );
                                                                }}
                                                                label="Reset"
                                                            />
                                                        </Box>
                                                    )}
                                            </TableCell>
                                        </TableRow>
                                    ))}
                                </TableBody>
                            </Table>
                        </>
                    )}
                </ReactHookProductTabForm>
            ) : (
                <> Selected product does not have any available variants.</>
            )}
        </>
    );
}
