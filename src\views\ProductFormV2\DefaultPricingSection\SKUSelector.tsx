import React, { Dispatch, SetStateAction, useState } from 'react';
import { IconButton, Box, Button } from '@mui/material';
import { styled } from '@mui/material/styles';
import ChevronLeftIcon from '@mui/icons-material/ChevronLeft';
import ChevronRightIcon from '@mui/icons-material/ChevronRight';

export interface SKU {
    id: string;
    name: string;
}

interface SKUSelectorProps {
    skus: SKU[];
    setSKUIndex: Dispatch<SetStateAction<number>>;
}

const Container = styled(Box)(() => ({
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: '1em',
    position: 'relative',
    overflow: 'hidden',
}));

const SkusWrapper = styled(Box)({
    display: 'flex',
    flex: 1,
    justifyContent: 'center',
    position: 'relative',
    paddingBottom: '.5em',
});

const SkuButton = styled(But<PERSON>, {
    shouldForwardProp: (prop) => prop !== 'active',
  })<{ active: boolean }>(({ active }) => ({
    fontWeight: 'normal',
    color: active ? 'black' : 'gray',
    textAlign: 'center',
    width: '100%',
    transition: 'color 0.3s ease',
}));

const Indicator = styled(Box)<{ activeindex: number; total: number }>(({ activeindex, total }) => ({
    position: 'absolute',
    bottom: 0,
    left: `${(activeindex * 100) / total}%`,
    width: `${100 / total}%`,
    height: '2px',
    backgroundColor: 'black',
    transition: 'left 0.3s ease, width 0.3s ease',
}));

const SKUSelector: React.FC<SKUSelectorProps> = ({ skus, setSKUIndex }) => {
    const [activeIndex, setActiveIndex] = useState(0);

    const handlePrev = () => {
        if (activeIndex > 0) {
            setActiveIndex(activeIndex - 1);
            setSKUIndex(activeIndex - 1);
        }
    };

    const handleNext = () => {
        if (activeIndex < skus.length - 1) {
            setActiveIndex(activeIndex + 1);
            setSKUIndex(activeIndex + 1);
        }
    };

    return (
        <Container>
            <IconButton onClick={handlePrev} disabled={activeIndex === 0} color="inherit">
                <ChevronLeftIcon color={activeIndex === 0 ? 'disabled' : 'inherit'} />
            </IconButton>

            <SkusWrapper>
                {skus.map((sku, index) => (
                    <SkuButton
                        onClick={() => {
                            setActiveIndex(index);
                            setSKUIndex(index);
                        }}
                        key={sku.id}
                        active={Boolean(index === activeIndex)}
                        sx={{ width: `${100 / skus.length}%` }}
                    >
                        {sku.name}
                    </SkuButton>
                ))}
                <Indicator activeindex={activeIndex} total={skus.length} />
            </SkusWrapper>

            <IconButton onClick={handleNext} disabled={activeIndex === skus.length - 1} color="inherit">
                <ChevronRightIcon color={activeIndex === skus.length - 1 ? 'disabled' : 'inherit'} />
            </IconButton>
        </Container>
    );
};

export default SKUSelector;
