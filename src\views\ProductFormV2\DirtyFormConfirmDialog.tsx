import React, { useEffect, useState } from 'react';
import { Typography } from '@mui/material/';
import { Modal } from '@treez-inc/component-library';
import useProductTabNavigation from './Hooks/useProductTabNavigation';
import { BaseNavigationOption } from './Types/NavigationOption';

interface MutationErrorDialogProps {
    onNavigate: (submit: boolean, navigate: BaseNavigationOption) => any;
}

export default function DirtyFormConfirmDialog({ onNavigate }: MutationErrorDialogProps) {
    const { confirmNavigation } = useProductTabNavigation();
    const [isOpen, setIsOpen] = useState<boolean>(!!confirmNavigation);

    useEffect(() => {
        setIsOpen(!!confirmNavigation);
    }, [confirmNavigation]);

    const handleSubmitAndNavigate = async () => {
        setIsOpen(false);
        if (confirmNavigation) {
            onNavigate(true, confirmNavigation);
        }
    };

    const handleClose = () => {
        setIsOpen(false);

        if (confirmNavigation) {
            onNavigate(false, { ...confirmNavigation, force: true });
        }
    };

    return (
        <Modal
            testId="product-form-exit-modal"
            title="UNSAVED CHANGES"
            content={<Typography>Would you like to save the changes you made?</Typography>}
            onClose={() => setIsOpen(false)}
            open={!!isOpen}
            primaryButton={{ label: 'Yes', onClick: handleSubmitAndNavigate }}
            secondaryButton={{ label: 'No', onClick: handleClose }}
        />
    );
}
