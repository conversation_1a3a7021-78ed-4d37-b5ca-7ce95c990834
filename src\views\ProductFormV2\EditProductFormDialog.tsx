import React, { useEffect, useState } from 'react';
import { Box, styled } from '@mui/material/';
import useLoadData from '../../hooks/useLoadData';
import productApiKeyStore from '../../api/productApiKeyStore';
import ProductFormHeader from './ProductFormHeader';
import LoadingIndicator from '../../components/LoadingIndicator';
import { ProductDto } from '../../interfaces/dto/product';
import TabName from './Types/TabNames.enum';
import useProduct from './Hooks/useProduct';
import { ProductCategoryDto } from '../../interfaces/dto/productCategory';
import productCategoryApiKeyStore from '../../api/productCategoryApiKeyStore';
import useProductTabNavigation from './Hooks/useProductTabNavigation';
import ProductForms from './ProductForms';
import { PageName, TabNavigation } from './Types/Navigation.enum';
import { PageNavigationOption, TabNavigationOption } from './Types/NavigationOption';
import ProductPreviewDialog from './ProductPreviewDialog';
import { EntityPriceDto } from '../../interfaces/dto/entityPrice';
import entityPriceApiKeyStore from '../../api/entityPriceApiKeyStore';
import { SkuDto } from '../../interfaces/dto/sku';
import { ProductSubCategoryDto } from '../../interfaces/dto/productSubCategory';

const DialogStyled = styled(Box)`
    .MuiPaper-root {
        overflow-y: hidden;
    }
`;

interface EditProductFormDialogProps {
    productId?: string;
    currentTab: TabName;
}

const EditProductFormDialog = ({ productId, currentTab }: EditProductFormDialogProps) => {
    const { product, setProductUpdate, setEntityPriceUpdate } = useProduct();
    const { navigate } = useProductTabNavigation();

    const [isLoadingCompleted, setIsLoadingCompleted] = useState(productId === undefined);
    const [sortedCategories, setSortedCategories] = useState<ProductCategoryDto[]>([]);
    const [isPreviewOpen, setIsPreviewOpen] = useState(false);

    const {
        isLoading,
        isError,
        data: productResponseData,
        refetch,
    } = useLoadData<ProductDto>({
        queryConfig: productApiKeyStore.getProductDetails(productId),
    });

    const usableVariants = productResponseData?.variants
        ?.map((variant) => !variant.parentId && variant)
        .filter(Boolean) as SkuDto[];

    const variantIds = usableVariants?.map((variants) => variants.id) as string[];

    const {
        isLoading: isEntityLoading,
        data: entityPrices = [],
        isSuccess,
    } = useLoadData<EntityPriceDto[]>({
        queryConfig: entityPriceApiKeyStore.getEntityPrices(variantIds),
    });

    const {
        isError: isCategoryError,
        data: categories,
        isLoading: isLoadingCategories,
    } = useLoadData<ProductCategoryDto[]>({
        queryConfig: productCategoryApiKeyStore.getAllCategories(),
    });

    const { data: subCategories } = useLoadData<ProductSubCategoryDto[]>({
        queryConfig: productCategoryApiKeyStore.getAllSubCategories(),
    });

    useEffect(() => {
        navigate(new TabNavigationOption(TabNavigation.SELECT, currentTab));
    }, [currentTab]);

    useEffect(() => {
        if (categories) {
            // sort categories alphabetically
            setSortedCategories(
                categories.sort((a: ProductCategoryDto, b: ProductCategoryDto) => {
                    if (a.name.toLowerCase() < b.name.toLowerCase()) return -1;
                    if (a.name.toLowerCase() > b.name.toLowerCase()) return 1;
                    return 0;
                }),
            );
        }

        if (productResponseData && categories) {
            const productSubCategory = subCategories?.find((sc) => sc.id === productResponseData.productSubCategoryId);
            const productData = {
                ...productResponseData,
                productCategory: categories.find(
                    (c) => c.id === productResponseData.productSubCategory?.productCategoryId,
                ),
                productImages: productResponseData.images?.filter(
                    (i) => i.productId !== undefined && i.variantId === undefined,
                ),
                variants: productResponseData.variants?.map((v) => ({
                    ...v,
                    variantImages: productResponseData.images?.filter((i) => i.variantId === v.id),
                })),
                ...(productSubCategory && { productSubCategory }),
            };

            setProductUpdate(productData);
            setIsLoadingCompleted(true);
        }
    }, [productResponseData, categories]);

    useEffect(() => {
        if (productResponseData && isSuccess) {
            setEntityPriceUpdate(entityPrices);
        }
    }, [productResponseData, entityPrices, isSuccess]);

    const onHandleCloseClick = () => {
        navigate(new PageNavigationOption(PageName.PRODUCT_CONTROL_PAGE));
    };

    const onHandlePreviewClick = () => {
        refetch();
        setIsPreviewOpen(true);
    };

    const handlePreviewClose = () => {
        setIsPreviewOpen(false);
    };

    return (
        <>
            <DialogStyled>
                <ProductFormHeader
                    productId={productId}
                    isLoadingCompleted={isLoadingCompleted}
                    onCloseClick={onHandleCloseClick}
                    onPreviewClick={onHandlePreviewClick}
                />
                <LoadingIndicator isLoading={isLoading || isLoadingCategories || isEntityLoading} />
                {(isError || isCategoryError) && <>Error loading product - to be implemented</>}
                <ProductForms
                    isProductLoading={isLoading}
                    isLoadingCompleted={isLoadingCompleted}
                    categories={sortedCategories}
                />
            </DialogStyled>
            <LoadingIndicator isLoading={isLoading || isLoadingCategories} />
            <ProductPreviewDialog
                isOpen={isPreviewOpen}
                onClose={handlePreviewClose}
                productCategoryName={product.productCategory?.name}
                currentProductDetails={productResponseData}
            />
        </>
    );
};

export default EditProductFormDialog;
