import React from 'react';
import { useParams, useSearchParams } from 'react-router-dom';
import { EditProductContextProvider } from './Context/EditProductContext';
import EditProductFormDialog from './EditProductFormDialog';
import brandApiKeyStore from '../../api/brandApiKeyStore';
import productCategoryApiKeyStore from '../../api/productCategoryApiKeyStore';
import useLoadData from '../../hooks/useLoadData';
import attributesApiKeyStore from '../../api/attributesApiKeyStore';
import { EditProductNavigationContextProvider } from './Context/EditProductNavigationContext';
import TabName from './Types/TabNames.enum';
import Mode from './Types/Mode';

interface EditProductPageProps {
    mode: Mode;
}

const EditProductPage = ({ mode }: EditProductPageProps) => {
    const { productId } = useParams();
    const [searchParams] = useSearchParams();
    const currentTab = Object.values(TabName).find((t) => t === searchParams.get('tab'));

    // Caching data to improve performance
    useLoadData({
        queryConfig: productCategoryApiKeyStore.getAllSubCategories(),
    });
    useLoadData({
        queryConfig: brandApiKeyStore.getBrands(),
    });
    useLoadData({
        queryConfig: attributesApiKeyStore.getAttributeCategories(),
    });

    return (
        <EditProductContextProvider mode={mode}>
            <EditProductNavigationContextProvider>
                <EditProductFormDialog
                    productId={productId}
                    currentTab={!productId || !currentTab ? TabName.PRODUCT_INFO : (currentTab as TabName)}
                />
            </EditProductNavigationContextProvider>
        </EditProductContextProvider>
    );
};

export default EditProductPage;
