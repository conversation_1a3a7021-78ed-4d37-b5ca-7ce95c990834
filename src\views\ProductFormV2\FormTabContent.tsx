import React from 'react';
import TabName from './Types/TabNames.enum';
import ProductInfoSection from './ProductInfoSection/ProductInfoSection';
import VariantsSection from './VariantsSection/VariantsSection';
import AttributesSection from './AttributesSection/AttributesSection';
import DefaultPricingSection from './DefaultPricingSection/DefaultPricingSection';
import ImagesSection from './ImagesSection/ImagesSection';
import { ProductCategoryDto } from '../../interfaces/dto/productCategory';
import useProductTabNavigation from './Hooks/useProductTabNavigation';

interface ProductSectionProps {
    categories: ProductCategoryDto[];
}

const FormTabContent = ({ categories }: ProductSectionProps) => {
    const { tabs } = useProductTabNavigation();
    const sectionName = tabs.find((t) => t.isSelected)?.tab;

    switch (sectionName) {
        case TabName.PRODUCT_INFO:
            return <ProductInfoSection categories={categories} />;
        case TabName.SKU:
            return <VariantsSection />;
        case TabName.ATTRIBUTES:
            return <AttributesSection />;
        case TabName.PRICING:
            return <DefaultPricingSection />;
        case TabName.IMAGES:
            return <ImagesSection />;
        default:
            return <>Not implemented</>;
    }
};

export default FormTabContent;
