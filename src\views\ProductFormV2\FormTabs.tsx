import React from 'react';
import { LinearStepper } from '@treez-inc/component-library';
import useProductTabNavigation from './Hooks/useProductTabNavigation';
import { TabInfo } from './Types/TabInfo';
import useProduct from './Hooks/useProduct';
import Mode from './Types/Mode';
import { TabNavigation } from './Types/Navigation.enum';
import { TabNavigationOption } from './Types/NavigationOption';

interface StepperData extends TabInfo {
    completed: boolean;
    disabled: boolean;
    onClick: (index: number) => void;
}

const FormTabs = () => {
    const { mode } = useProduct();
    const { tabs, navigate } = useProductTabNavigation();

    const sectionSelectionChangeHandler = (index: number) => {
        navigate(new TabNavigationOption(TabNavigation.SELECT, tabs[index].tab));
    };
    const selectedIndex = tabs.findIndex((t) => t.isSelected);
    const stepperData = tabs?.map<StepperData>((t, i) => ({
        ...t,
        disabled: mode === Mode.EDIT ? false : i > selectedIndex,
        completed: mode === Mode.EDIT ? true : i <= selectedIndex,
        onClick: sectionSelectionChangeHandler,
    }));

    const currentIndex = tabs.findIndex((t) => t.isSelected);

    return <LinearStepper activeStep={currentIndex >= 0 ? currentIndex : 0} steps={stepperData} />;
};

export default FormTabs;
