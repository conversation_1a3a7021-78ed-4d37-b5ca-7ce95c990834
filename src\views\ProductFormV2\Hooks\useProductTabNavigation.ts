import { useContext } from 'react';
import { EditProductTabNavigationContext } from '../Context/EditProductNavigationContext';

const useProductTabNavigation = () => {
    const context = useContext(EditProductTabNavigationContext);

    if (context === undefined) {
        throw new Error('Hook must be used within a EditProductTabNavigationContextProvider');
    }

    return context;
};

export default useProductTabNavigation;
