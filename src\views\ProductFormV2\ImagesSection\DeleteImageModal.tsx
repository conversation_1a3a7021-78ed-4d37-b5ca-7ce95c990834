import React from 'react';
import { But<PERSON> } from '@treez-inc/component-library';
import { DialogActions, DialogContent, DialogTitle } from '@mui/material/';
import { DialogContentRow, ImagesDialog } from './ImageDetailsModal';
import ImageViewer from './ImageViewer';
import { ImageDetailsDto } from '../../../interfaces/dto/imageDetails';
import useProduct from '../Hooks/useProduct';
import { isEmpty } from '../../../utils/common';
import useSaveData, { MutationResponse } from '../../../hooks/useSaveData';
import imageApiKeyStore from '../../../api/imageApiKeyStore';
import filterMutationErrors from '../../../utils/MutationResponseUtil';
import LoadingIndicator from '../../../components/LoadingIndicator';
import { StyledSubHeader } from '../../../styles/globalStyles';

interface DeleteImageModalProps {
    image: ImageDetailsDto;
    onClose: (reason?: any) => void;
}

const DeleteImageModal = ({ image, onClose }: DeleteImageModalProps) => {
    const { product, setProductUpdate } = useProduct();
    const {
        isLoading: isSaving,
        data,
        mutateAsync,
    } = useSaveData<ImageDetailsDto[]>({
        mutationConfig: imageApiKeyStore.saveImageDetails(),
    });

    const isVariantImage = !isEmpty(image.variantId);

    const getCurrentImages = () => {
        if (isVariantImage) {
            const variant = product.variants?.find((v) => v.id === image.variantId);
            return variant?.images ?? [];
        }
        return product.images ?? [];
    };

    const buildUpdateData = () => {
        const allImages = getCurrentImages().sort((a: any, b: any) => {
            if (a.order > b.order) return 1;
            if (a.order < b.order) return -1;
            return 0;
        });

        const otherImages = allImages
            .filter((img) => img.order && image.order && img.order > image.order)
            .map((img, index) => ({
                ...img,
                description: img.description ? img.description : undefined,
                variantId: img.variantId ? img.variantId : undefined,
                productId: img.productId ? img.productId : undefined,
                name: img.name ? img.name : undefined,
                order: index + (image.order ?? 0),
            }));

        return !isEmpty(otherImages) ? otherImages : undefined;
    };
    const mergeImages = (left: ImageDetailsDto[], right: ImageDetailsDto[]) => {
        const images = [...left, ...right];
        return images.reduce((result: ImageDetailsDto[], img: ImageDetailsDto) => {
            const index = result.findIndex((r) => r.id === img.id);
            if (index >= 0) {
                const mergesArray = [...result];
                mergesArray[index] = { ...result[index], ...img };
                return mergesArray;
            }
            return [...result, img];
        }, []);
    };
    const updateProductImage = (result: MutationResponse<ImageDetailsDto[]>) => {
        const images = mergeImages(product.images ?? [], [
            ...(result?.createData?.data ?? []),
            ...(result?.updateData?.data ?? []),
        ]);
        setProductUpdate({ images: images.filter((img) => img.id !== result.deleteData?.data?.[0]?.id) });
    };
    const updateVariantImage = (result: MutationResponse<ImageDetailsDto[]>) => {
        const { variants } = product;
        const variant = variants?.find((v) => v.id === image.variantId);
        if (variant) {
            variant.images = mergeImages(
                (variant.images ?? []).filter((i) => i.id !== result.deleteData?.data?.[0]?.id),
                [...(result?.createData?.data ?? []), ...(result?.updateData?.data ?? [])],
            );
        }
        setProductUpdate({ variants });
    };

    const handleConfirmation = async () => {
        const result = await mutateAsync({ deleteData: { ids: [image.id] }, updateData: buildUpdateData() });
        const errors = filterMutationErrors(result);
        if (!errors) {
            if (isVariantImage) {
                updateVariantImage(result);
            } else {
                updateProductImage(result);
            }
            onClose();
        }
    };

    const handleCancel = () => {
        onClose();
    };

    const errors = filterMutationErrors(data ?? {});

    return (
        <ImagesDialog open onClose={onClose}>
            <DialogTitle>
                <StyledSubHeader>Remove Image?</StyledSubHeader>
            </DialogTitle>
            <DialogContent>
                <DialogContentRow>
                    <ImageViewer image={image} />
                </DialogContentRow>
                <DialogContentRow>
                    <StyledSubHeader>Please confirm that you would like to remove this image.</StyledSubHeader>
                    <LoadingIndicator isLoading={isSaving} />
                </DialogContentRow>
                <DialogContentRow>{errors && <div>{errors.join(', ')}</div>}</DialogContentRow>
            </DialogContent>
            <DialogActions>
                <>
                    <Button label="No" variant="secondary" onClick={handleCancel} disabled={isSaving} />
                    <Button label="Yes" variant="primary" onClick={handleConfirmation} disabled={isSaving} />
                </>
            </DialogActions>
        </ImagesDialog>
    );
};

export default DeleteImageModal;
