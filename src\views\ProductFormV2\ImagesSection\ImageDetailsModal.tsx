import React from 'react';
import { Box, Dialog, DialogActions, DialogContent, DialogTitle, styled } from '@mui/material/';
import { convertPxToRem } from '@treez-inc/component-library';
import { ImageDetailsDto } from '../../../interfaces/dto/imageDetails';
import ImageViewer from './ImageViewer';
import useProduct from '../Hooks/useProduct';
import { isEmpty } from '../../../utils/common';
import useReactHookForm from '../../../hooks/useReactHookForm';
import ReactHookForm from '../../../components/hook-form-v2/ReactHookForm';
import HookFormInput from '../../../components/hook-form-v2/HookFormInput';
import HookFormCheckbox from '../../../components/hook-form-v2/HookFormCheckbox';
import { StyledPrimaryButton } from '../../../styles/StyledProductForm';
import useSaveData, { MutationResponse } from '../../../hooks/useSaveData';
import imageApiKeyStore from '../../../api/imageApiKeyStore';
import filterMutationErrors from '../../../utils/MutationResponseUtil';
import { StyledHeader } from '../../../styles/globalStyles';

export const ImagesDialog = styled(Dialog)(() => ({
    '.MuiDialog-paper': {
        borderRadius: convertPxToRem(28),
        width: convertPxToRem(606),
    },
}));

export const DialogContentRow = styled(Box)(() => ({
    padding: `${convertPxToRem(10)} 0`,
    textAlign: 'center',
    fontSize: convertPxToRem(15),
    fontStyle: 'normal',
    fontWeight: 500,
}));

interface ImageDetailsModalProps {
    image: ImageDetailsDto;
    onClose: () => void;
}

const ImageDetailsModal = ({ image, onClose }: ImageDetailsModalProps) => {
    const { product, setProductUpdate } = useProduct();

    const form = useReactHookForm<ImageDetailsDto>({
        values: {
            ...image,
        },
    });
    const { isLoading: isSaving, mutateAsync } = useSaveData<ImageDetailsDto[]>({
        mutationConfig: imageApiKeyStore.saveImageDetails(),
    });

    const isVariantImage = !isEmpty(image.variantId);

    const mergeImages = (left: ImageDetailsDto[], right: ImageDetailsDto[]) => {
        const images = [...left, ...right];
        return images.reduce((result: ImageDetailsDto[], img: ImageDetailsDto) => {
            const index = result.findIndex((r) => r.id === img.id);
            if (index >= 0) {
                const mergesArray = [...result];
                mergesArray[index] = { ...result[index], ...img };
                return mergesArray;
            }
            return [...result, img];
        }, []);
    };

    const hadChanges = (values: any) =>
        !values.id || values.default || image.name !== values.name || image.description !== values.description;

    const updateProductImage = (result: MutationResponse<ImageDetailsDto[]>) => {
        const images = mergeImages(product.images ?? [], [
            ...(result?.createData?.data ?? []),
            ...(result?.updateData?.data ?? []),
        ]);
        setProductUpdate({ images });
    };

    const updateVariantImage = (result: MutationResponse<ImageDetailsDto[]>) => {
        const { variants } = product;
        const variant = variants?.find((v) => v.id === image.variantId);
        if (variant) {
            variant.images = mergeImages(variant.images ?? [], [
                ...(result?.createData?.data ?? []),
                ...(result?.updateData?.data ?? []),
            ]);
        }
        setProductUpdate({ variants });
    };
    const getCurrentImages = () => {
        if (isVariantImage) {
            const variant = product.variants?.find((v) => v.id === image.variantId);
            return variant?.images ?? [];
        }
        return product.images ?? [];
    };
    const buildUpdateData = (imageValue: ImageDetailsDto, isDefault: boolean) => {
        if (!isDefault) {
            return imageValue.id ? [imageValue] : undefined;
        }

        const allImages = getCurrentImages().sort((a: any, b: any) => {
            if (a.order > b.order) return 1;
            if (a.order < b.order) return -1;
            return 0;
        });

        const otherImages = allImages
            .filter((img) => img.id !== imageValue.id)
            .map((img, index) => ({
                ...img,
                description: img.description ? img.description : undefined,
                variantId: img.variantId ? img.variantId : undefined,
                productId: img.productId ? img.productId : undefined,
                name: img.name ? img.name : undefined,
                order: index + 2,
            }));

        return imageValue.id ? [imageValue, ...otherImages] : otherImages;
    };

    const buildMutationData = (values: any) => {
        const imageValue = {
            ...image,
            ...values,
            order: values.default ? 1 : image.order,
            description: values.description ? values.description : undefined,
            variantId: values.variantId ? values.variantId : undefined,
            productId: values.productId ? values.productId : undefined,
            name: values.name ? values.name : undefined,
        };

        const updateData = buildUpdateData(imageValue, values.default);
        return {
            createData: !values.id ? [imageValue] : undefined,
            updateData,
        };
    };

    const handleSubmit = async (values: any) => {
        if (hadChanges(values)) {
            const mutationData = buildMutationData(values);
            if (mutationData.createData || mutationData.updateData) {
                const result = await mutateAsync(mutationData);
                const errors = filterMutationErrors(result);
                if (!errors) {
                    if (isVariantImage) {
                        updateVariantImage(result);
                    } else {
                        updateProductImage(result);
                    }
                }
            }
        }
        onClose();
    };

    const getTitle = () => (image.id ? `Update Image` : `Create Image`);

    return (
        <ImagesDialog open onClose={onClose}>
            <ReactHookForm formName="VariantInfoForm" onSubmit={handleSubmit} formContextProps={form} isBusy={isSaving}>
                <DialogTitle>
                    <StyledHeader>{getTitle()}</StyledHeader>
                </DialogTitle>
                <DialogContent>
                    <DialogContentRow>
                        <ImageViewer image={image} />
                    </DialogContentRow>
                    <DialogContentRow>
                        <HookFormInput name="name" label="Title" />
                    </DialogContentRow>
                    <DialogContentRow>
                        <HookFormInput name="description" label="Description" />
                    </DialogContentRow>
                    {image.order !== 1 && (
                        <DialogContentRow>
                            <HookFormCheckbox name="default" label="Make Default" />
                        </DialogContentRow>
                    )}
                </DialogContent>
                <DialogActions>
                    <StyledPrimaryButton
                        name="Save and Continue"
                        type="submit"
                        data-testid="next-section-button"
                        disabled={isSaving}
                    >
                        Save
                    </StyledPrimaryButton>
                </DialogActions>
            </ReactHookForm>
        </ImagesDialog>
    );
};

export default ImageDetailsModal;
