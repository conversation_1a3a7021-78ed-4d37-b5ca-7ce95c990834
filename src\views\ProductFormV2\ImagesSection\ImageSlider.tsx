import React from 'react';
import { Box, IconButton, styled } from '@mui/material/';
import { convertPxToRem, Icon } from '@treez-inc/component-library';
import { ImageDetailsDto } from '../../../interfaces/dto/imageDetails';
import ImageViewer from './ImageViewer';

const ImgSlider = styled(Box)(() => ({
    display: 'block',
}));

const ImgThumbnailWrapper = styled(Box)(() => ({
    display: 'inline-flex',
    flexDirection: 'column',
    margin: `${convertPxToRem(5)} ${convertPxToRem(15)}`,
    width: convertPxToRem(70),
}));

const ImgThumbnail = styled(Box)(({ theme }) => ({
    border: `${convertPxToRem(1)} solid ${theme.palette.grey04.main}`,
    borderRadius: convertPxToRem(16),
    cursor: 'pointer',
    height: convertPxToRem(64),
    position: 'relative',
    width: convertPxToRem(64),
    '&:hover': {
        '.imgIconBtn': {
            display: 'inline-flex',
        },
    },
}));

const EditImgButton = styled(IconButton)(({ theme }) => ({
    border: `1px solid ${theme.palette.grey04.main}`,
    background: `${theme.palette.primaryWhite.main}`,
    display: 'none',
    height: '24px',
    overflow: 'hidden',
    position: 'absolute',
    right: '30px',
    top: '-8px',
    width: '24px',

    '&:hover': {
        background: `${theme.palette.green03.main}`,
    },
}));

const DeleteImgButton = styled(IconButton)(({ theme }) => ({
    border: `1px solid ${theme.palette.grey04.main}`,
    background: `${theme.palette.primaryWhite.main}`,
    display: 'none',
    height: '24px',
    overflow: 'hidden',
    position: 'absolute',
    right: '3px',
    top: '-8px',
    width: '24px',

    '&:hover': {
        background: `${theme.palette.green03.main}`,
    },
}));

const ImgLabel = styled(Box)(({ theme }) => ({
    color: `${theme.palette.primaryBlack.main}`,
    fontSize: convertPxToRem(12),
    fontStyle: 'normal',
    fontWeight: '400',
    lineHeight: convertPxToRem(16),
    padding: convertPxToRem(5),
    textAlign: 'center',
}));

interface ImageSliderProps {
    images: ImageDetailsDto[];
    onEditImage?: (image: ImageDetailsDto) => any;
    onDeleteImage?: (image: ImageDetailsDto) => any;
}

const ImageSlider = ({ images, onEditImage, onDeleteImage }: ImageSliderProps) => {
    const handleImageEdit = (imageDetails: ImageDetailsDto) => {
        onEditImage?.(imageDetails);
    };

    const handleImageDelete = (imageDetails: ImageDetailsDto) => {
        onDeleteImage?.(imageDetails);
    };

    if (images.length <= 1) {
        return null;
    }

    return (
        <ImgSlider>
            {images
                ?.filter((i) => i.order !== 1)
                .map((imageDetails: ImageDetailsDto) => (
                    <ImgThumbnailWrapper key={imageDetails.imageId}>
                        <ImgThumbnail>
                            <EditImgButton className="imgIconBtn" onClick={() => handleImageEdit(imageDetails)}>
                                <Icon iconName="Edit" fontSize="small" />
                            </EditImgButton>
                            {!imageDetails.verifiedReferenceId && (
                                <DeleteImgButton className="imgIconBtn" onClick={() => handleImageDelete(imageDetails)}>
                                    <Icon iconName="Delete" fontSize="small" />
                                </DeleteImgButton>
                            )}
                            <ImageViewer image={imageDetails} />
                        </ImgThumbnail>
                        <ImgLabel>{imageDetails.name}</ImgLabel>
                    </ImgThumbnailWrapper>
                ))}
        </ImgSlider>
    );
};

export default ImageSlider;
