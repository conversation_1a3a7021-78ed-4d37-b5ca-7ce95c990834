import React from 'react';
import { styled, Box } from '@mui/material';
import { convertPxToRem } from '@treez-inc/component-library';
import ProductImagesContainer from './ProductImagesContainer';
import VariantImagesContainer from './VariantImagesContainer';
import ProductFormButtons from '../ProductFormButtons';
import useProductTabNavigation from '../Hooks/useProductTabNavigation';
import { BaseNavigationOption } from '../Types/NavigationOption';

const ImagesSectionContainer = styled(Box)(() => ({
    display: 'flex',
    flexDirection: 'column',
    rowGap: convertPxToRem(16),
}));

const ImagesSection = () => {
    const { navigate } = useProductTabNavigation();

    const handleNavigate = (isSubmit: boolean, nav: BaseNavigationOption) => {
        navigate(nav);
    };

    return (
        <ImagesSectionContainer>
            <ProductImagesContainer />
            <VariantImagesContainer />
            <ProductFormButtons onNavigate={handleNavigate} />
        </ImagesSectionContainer>
    );
};

export default ImagesSection;
