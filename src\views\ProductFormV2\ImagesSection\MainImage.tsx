import React from 'react';
import { Box, Chip, IconButton, styled } from '@mui/material/';
import { convertPxToRem, Icon } from '@treez-inc/component-library';
import { ImageDetailsDto } from '../../../interfaces/dto/imageDetails';
import ImageViewer from './ImageViewer';

const MainImgDetails = styled(Box)(({ theme }) => ({
    background: `${theme.palette.primaryWhite.main}`,
    border: `1px solid ${theme.palette.grey04.main}`,
    borderRadius: '16px',
    height: '300px',
    padding: '16px',
}));

const EditImgButton = styled(IconButton)(({ theme }) => ({
    border: `1px solid ${theme.palette.grey04.main}`,
    background: `${theme.palette.primaryWhite.main}`,
    display: 'none',
    top: '-8px',
    right: '30px',
    overflow: 'hidden',
    position: 'absolute',
    width: '24px',
    height: '24px',

    '&:hover': {
        background: `${theme.palette.green03.main}`,
    },
}));

const DeleteImgButton = styled(IconButton)(({ theme }) => ({
    border: `1px solid ${theme.palette.grey04.main}`,
    background: `${theme.palette.primaryWhite.main}`,
    display: 'none',
    top: '-8px',
    right: '3px',
    overflow: 'hidden',
    position: 'absolute',
    width: '24px',
    height: '24px',

    '&:hover': {
        background: `${theme.palette.green03.main}`,
    },
}));

const MainImgWrapper = styled(Box)(() => ({
    height: convertPxToRem(260),
    position: 'relative',
    margin: '0 auto',
    width: convertPxToRem(260),
    '&:hover': {
        cursor: 'pointer',
        '.imgIconBtn': {
            display: 'inline-flex',
        },
    },
}));

const MainImgChip = styled(Chip)(({ theme }) => ({
    background: `${theme.palette.green03.main}`,
    marginBottom: '10px',
}));

const ImgLabel = styled(Box)(({ theme }) => ({
    color: `${theme.palette.primaryBlack.main}`,
    fontFamily: 'Roboto',
    fontSize: '12px',
    fontStyle: 'normal',
    fontWeight: 400,
    lineHeight: '16px',
    padding: '5px',
    textAlign: 'center',
}));

interface MainImageProps {
    mainImage: ImageDetailsDto;
    onEditImage?: (image: ImageDetailsDto) => any;
    onDeleteImage?: (image: ImageDetailsDto) => any;
}

const MainImage = ({ mainImage, onEditImage, onDeleteImage }: MainImageProps) => {
    const handleImageEdit = () => {
        onEditImage?.(mainImage);
    };

    const handleImageDelete = () => {
        onDeleteImage?.(mainImage);
    };

    return (
        <>
            <MainImgDetails>
                <MainImgChip variant="filled" label="Main image" />
                <MainImgWrapper className="mainImgWrapper">
                    <EditImgButton className="imgIconBtn" onClick={handleImageEdit}>
                        <Icon iconName="Edit" fontSize="small" />
                    </EditImgButton>
                    {!mainImage.verifiedReferenceId && (
                        <DeleteImgButton className="imgIconBtn" onClick={handleImageDelete}>
                            <Icon iconName="Delete" fontSize="small" />
                        </DeleteImgButton>
                    )}
                    <ImageViewer image={mainImage} />
                </MainImgWrapper>
            </MainImgDetails>

            <ImgLabel>Cover Image</ImgLabel>
        </>
    );
};

export default MainImage;
