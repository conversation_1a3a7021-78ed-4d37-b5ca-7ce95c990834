import React, { useState } from 'react';
import { Box, styled, Typography } from '@mui/material/';
import { ObjectType } from '@treez-inc/file-management';
import ImageUploader from './ImageUploader';
import useProduct from '../Hooks/useProduct';
import MainImage from './MainImage';
import ImageDetailsModal from './ImageDetailsModal';
import { ImageDetailsDto } from '../../../interfaces/dto/imageDetails';
import ImageSlider from './ImageSlider';
import DeleteImageModal from './DeleteImageModal';
import { isEmpty } from '../../../utils/common';
import { ProductFormSectionBox } from '../../../styles/globalStyles';

const SubPanel = styled(Box)(({ theme }) => ({
    borderRadius: '16px',
    background: `${theme.palette.grey03.main}`,
    border: `1px solid ${theme.palette.grey04.main}`,
    padding: '16px',
}));

const ProductImagesContainer = () => {
    const { product } = useProduct();
    const [editImage, setEditImage] = useState<ImageDetailsDto>();
    const [deleteImage, setDeleteImage] = useState<ImageDetailsDto>();

    const handleEditImageAction = (img: ImageDetailsDto) => {
        setEditImage(img);
    };

    const handleDeleteImageAction = (img: ImageDetailsDto) => {
        setDeleteImage(img);
    };

    const images = product.images?.filter((i) => !!i.productId && !i.variantId);

    const productImages = images ? [...images].sort((a: any, b: any) => a.order - b.order) : [];

    const uploadImageOrder = () => {
        if (!images || isEmpty(images)) {
            return 1;
        }
        const orders: any[] = images.map((i) => i.order);
        return Math.max(...orders) + 1;
    };

    return (
        <ProductFormSectionBox>
            <Typography variant="h6" data-testid="product-form-images-header">
                Global Product Images
            </Typography>
            <SubPanel>
                {productImages && productImages?.length > 0 && (
                    <>
                        {productImages && (
                            <MainImage
                                mainImage={productImages[0]}
                                onEditImage={handleEditImageAction}
                                onDeleteImage={handleDeleteImageAction}
                            />
                        )}
                        <ImageSlider
                            images={productImages}
                            onEditImage={handleEditImageAction}
                            onDeleteImage={handleDeleteImageAction}
                        />
                        {editImage && (
                            <ImageDetailsModal
                                image={editImage}
                                onClose={() => {
                                    setEditImage(undefined);
                                }}
                            />
                        )}
                        {deleteImage && (
                            <DeleteImageModal
                                image={deleteImage}
                                onClose={() => {
                                    setDeleteImage(undefined);
                                }}
                            />
                        )}
                    </>
                )}
                {product.id && (
                    <ImageUploader
                        objectId={product.id}
                        label={productImages ? 'Add more files' : 'Choose file'}
                        objectType={ObjectType.PRODUCT_IMAGE}
                        order={uploadImageOrder()}
                    />
                )}
            </SubPanel>
        </ProductFormSectionBox>
    );
};

export default ProductImagesContainer;
