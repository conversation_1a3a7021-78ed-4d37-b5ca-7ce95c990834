import React, { useState } from 'react';
import { Box, Typography, styled } from '@mui/material/';
import { ObjectType } from '@treez-inc/file-management';
import { Checkbox, convertPxToRem } from '@treez-inc/component-library';
import ImageUploader from './ImageUploader';
import MainImage from './MainImage';
import ImageSlider from './ImageSlider';
import { ImageDetailsDto } from '../../../interfaces/dto/imageDetails';
import { isEmpty } from '../../../utils/common';
import { ProductFormSectionBox, StyledHeader } from '../../../styles/globalStyles';
import VariantTitle from '../VariantsSection/VariantTitle';
import { SkuDto } from '../../../interfaces/dto/sku';

const SubPanel = styled(Box)(({ theme }) => ({
    borderRadius: '16px',
    background: `${theme.palette.grey03.main}`,
    border: `1px solid ${theme.palette.grey04.main}`,
    padding: '16px',
}));

interface VariantImagesProps {
    variant: SkuDto;
    onEditImage?: (image: ImageDetailsDto) => any;
    onDeleteImage?: (image: ImageDetailsDto) => any;
}

const VariantImages = ({ variant, onEditImage, onDeleteImage }: VariantImagesProps) => {
    const [globalImages, setGlobalImages] = useState<boolean | undefined>(isEmpty(variant.images));
    const mainImage = variant.images?.find((item: any) => item.order === 1);
    const uploadImageOrder = () => {
        if (!variant.images || isEmpty(variant.images)) {
            return 1;
        }
        const orders: any[] = variant.images.map((i) => i.order).filter(Number);
        return Math.max(...orders) + 1;
    };

    return (
        <ProductFormSectionBox>
            <StyledHeader>
                <Typography>
                    <VariantTitle variant={variant} />
                </Typography>
            </StyledHeader>
            {!variant?.images?.length && (
                <Box sx={{ paddingTop: convertPxToRem(10) }}>
                    <Checkbox
                        value={globalImages}
                        label="Use global images"
                        checked={globalImages}
                        onChange={(checked?: boolean) => setGlobalImages(checked)}
                    />
                </Box>
            )}
            {!globalImages && !!variant?.images?.length && (
                <>
                    {mainImage && (
                        <MainImage mainImage={mainImage} onEditImage={onEditImage} onDeleteImage={onDeleteImage} />
                    )}
                    <ImageSlider images={variant.images} onDeleteImage={onDeleteImage} onEditImage={onEditImage} />
                </>
            )}
            {!globalImages && variant && variant.id && (
                <SubPanel>
                    <ImageUploader
                        objectId={variant.id}
                        label={variant.images?.length ? 'Add more files' : 'Choose file'}
                        objectType={ObjectType.PRODUCT_VARIANT_IMAGE}
                        order={uploadImageOrder()}
                    />
                </SubPanel>
            )}
        </ProductFormSectionBox>
    );
};

export default VariantImages;
