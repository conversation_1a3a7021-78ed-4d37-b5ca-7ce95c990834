import React, { useState } from 'react';
import VariantImages from './VariantImages';
import useProduct from '../Hooks/useProduct';
import { ImageDetailsDto } from '../../../interfaces/dto/imageDetails';
import ImageDetailsModal from './ImageDetailsModal';
import DeleteImageModal from './DeleteImageModal';

const VariantImagesContainer = () => {
    const { product } = useProduct();
    const [editImage, setEditImage] = useState<ImageDetailsDto>();
    const [deleteImage, setDeleteImage] = useState<ImageDetailsDto>();

    const handleEditImageAction = (img: ImageDetailsDto) => {
        setEditImage(img);
    };

    const handleDeleteImageAction = (img: ImageDetailsDto) => {
        setDeleteImage(img);
    };

    const variants = product.variants?.map((v) => ({
        ...v,
        images: v.images
            ?.filter((i) => i.variantId === v.id)
            .sort((a: any, b: any) => {
                if (a.order > b.order) return 1;
                if (a.order < b.order) return -1;
                return 0;
            }),
    }));

    return (
        <>
            {variants?.map((variant) => (
                <VariantImages
                    key={variant.id}
                    variant={variant}
                    onDeleteImage={handleDeleteImageAction}
                    onEditImage={handleEditImageAction}
                />
            ))}
            {editImage && (
                <ImageDetailsModal
                    image={editImage}
                    onClose={() => {
                        setEditImage(undefined);
                    }}
                />
            )}
            {deleteImage && (
                <DeleteImageModal
                    image={deleteImage}
                    onClose={() => {
                        setDeleteImage(undefined);
                    }}
                />
            )}
        </>
    );
};

export default VariantImagesContainer;
