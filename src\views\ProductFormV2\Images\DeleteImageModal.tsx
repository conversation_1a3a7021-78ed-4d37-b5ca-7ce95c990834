import React from 'react';
import { Button } from '@treez-inc/component-library';
import { DialogActions, DialogContent, DialogTitle } from '@mui/material/';
import { DialogContentRow, ImagesDialog } from './ImageDetailsModal';
import ImageViewer from './ImageViewer';
import { ImageDetailsDto } from '../../../interfaces/dto/imageDetails';
import { isEmpty } from '../../../utils/common';
import { StyledSubHeader } from '../../../styles/globalStyles';

interface DeleteImageModalProps {
    allImages: ImageDetailsDto[];
    image: ImageDetailsDto;
    onClose: (reason?: any) => void;
    onImageDeleted: (images: ImageDetailsDto[]) => void;
}

const DeleteImageModal = ({ allImages, image, onClose, onImageDeleted }: DeleteImageModalProps) => {
    const handleConfirmation = async () => {
        if (isEmpty(image.id)) {
            const newImages = allImages.filter((img) => img.imageId !== image.imageId);
            onImageDeleted(newImages);
        } else {
            const newImages = allImages.filter((img) => img.id !== image.id);
            newImages.push({ ...image, isDeleted: true });
            const orderedImages = newImages.map((i, order) => ({ ...i, order: order + 1 }));
            onImageDeleted(orderedImages);
        }
        onClose();
    };

    const handleCancel = () => {
        onClose();
    };

    return (
        <ImagesDialog open onClose={onClose} data-testid="delete-product-image">
            <DialogTitle>
                <StyledSubHeader>Remove Image?</StyledSubHeader>
            </DialogTitle>
            <DialogContent>
                <DialogContentRow>
                    <ImageViewer image={image} />
                </DialogContentRow>
                <DialogContentRow>
                    <StyledSubHeader>Please confirm that you would like to remove this image.</StyledSubHeader>
                </DialogContentRow>
            </DialogContent>
            <DialogActions>
                <>
                    <Button label="No" variant="secondary" onClick={handleCancel} />
                    <Button label="Yes" variant="primary" onClick={handleConfirmation} />
                </>
            </DialogActions>
        </ImagesDialog>
    );
};

export default DeleteImageModal;
