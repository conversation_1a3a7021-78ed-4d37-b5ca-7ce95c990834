import React from 'react';
import { Box, Dialog, DialogActions, DialogContent, DialogTitle, styled } from '@mui/material/';
import { convertPxToRem } from '@treez-inc/component-library';
import { ImageDetailsDto } from '../../../interfaces/dto/imageDetails';
import ImageViewer from './ImageViewer';
import useReactHookForm from '../../../hooks/useReactHookForm';
import ReactHookForm from '../../../components/hook-form-v2/ReactHookForm';
import HookFormInput from '../../../components/hook-form-v2/HookFormInput';
import HookFormCheckbox from '../../../components/hook-form-v2/HookFormCheckbox';
import { StyledPrimaryButton } from '../../../styles/StyledProductForm';
import { StyledHeader } from '../../../styles/globalStyles';
import { pushOrUpdateItem } from './ImageUtils';

export const ImagesDialog = styled(Dialog)(() => ({
    '.MuiDialog-paper': {
        borderRadius: convertPxToRem(28),
        width: convertPxToRem(606),
    },
}));

export const DialogContentRow = styled(Box)(() => ({
    padding: `${convertPxToRem(10)} 0`,
    textAlign: 'center',
    fontSize: convertPxToRem(15),
    fontStyle: 'normal',
    fontWeight: 500,
}));

interface ImageDetailsModalProps {
    allImages: ImageDetailsDto[];
    image: ImageDetailsDto;
    onClose: () => void;
    onImageUpdated: (images: ImageDetailsDto[]) => void;
}

const ImageDetailsModal = ({ allImages, image, onClose, onImageUpdated }: ImageDetailsModalProps) => {
    // const { product, setProductUpdate } = useProduct();

    const form = useReactHookForm<ImageDetailsDto>({
        values: {
            ...image,
        },
    });

    const hadChanges = (values: any) =>
        !values.id || values.default || image.name !== values.name || image.description !== values.description;

    const handleSubmit = async (values: any) => {
        if (hadChanges(values)) {
            const images: ImageDetailsDto[] = pushOrUpdateItem(allImages, values);
            const orderedImages = images.map((i, order) => ({ ...i, order: order + 1 }));
            onImageUpdated(orderedImages);
        }
        onClose();
    };

    const getTitle = () => (image.id ? `Update Image` : `Create Image`);

    return (
        <ImagesDialog open onClose={onClose}>
            <ReactHookForm formName="VariantInfoForm" onSubmit={handleSubmit} formContextProps={form}>
                <DialogTitle>
                    <StyledHeader>{getTitle()}</StyledHeader>
                </DialogTitle>
                <DialogContent>
                    <DialogContentRow>
                        <ImageViewer image={image} />
                    </DialogContentRow>
                    <DialogContentRow>
                        <HookFormInput name="name" label="Title" />
                    </DialogContentRow>
                    <DialogContentRow>
                        <HookFormInput name="description" label="Description" />
                    </DialogContentRow>
                    {image.order !== 1 && (
                        <DialogContentRow>
                            <HookFormCheckbox name="default" label="Make Default" />
                        </DialogContentRow>
                    )}
                </DialogContent>
                <DialogActions>
                    <StyledPrimaryButton
                        name="Save and Continue"
                        type="button"
                        data-testid="next-section-button"
                        onClick={() => form.handleSubmit(handleSubmit)()}
                    >
                        OK
                    </StyledPrimaryButton>
                </DialogActions>
            </ReactHookForm>
        </ImagesDialog>
    );
};

export default ImageDetailsModal;
