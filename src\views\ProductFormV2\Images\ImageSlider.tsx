import React from 'react';
import { Box, IconButton, styled } from '@mui/material/';
import { convertPxToRem, Icon } from '@treez-inc/component-library';
import { ImageDetailsDto } from '../../../interfaces/dto/imageDetails';
import ImageViewer from './ImageViewer';

const ImgSlider = styled(Box)(() => ({
    display: 'flex',
    flexDirection: 'row',
    overflowX: 'auto',
}));

const ImgThumbnailWrapper = styled(Box)(() => ({
    display: 'inline-flex',
    flexDirection: 'column',
    margin: `${convertPxToRem(5)} ${convertPxToRem(15)}`,
    width: convertPxToRem(70),
    cursor: 'grab',
}));

const ImgThumbnail = styled(Box)(({ theme }) => ({
    border: `${convertPxToRem(1)} solid ${theme.palette.grey04.main}`,
    borderRadius: convertPxToRem(16),
    cursor: 'pointer',
    height: convertPxToRem(64),
    position: 'relative',
    width: convertPxToRem(64),
    '&:hover': {
        '.imgIconBtn': {
            display: 'inline-flex',
        },
    },
}));

const EditImgButton = styled(IconButton)(({ theme }) => ({
    border: `1px solid ${theme.palette.grey04.main}`,
    background: `${theme.palette.primaryWhite.main}`,
    display: 'none',
    height: '24px',
    overflow: 'hidden',
    position: 'absolute',
    right: '30px',
    top: '-8px',
    width: '24px',

    '&:hover': {
        background: `${theme.palette.green03.main}`,
    },
}));

const DeleteImgButton = styled(IconButton)(({ theme }) => ({
    border: `1px solid ${theme.palette.grey04.main}`,
    background: `${theme.palette.primaryWhite.main}`,
    display: 'none',
    height: '24px',
    overflow: 'hidden',
    position: 'absolute',
    right: '3px',
    top: '-8px',
    width: '24px',

    '&:hover': {
        background: `${theme.palette.green03.main}`,
    },
}));

const ImgLabel = styled(Box)(({ theme }) => ({
    color: `${theme.palette.primaryBlack.main}`,
    fontSize: convertPxToRem(12),
    fontStyle: 'normal',
    fontWeight: '400',
    lineHeight: convertPxToRem(16),
    padding: convertPxToRem(5),
    textAlign: 'center',
}));

interface ImageSliderProps {
    images: ImageDetailsDto[];
    onEditImage?: (image: ImageDetailsDto) => any;
    onDeleteImage?: (image: ImageDetailsDto) => any;
    onOrderChange?: (images: ImageDetailsDto[]) => any;
}

const ImageSlider = ({ images, onEditImage, onDeleteImage, onOrderChange }: ImageSliderProps) => {
    const handleImageEdit = (imageDetails: ImageDetailsDto) => {
        onEditImage?.(imageDetails);
    };

    const handleImageDelete = (imageDetails: ImageDetailsDto) => {
        onDeleteImage?.(imageDetails);
    };

    const handleDragStart = (event: React.DragEvent, index: number) => {
        event.dataTransfer.setData('draggedIndex', index.toString());
    };

    const moveElement = (items: any[], fromIndex: number, toIndex: number) => {
        if (fromIndex < 0 || fromIndex >= items.length || toIndex < 0 || toIndex >= items.length) {
            return items;
        }

        const element = items.splice(fromIndex, 1)[0];
        items.splice(toIndex, 0, element);
        return items;
    };

    const handleDrop = (event: React.DragEvent, dropIndex: number) => {
        const draggedIndex = parseInt(event.dataTransfer.getData('draggedIndex'), 10);

        if (draggedIndex === dropIndex) return;

        const updatedList = moveElement([...images], draggedIndex, dropIndex);
        const orderedImages = updatedList.map((img, i) => ({ ...img, order: i + 1 }));
        onOrderChange?.([...orderedImages]);
    };

    const handleDragOver = (event: React.DragEvent) => {
        event.preventDefault();
    };

    const imagesToDisplay = images
        ?.filter((img) => !img.isDeleted)
        .sort((img1: ImageDetailsDto, img2: ImageDetailsDto) =>
            img1.order && img2.order && img1.order > img2.order ? 1 : -1,
        );

    return (
        <ImgSlider>
            {imagesToDisplay?.map((imageDetails: ImageDetailsDto, index) => (
                <ImgThumbnailWrapper
                    key={imageDetails.imageId}
                    draggable
                    onDragStart={(e) => handleDragStart(e, index)}
                    onDrop={(e) => handleDrop(e, index)}
                    onDragOver={handleDragOver}
                >
                    <ImgThumbnail>
                        <EditImgButton className="imgIconBtn" onClick={() => handleImageEdit(imageDetails)}>
                            <Icon iconName="Edit" fontSize="small" />
                        </EditImgButton>
                        {!imageDetails.verifiedReferenceId && (
                            <DeleteImgButton className="imgIconBtn" onClick={() => handleImageDelete(imageDetails)}>
                                <Icon iconName="Delete" fontSize="small" />
                            </DeleteImgButton>
                        )}
                        <ImageViewer image={imageDetails} />
                    </ImgThumbnail>
                    <ImgLabel>{imageDetails.name}</ImgLabel>
                </ImgThumbnailWrapper>
            ))}
        </ImgSlider>
    );
};

export default ImageSlider;
