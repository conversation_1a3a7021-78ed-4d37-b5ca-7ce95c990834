import React, { useState } from 'react';
import { CircularProgress, FileUploadButton } from '@treez-inc/component-library';
import {
    Stage,
    UploadFileResult,
    ValidateFileResult,
} from '@treez-inc/component-library/dist/components/FileUpload/types';
import { ContentType, ObjectType } from '@treez-inc/file-management';
import constants from '../../../utils/constants';
import useSnackbarContext from '../../../hooks/snackbar/useSnackbarContext';
import { getAccessToken } from '../../../api/genericAccessor';
import { ImageDetailsDto } from '../../../interfaces/dto/imageDetails';

interface ImageUploaderProps {
    objectId: string;
    objectType: ObjectType;
    label: string;
    order: number;
    onNewImageCreated: (image: ImageDetailsDto) => void;
}

const getStage = (): Stage => {
    switch (constants.STAGE) {
        case 'sandbox':
            return Stage.SANDBOX;
        case 'dev':
            return Stage.DEV;
        case 'prod':
            return Stage.PROD;
        default:
            return Stage.LOCAL;
    }
};

const ImageUploader = ({ objectId, objectType, label, order, onNewImageCreated }: ImageUploaderProps) => {
    const { setSnackbar } = useSnackbarContext();
    const [isImgUploading, setIsImgUploading] = useState(false);

    const onFilesChanged = () => {
        setIsImgUploading(true);
    };

    const onFileValidated = (result: ValidateFileResult) => {
        if (!result.isValid) {
            setSnackbar({
                message: result.validationErrors[0],
                severity: 'warning',
                iconName: 'Warning',
            });
            setIsImgUploading(false);
        }
    };

    const onUploadFinished = (result: UploadFileResult) => {
        if (!result.isSuccess) {
            setSnackbar({
                message: `${result.error}`,
                severity: 'warning',
                iconName: 'Warning',
            });
        } else if (result.isSuccess) {
            setSnackbar({
                message: `Image has been uploaded successfully`,
                severity: 'info',
                iconName: 'Success',
            });

            // calculate order

            const imgId = result?.fileId as string;

            onNewImageCreated({
                imageId: imgId,
                order,
            });
        }
        setIsImgUploading(false);
    };

    return (
        <>
            {!isImgUploading && (
                <FileUploadButton
                    authToken={getAccessToken()}
                    contentTypes={[ContentType.JPEG, ContentType.JPG, ContentType.PNG]}
                    label={label}
                    maxFileSizeInMegaBytes={10}
                    objectId={objectId}
                    objectType={objectType}
                    onFilesChanged={onFilesChanged}
                    onFileValidated={onFileValidated}
                    onUploadFinished={onUploadFinished}
                    stage={getStage()}
                    showContentTypes
                    showMaxFileSize
                    testId="file-upload-component"
                />
            )}
            {isImgUploading && <CircularProgress />}
        </>
    );
};

export default ImageUploader;
