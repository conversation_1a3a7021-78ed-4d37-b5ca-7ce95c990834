import { ImageDetailsDto } from '../../../interfaces/dto/imageDetails';
import { isEmpty } from '../../../utils/common';

export const hasImageChanges = (image1?: ImageDetailsDto, image2?: ImageDetailsDto) => {
    if (image1 && image2) {
        return (
            image2.order !== image1.order || image2.name !== image1.name || image2.description !== image1.description
        );
    }
    return false;
};

const getImage = (images: ImageDetailsDto[], id?: string) => images?.find((i) => i.id === id);

export const getImageMutationData = (
    formImages: ImageDetailsDto[],
    currentImages: ImageDetailsDto[],
    productId: string,
) => {
    const createData = formImages?.filter((i) => !i.id && !i.isDeleted).map((img) => ({ ...img, productId }));
    const updateData = formImages
        ?.filter((img) => img.id && !img.isDeleted && hasImageChanges(img, getImage(currentImages, img.id)))
        .map((img) => ({
            ...img,
            description: img.description ? img.description : undefined,
            variantId: undefined,
            productId,
            name: img.name ? img.name : undefined,
        }));

    const deletedImages = formImages?.filter((img) => img.isDeleted);

    return {
        createData: !isEmpty(createData) ? createData : undefined,
        updateData: !isEmpty(updateData) ? updateData : undefined,
        deleteData: !isEmpty(deletedImages) && deletedImages ? { ids: deletedImages.map((i) => i.id) } : undefined,
    };
};

// eslint-disable-next-line import/prefer-default-export
export const mergeArraysById = (array1: ImageDetailsDto[], array2: ImageDetailsDto[]): ImageDetailsDto[] => {
    const mergedArrayMap = new Map<string, ImageDetailsDto>();

    array1.forEach((item) => {
        if (item.id) {
            mergedArrayMap.set(item.id, item);
        }
    });
    array2.forEach((item) => {
        if (item.id) {
            mergedArrayMap.set(item.id, item);
        }
    });

    return Array.from(mergedArrayMap.values());
};

export const pushOrUpdateItem = (allimages: ImageDetailsDto[], img: any): ImageDetailsDto[] => {
    const index = allimages?.findIndex(
        (existingItem) =>
            (existingItem.id && img.id && existingItem.id === img.id) || existingItem.imageId === img.imageId,
    );
    const updatedImages = allimages ? [...allimages] : [];
    if (index !== undefined && index !== -1) {
        updatedImages[index] = img;
    } else {
        updatedImages.push(img);
    }

    if (img.default) {
        const defaultIndex = updatedImages.findIndex((item: any) => item.default);
        if (defaultIndex !== -1) {
            const defaultItem = updatedImages.splice(defaultIndex, 1)[0];
            updatedImages.unshift(defaultItem);
        }
    }

    return updatedImages;
};
