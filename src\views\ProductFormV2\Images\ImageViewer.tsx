import React from 'react';
import { styled } from '@mui/material/';
import { convertPxToRem } from '@treez-inc/component-library';
import { ImageDetailsDto } from '../../../interfaces/dto/imageDetails';
import buildImageUrl from '../../../utils/images';
import { useUserPermissionsContext } from '../../../permissions/userPermissions.context';

const Image = styled('img')(() => ({
    borderRadius: convertPxToRem(16),
    width: '100%',
    height: '100%',
}));

interface ImageViewerProps {
    image: ImageDetailsDto;
}

const ImageViewer = ({ image }: ImageViewerProps) => {
    const { userOrgId } = useUserPermissionsContext();
    const imageUrl = image?.imageId ? buildImageUrl(userOrgId, image?.imageId) : image.imageUrl;
    return <Image data-testid="product-image" src={imageUrl} alt={image?.name} />;
};

export default ImageViewer;
