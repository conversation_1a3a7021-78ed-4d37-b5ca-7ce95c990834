import React, { useState } from 'react';
import { Box, styled } from '@mui/material/';
import { ObjectType } from '@treez-inc/file-management';
import { Checkbox, convertPxToRem } from '@treez-inc/component-library';
import ImageUploader from './ImageUploader';
import ImageSlider from './ImageSlider';
import { ImageDetailsDto } from '../../../interfaces/dto/imageDetails';
import { isEmpty } from '../../../utils/common';

const SubPanel = styled(Box)(({ theme }) => ({
    borderRadius: '16px',
    background: `${theme.palette.grey03.main}`,
    // border: `1px solid ${theme.palette.grey04.main}`,
    padding: '16px',
}));

interface VariantImagesProps {
    objectId: string | undefined;
    objectType: ObjectType;
    images: ImageDetailsDto[];
    hideGlobalImageCheckbox?: boolean;
    onEditImage?: (image: ImageDetailsDto) => any;
    onDeleteImage?: (image: ImageDetailsDto) => any;
    onNewImageCreated: (image: ImageDetailsDto) => void;
    onOrderChange?: (images: ImageDetailsDto[]) => any;
}

const Images = ({
    objectId,
    images,
    objectType,
    hideGlobalImageCheckbox,
    onEditImage,
    onDeleteImage,
    onNewImageCreated,
    onOrderChange,
}: VariantImagesProps) => {
    const [globalImages, setGlobalImages] = useState<boolean | undefined>(isEmpty(images) && !hideGlobalImageCheckbox);
    const uploadImageOrder = () => {
        if (!images || isEmpty(images)) {
            return 1;
        }
        const orders: any[] = images.map((i) => i.order).filter(Number);
        return Math.max(...orders) + 1;
    };

    return (
        <>
            {!images?.length && !hideGlobalImageCheckbox && (
                <Box sx={{ paddingTop: convertPxToRem(10) }}>
                    <Checkbox
                        value={globalImages}
                        label="Use global images"
                        checked={globalImages}
                        onChange={(checked?: boolean) => setGlobalImages(checked)}
                    />
                </Box>
            )}
            {!globalImages && (
                <>
                    <ImageSlider
                        images={images}
                        onDeleteImage={onDeleteImage}
                        onEditImage={onEditImage}
                        onOrderChange={onOrderChange}
                    />
                </>
            )}
            {!globalImages && objectId && (
                <SubPanel>
                    <ImageUploader
                        objectId={objectId}
                        label={images?.length ? 'Add more files' : 'Choose file'}
                        objectType={objectType}
                        order={uploadImageOrder()}
                        onNewImageCreated={onNewImageCreated}
                    />
                </SubPanel>
            )}
        </>
    );
};

export default Images;
