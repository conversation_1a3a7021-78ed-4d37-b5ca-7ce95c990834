import React, { useState } from 'react';
import { useFormContext } from 'react-hook-form';
import { Box, styled, Typography } from '@mui/material';
import { ObjectType } from '@treez-inc/file-management';
import { convertPxToRem } from '@treez-inc/component-library';
import Images from './Images';
import { ImageDetailsDto } from '../../../interfaces/dto/imageDetails';
import ImageDetailsModal from './ImageDetailsModal';
import DeleteImageModal from './DeleteImageModal';

const ImagesHeader = styled(Typography)(({ theme }) => ({
    color: theme.palette.primaryBlackText.main,
    fontSize: convertPxToRem(12),
    fontWeight: 500,
    lineHeight: convertPxToRem(16),
}));

export const ImageFormSectionBox = styled(Box)(({ theme }) => ({
    backgroundColor: theme.palette.primaryWhite.main,
    display: 'flex',
    flexDirection: 'column',
    fontFamily: 'Roboto',
    rowGap: convertPxToRem(4),
}));

interface ImagesContainerProps {
    fieldName: string;
    objectId: string | undefined;
    objectType: ObjectType;
    hideGlobalImageCheckbox?: boolean;
}

const ImagesContainer = ({ fieldName, objectId, objectType, hideGlobalImageCheckbox }: ImagesContainerProps) => {
    const { setValue, getValues, watch } = useFormContext();
    const [editImage, setEditImage] = useState<ImageDetailsDto>();
    const [deleteImage, setDeleteImage] = useState<ImageDetailsDto>();
    const formImages = watch(fieldName);

    const handleEditImageAction = (img: ImageDetailsDto) => {
        setEditImage(img);
    };

    const handleDeleteImageAction = (img: ImageDetailsDto) => {
        setDeleteImage(img);
    };

    const handleImageEdit = (images: ImageDetailsDto[]) => {
        setValue(fieldName, images, { shouldDirty: true });
    };

    const handleImageDelete = (images: ImageDetailsDto[]) => {
        setValue(fieldName, images, { shouldDirty: true });
    };

    const handleImageOrderChange = (images: ImageDetailsDto[]) => {
        setValue(fieldName, images, { shouldDirty: true });
    };

    return (
        <ImageFormSectionBox>
            <ImagesHeader>IMAGES</ImagesHeader>
            <Images
                objectId={objectId}
                objectType={objectType}
                hideGlobalImageCheckbox={hideGlobalImageCheckbox}
                images={formImages}
                onDeleteImage={handleDeleteImageAction}
                onEditImage={handleEditImageAction}
                onNewImageCreated={handleEditImageAction}
                onOrderChange={handleImageOrderChange}
            />
            {editImage && (
                <ImageDetailsModal
                    allImages={formImages}
                    image={editImage}
                    onClose={() => {
                        setEditImage(undefined);
                    }}
                    onImageUpdated={handleImageEdit}
                />
            )}
            {deleteImage && (
                <DeleteImageModal
                    image={deleteImage}
                    allImages={getValues(fieldName)}
                    onClose={() => {
                        setDeleteImage(undefined);
                    }}
                    onImageDeleted={handleImageDelete}
                />
            )}
        </ImageFormSectionBox>
    );
};

export default ImagesContainer;
