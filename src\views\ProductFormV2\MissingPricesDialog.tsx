import React from 'react';
import { Typography } from '@mui/material/';
import { Modal } from '@treez-inc/component-library';
import { PageNavigationOption } from './Types/NavigationOption';
import { PageName } from './Types/Navigation.enum';
import useProductTabNavigation from './Hooks/useProductTabNavigation';

interface DialogProps {
    isIncompleteDialogOpen: boolean;
    setIncompleteDialogOpen: (val: boolean) => void;
}

export default function MissingPricesDialog({ isIncompleteDialogOpen, setIncompleteDialogOpen }: DialogProps) {
    const { navigate } = useProductTabNavigation();

    const handleClose = () => {
        setIncompleteDialogOpen(false);
    };

    const handleNavigate = () => {
        navigate(new PageNavigationOption(PageName.PRODUCT_CONTROL_PAGE));
    };

    return (
        <Modal
            testId="product-form-exit-modal"
            title="Incomplete Pricing Alert"
            content={
                <Typography>
                    Some pricing details for this product are missing. You can fill them in now or skip for the moment
                    and complete them later in the Pricing Management section.
                </Typography>
            }
            onClose={handleClose}
            open={isIncompleteDialogOpen}
            primaryButton={{ label: 'Complete now', onClick: handleClose }}
            secondaryButton={{ label: 'Skip for now', onClick: handleNavigate }}
        />
    );
}
