import React from 'react';
import { Modal } from '@treez-inc/component-library';
import { Box } from '@mui/material';
import { MutationErrorData } from '../../utils/MutationResponseUtil';
import { BaseNavigationOption, IgnoreNavigationOption } from './Types/NavigationOption';

interface MutationErrorDialogProps {
    errors: MutationErrorData[];
    navigate: BaseNavigationOption;
    isBlockingError: boolean;
    onNavigate: (submit: boolean, nav: BaseNavigationOption) => void;
}

const getMessage = (errors: MutationErrorData[]) =>
    errors.map((error) => (
        <Box key={`${error.title}-${Math.random().toFixed(4)}`}>
            <h3>{error.title}</h3>
            {error.errors.map((childError) => (
                <Box key={`${childError.entityName}-${Math.random().toFixed(4)}`}>
                    {childError.entityName && <h4>{childError.entityName}:</h4>}
                    <>
                        {childError.messages?.map((msg) => (
                            <p key={`${msg}-${Math.random().toFixed(4)}`}>
                                {msg}
                                <br />
                            </p>
                        ))}
                    </>
                </Box>
            ))}
        </Box>
    ));

export default function MutationErrorDialog({
    errors,
    isBlockingError,
    navigate,
    onNavigate,
}: MutationErrorDialogProps) {
    const handleClose = () => {
        onNavigate(false, new IgnoreNavigationOption());
    };
    const handleContinue = () => {
        onNavigate(false, navigate);
    };

    if (isBlockingError) {
        return (
            <Modal
                testId="product-form-exit-modal"
                title="Error Saving Data"
                content={getMessage(errors)}
                onClose={handleClose}
                open={errors && errors.length > 0}
                primaryButton={{ label: 'Stay', onClick: handleClose }}
            />
        );
    }

    return (
        <Modal
            testId="product-form-exit-modal"
            title="Error Saving Data"
            content={getMessage(errors)}
            onClose={handleClose}
            open={errors && errors.length > 0}
            primaryButton={{ label: 'Skip Error', onClick: handleContinue }}
            secondaryButton={{ label: 'Stay', onClick: handleClose }}
        />
    );
}
