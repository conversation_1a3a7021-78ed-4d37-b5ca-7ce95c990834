import React from 'react';
import { Box, Paper, styled } from '@mui/material';
import convertPxToRem from '@treez-inc/component-library/dist/util-functions/convertPxToRem';
import { Icon } from '@treez-inc/component-library';
import { StyledPrimaryButton, StyledSecondaryButton } from '../../styles/StyledProductForm';
import useProductTabNavigation from './Hooks/useProductTabNavigation';
import useProduct from './Hooks/useProduct';
import TabName from './Types/TabNames.enum';
import { TabNavigation, PageName } from './Types/Navigation.enum';
import {
    BaseNavigationOption,
    TabNavigationOption,
    PageNavigationOption,
    IgnoreNavigationOption,
} from './Types/NavigationOption';
import Mode from './Types/Mode';
import { ensureFunction } from '../../utils/common';

const PreviousButtonRow = styled(Box)(() => ({
    float: 'left',
    padding: convertPxToRem(16),
    marginLeft: '14%',
}));

const NextButtonRow = styled(Box)(() => ({
    float: 'right',
    padding: convertPxToRem(16),
    marginRight: '12%',
}));

const SaveButtonRow = styled(Box)(() => ({
    float: 'right',
    padding: convertPxToRem(16),
    marginRight: '2px',
    paddingRight: '2px',
}));

const PaperStyled = styled(Paper)(() => ({
    position: 'fixed',
    bottom: convertPxToRem(0),
    left: convertPxToRem(0),
    right: convertPxToRem(0),
    zIndex: 99,
}));

interface ProductFormButtonsProps {
    onNavigate: (submit: boolean, navigate: BaseNavigationOption) => any;
    isBusy?: boolean;
    hasMissingPrices?: boolean;
    isCustomPriceDirty?: boolean;
    setIncompleteDialogOpen?: (s: boolean) => void;
    enableNavigation?: boolean;
}

export default function ProductFormButtons({
    onNavigate,
    isBusy,
    hasMissingPrices,
    isCustomPriceDirty,
    setIncompleteDialogOpen,
    enableNavigation = true,
}: ProductFormButtonsProps) {
    const { product, mode } = useProduct();
    const { tabs, isTabDirty, navigate } = useProductTabNavigation();

    const getNext = () => {
        const nextIndex = tabs.findIndex((t) => t.isSelected) + 1;
        return nextIndex < tabs.length ? tabs[nextIndex] : undefined;
    };
    const getCurrent = () => tabs.find((t) => t.isSelected);

    const getPrev = () => {
        const prevIndex = tabs.findIndex((t) => t.isSelected) - 1;
        return prevIndex >= 0 ? tabs[prevIndex] : undefined;
    };

    const handleSubmitAndNext = async () => {
        const currentTab = getCurrent();
        if (isTabDirty || (currentTab?.tab === TabName.PRODUCT_INFO && !product.id)) {
            onNavigate(true, new TabNavigationOption(TabNavigation.SELECT, getNext()?.tab));
        } else {
            onNavigate(false, new TabNavigationOption(TabNavigation.SELECT, getNext()?.tab));
        }
    };

    const handleNext = async () => {
        onNavigate(false, new TabNavigationOption(TabNavigation.SELECT, getNext()?.tab));
    };

    const handleSave = async () => {
        const currentTab = getCurrent();
        if (isTabDirty || (currentTab?.tab === TabName.PRODUCT_INFO && !product.id)) {
            onNavigate(true, new IgnoreNavigationOption());
        } else {
            onNavigate(false, new IgnoreNavigationOption());
        }
    };

    const handleSubmitAndClose = async () => {
        const currentTab = getCurrent();
        if (isTabDirty || (currentTab?.tab === TabName.PRODUCT_INFO && !product.id)) {
            onNavigate(true, new PageNavigationOption(PageName.PRODUCT_CONTROL_PAGE));
        } else {
            onNavigate(false, new PageNavigationOption(PageName.PRODUCT_CONTROL_PAGE));
        }
    };

    const handlePrevNavigation = async () => {
        navigate(new TabNavigationOption(TabNavigation.SELECT, getPrev()?.tab));
    };

    const handleClose = () => {
        navigate(new PageNavigationOption(PageName.PRODUCT_CONTROL_PAGE));
    };

    const handleFinish = () => {
        const safeSetIncompleteDialogOpen = ensureFunction(setIncompleteDialogOpen);

        if (hasMissingPrices) {
            safeSetIncompleteDialogOpen(true);
        } else {
            onNavigate(true, new PageNavigationOption(PageName.PRODUCT_CONTROL_PAGE, true));
        }
    };

    const handleCustomPriceSave = async () => {
        const currentTab = getCurrent();
        if (isTabDirty || isCustomPriceDirty || (currentTab?.tab === TabName.PRODUCT_INFO && !product.id)) {
            onNavigate(true, new IgnoreNavigationOption());
        } else {
            onNavigate(false, new IgnoreNavigationOption());
        }
    };

    if (mode === Mode.CREATE && getCurrent()?.label !== 'Pricing') {
        return (
            <PaperStyled elevation={3}>
                {getPrev() ? (
                    <PreviousButtonRow>
                        <StyledSecondaryButton
                            type="button"
                            name="Previous"
                            data-testid="previous-section-button"
                            onClick={handlePrevNavigation}
                            disabled={isBusy}
                            startIcon={<Icon iconName="ChevronLeft" fontSize="medium" color="green10" />}
                        >
                            {getPrev()?.label}
                        </StyledSecondaryButton>
                    </PreviousButtonRow>
                ) : (
                    <PreviousButtonRow>
                        <StyledSecondaryButton
                            type="button"
                            name="Previous"
                            data-testid="previous-section-button"
                            onClick={handleClose}
                            disabled={isBusy}
                            startIcon={<Icon iconName="ChevronLeft" fontSize="medium" color="green10" />}
                        >
                            Close
                        </StyledSecondaryButton>
                    </PreviousButtonRow>
                )}

                {getNext() || (getCurrent()?.tab === TabName.PRODUCT_INFO && !product.id) ? (
                    <>
                        <NextButtonRow>
                            <StyledPrimaryButton
                                name="Save and Next"
                                type="button"
                                data-testid="next-section-button"
                                disabled={isBusy}
                                onClick={handleSubmitAndNext}
                            >
                                {isTabDirty ? 'Save And Next' : 'Next'}
                            </StyledPrimaryButton>
                        </NextButtonRow>
                    </>
                ) : (
                    <>
                        <NextButtonRow>
                            <StyledPrimaryButton
                                name="Save and Close"
                                type="button"
                                data-testid="next-section-button"
                                onClick={handleSubmitAndClose}
                                disabled={isBusy}
                            >
                                {isTabDirty || (getCurrent()?.tab === TabName.PRICING && !product.id)
                                    ? 'Save and Close'
                                    : 'Close'}
                            </StyledPrimaryButton>
                        </NextButtonRow>
                    </>
                )}
            </PaperStyled>
        );
    }

    return enableNavigation ? (
        <PaperStyled elevation={3}>
            {getPrev() ? (
                <PreviousButtonRow>
                    <StyledSecondaryButton
                        type="button"
                        name="Previous"
                        data-testid="previous-section-button"
                        onClick={handlePrevNavigation}
                        disabled={isBusy}
                        startIcon={<Icon iconName="ChevronLeft" fontSize="medium" color="green10" />}
                    >
                        {getPrev()?.label}
                    </StyledSecondaryButton>
                </PreviousButtonRow>
            ) : (
                <PreviousButtonRow>
                    <StyledSecondaryButton
                        type="button"
                        name="Previous"
                        data-testid="previous-section-button"
                        onClick={handleClose}
                        disabled={isBusy}
                        startIcon={<Icon iconName="ChevronLeft" fontSize="medium" color="green10" />}
                    >
                        Close
                    </StyledSecondaryButton>
                </PreviousButtonRow>
            )}

            {getNext() || (getCurrent()?.tab === TabName.PRODUCT_INFO && !product.id) ? (
                <>
                    <NextButtonRow>
                        <StyledPrimaryButton
                            name="Next"
                            type="button"
                            data-testid="next-section-button"
                            disabled={isBusy}
                            onClick={handleNext}
                        >
                            Next
                        </StyledPrimaryButton>
                    </NextButtonRow>
                    <SaveButtonRow>
                        <StyledPrimaryButton
                            name="Save"
                            type="button"
                            data-testid="save-section-button"
                            disabled={isBusy || !isTabDirty}
                            onClick={handleSave}
                        >
                            Save
                        </StyledPrimaryButton>
                    </SaveButtonRow>
                </>
            ) : (
                <>
                    <NextButtonRow>
                        <StyledPrimaryButton
                            name="saveDone"
                            type="button"
                            data-testid="next-section-button"
                            onClick={handleFinish}
                            disabled={isBusy}
                        >
                            Save and Done
                        </StyledPrimaryButton>
                    </NextButtonRow>
                    <SaveButtonRow>
                        <StyledPrimaryButton
                            name="Save"
                            type="button"
                            data-testid="save-section-button"
                            disabled={isBusy || !isCustomPriceDirty}
                            onClick={handleCustomPriceSave}
                        >
                            Save
                        </StyledPrimaryButton>
                    </SaveButtonRow>
                </>
            )}
        </PaperStyled>
    ) : null;
}
