import React from 'react';
import { Box, Grid, IconButton, Typography, styled } from '@mui/material';
import convertPxToRem from '@treez-inc/component-library/dist/util-functions/convertPxToRem';
import { ChevronLeft, RemoveRedEyeOutlined } from '@mui/icons-material';
import useProduct from './Hooks/useProduct';
import { GlobalBadge } from '../../styles/globalStyles';
import { isEmpty } from '../../utils/common';

const SaveCloseText = styled(Typography)`
    color: #595959;
    font-size: 0.875em;
    font-weight: 400;
    line-height: 1.25rem;
`;

const ProductNameText = styled(Typography)`
    font-size: 1.5em;
    font-weight: 400;
`;

const ProductCategoryText = styled(Typography)`
    font-size: 0.875em;
    font-weight: 400;
`;

const ProductNameAndImageBox = styled(Box)`
    display: flex;
    flex-direction: row;
    grid-gap: 1em;
`;

const ProductNameBox = styled(Box)`
    display: flex;
    flex-direction: column;
`;

const HeaderBox = styled(Grid)`
    display: flex;
    flex-direction: row;
    width: 100%;
    padding: 1.5rem 2rem 1rem 2rem;
`;

const CloseBox = styled(Grid)`
    display: flex;
    flex-direction: row;
    align-items: center;
    flex-grow: 1;
    cursor: pointer;
`;

const ProductMainInfoBox = styled(Grid)`
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    flex-grow: 4;
    padding: 0 0 0 ${convertPxToRem(5)} !important;
`;

export interface ProductFormHeaderProps {
    productId?: string;
    isLoadingCompleted: boolean;
    onCloseClick: () => void;
    onPreviewClick: () => void;
}

export default function ProductFormHeader({
    productId,
    isLoadingCompleted,
    onCloseClick,
    onPreviewClick,
}: ProductFormHeaderProps) {
    const { product } = useProduct();

    const getTitle = () => {
        if (!productId && isEmpty(product.name)) {
            return 'New Product';
        }
        return product.name;
    };

    return (
        <HeaderBox container>
            <CloseBox onClick={onCloseClick} item md={1.5}>
                <IconButton>
                    <ChevronLeft />
                </IconButton>
                <SaveCloseText>Close</SaveCloseText>
            </CloseBox>
            <ProductMainInfoBox item>
                <ProductNameAndImageBox>
                    <ProductNameBox>
                        <ProductNameText data-testid="product-heading">
                            {getTitle()}{' '}
                            {isLoadingCompleted && product.verifiedReferenceId && <GlobalBadge fontSize="small" />}
                        </ProductNameText>
                        {product.productCategory && (
                            <ProductCategoryText>{product.productCategory.name}</ProductCategoryText>
                        )}
                    </ProductNameBox>
                </ProductNameAndImageBox>
            </ProductMainInfoBox>
            {false && (
                <CloseBox onClick={onPreviewClick} item md={1.5}>
                    <IconButton>
                        <RemoveRedEyeOutlined />
                    </IconButton>
                    <SaveCloseText>Preview</SaveCloseText>
                </CloseBox>
            )}
        </HeaderBox>
    );
}
