import React from 'react';
import { Grid, styled } from '@mui/material/';
import FormTabs from './FormTabs';
import FormTabContent from './FormTabContent';
import { ProductCategoryDto } from '../../interfaces/dto/productCategory';
import LoadingIndicator from '../../components/LoadingIndicator';

const StyledFormMainContainer = styled(Grid)(() => ({
    justifyContent: 'center',
    margin: 0,
}));

const StyledStepperGrid = styled(Grid)(() => ({
    padding: '0 !important',
}));

const GridStyled = styled(Grid)`
    overflow-x: hidden;
    overflow-y: auto;
    margin-bottom: 5em !important;
    padding-top: 0 !important;
    padding-bottom: 1em !important;
    scrollbar-width: none;
    -ms-overflow-style: none;
    &::-webkit-scrollbar {
        width: 0;
        height: 0;
    }
`;

interface ProductContentAreaProps {
    categories: ProductCategoryDto[];
    isProductLoading: boolean;
    isLoadingCompleted: boolean;
}

const ProductForms = ({ categories, isProductLoading, isLoadingCompleted }: ProductContentAreaProps) => (
    <StyledFormMainContainer container spacing={1}>
        <StyledStepperGrid item sm={4} md={2} data-testid="linear-stepper">
            <FormTabs />
        </StyledStepperGrid>
        <GridStyled item sm={8} md={7}>
            {!isProductLoading && isLoadingCompleted && categories.length > 0 && (
                <FormTabContent categories={categories} />
            )}
            {isProductLoading && !isLoadingCompleted && <LoadingIndicator />}
        </GridStyled>
    </StyledFormMainContainer>
);

export default ProductForms;
