import React from 'react';
import { Box, Typography, styled } from '@mui/material';
import { convertPxToRem } from '@treez-inc/component-library';
import attributesApiKeyStore from '../../../../api/attributesApiKeyStore';
import LoadingIndicator from '../../../../components/LoadingIndicator';
import SectionFieldLabel from '../../../../components/stepper/SectionFieldLabel';
import useLoadData from '../../../../hooks/useLoadData';
import { AttributeCategoryData } from '../../../../interfaces/dto/attributeCategoryData';
import useProduct from '../../Hooks/useProduct';
import ProductAttributeForm from './ProductAttibuteForm';

const AttributeFormSectionBox = styled(Box)(({ theme }) => ({
    backgroundColor: theme.palette.primaryWhite.main,
    borderTop: `${convertPxToRem(1)} solid ${theme.palette.grey04.main}`,
    display: 'flex',
    flexDirection: 'column',
    fontFamily: 'Roboto',
    padding: convertPxToRem(24),
}));

interface AttributesSectionProps {
    isBusy: boolean;
}

const AttributesSection = ({ isBusy }: AttributesSectionProps) => {
    const { product } = useProduct();

    const { isLoading, isError, data } = useLoadData<AttributeCategoryData[]>({
        queryConfig: attributesApiKeyStore.getAttributeCategories(),
    });

    return (
        <AttributeFormSectionBox>
            <Box>
                <Typography data-testid="attributeTitle-attributes" variant="h6">Attributes</Typography>
            </Box>
            <Box sx={[{ marginBottom: '1rem' }]}>
                <SectionFieldLabel>
                    Attributes help you track and tag products. You can show them on your retail labels and eCommerce
                    menu, and use them to create discounts and reports. Internal Tags are only visible to employees.
                </SectionFieldLabel>
            </Box>
            <LoadingIndicator isLoading={isLoading} />
            {isError && <>Data loading failed</>}
            {data && (
                <ProductAttributeForm
                    attributeCategories={data}
                    isBusy={isBusy}
                    verifiedReferenceId={product.verifiedReferenceId}
                />
            )}
        </AttributeFormSectionBox>
    );
};

export default AttributesSection;
