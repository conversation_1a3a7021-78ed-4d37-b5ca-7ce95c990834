import React from 'react';
import { Grid } from '@mui/material/';
import { Controller, useFormContext } from 'react-hook-form';
import MultiSelectWithSearch from '../../../../components/MultiSelectWithSearch';
import HookFormErrorBox from '../../../../components/hook-form-v2/HookFormErrorBox';
import { AttributeCategoryData } from '../../../../interfaces/dto/attributeCategoryData';
import { ProductAttributeData } from '../../../../interfaces/dto/productAttributeData';
import useCreateAttributeMutation from '../../../../mutations/attribute/useCreateAttributeMutation';
import CatalogPermissions from '../../../../permissions/catalogPermissions';
import { MenuItemsProps } from '../../../../interfaces/ProductProps';
import { isEmpty } from '../../../../utils/common';

interface AttributeFormData {
    [key: string]: MenuItemsProps[];
}

const getAtributeCategoryMenuItems = (attitbuteCategory: AttributeCategoryData): MenuItemsProps[] =>
    attitbuteCategory?.attributes?.map((a) => ({ displayValue: a.id, displayName: a.name }));

const getMenuItemData = (productAtt: ProductAttributeData) => ({
    displayValue: productAtt.attribute.id,
    displayName: productAtt.attribute.name,
});

export const getDefaultAttributeFormData = (
    attributeCategories: AttributeCategoryData[],
    productAttributes?: ProductAttributeData[],
): AttributeFormData => {
    const defaultValue = attributeCategories.reduce(
        (acc, ac) => ({
            ...acc,
            [ac.name]: [],
        }),
        {},
    );

    return productAttributes?.reduce((acc: any, current: ProductAttributeData) => {
        if (acc[current.attribute.attributeCategory.name]) {
            acc[current.attribute.attributeCategory.name].push(getMenuItemData(current));
        } else {
            acc[current.attribute.attributeCategory.name] = [getMenuItemData(current)];
        }
        return acc;
    }, defaultValue);
};

export const buildLatestProductAttributeDataFromFormValues = (
    attributeCategories: AttributeCategoryData[],
    values: any,
    productId: string,
    productAttributes?: ProductAttributeData[],
) =>
    attributeCategories.reduce((acc: ProductAttributeData[], category: AttributeCategoryData) => {
        const attributes = values[category.name]?.map((i: MenuItemsProps) => {
            const productAttributeId = productAttributes?.find((pa) => pa.attributeId === i.displayValue);
            const productAtt: ProductAttributeData = {
                id: productAttributeId?.id,
                attributeId: i.displayValue.toString(),
                productId,
                attribute: {
                    id: i.displayValue.toString(),
                    name: i.displayName,
                    attributeCategory: category,
                },
                createdAt: new Date(),
            };

            return productAtt;
        });

        return attributes ? [...acc, ...attributes] : acc;
    }, []);

export const getDeletedAttributeIds = (
    attributeCategories: any,
    attributeData: any,
    productId: string,
    productAttributes: any,
) => {
    const formProductAttributes = buildLatestProductAttributeDataFromFormValues(
        attributeCategories,
        attributeData,
        productId,
        productAttributes,
    );

    return productAttributes
        ?.filter(
            (pa: ProductAttributeData) =>
                !formProductAttributes.some((la) => pa.id && la.attributeId === pa.attributeId),
        )
        .map((pa: any) => pa.id)
        .filter(Boolean);
};

export const getAttributeMutationData = (
    attributeCategories: any,
    attributeData: any,
    productId: string,
    productAttributes: any,
) => {
    const formProductAttributes = buildLatestProductAttributeDataFromFormValues(
        attributeCategories,
        attributeData,
        productId,
        productAttributes,
    );
    const createData = formProductAttributes
        .filter((pa) => pa.id === undefined)
        .map((a) => ({ attributeId: a.attributeId, productId }));

    const deleteIds = productAttributes
        ?.filter(
            (pa: ProductAttributeData) =>
                !formProductAttributes.some((la) => pa.id && la.attributeId === pa.attributeId),
        )
        .map((pa: any) => pa.id)
        .filter(Boolean);

    return {
        createData: !isEmpty(createData) ? createData : undefined,
        updateData: undefined,
        ...(!isEmpty(deleteIds) && {
            deleteData: {
                ids: [...deleteIds],
            },
        }),
    };
};

const updateNewlyCreatedIds = (createData: ProductAttributeData[], curretProductAttribute: ProductAttributeData[]) => {
    if (!isEmpty(createData)) {
        return curretProductAttribute.map((pa) => {
            if (!pa.id) {
                const added = createData.find((d) => d.attributeId === pa.attributeId);
                const newItem = {
                    ...pa,
                    id: added?.id,
                };
                return newItem;
            }
            return pa;
        });
    }

    return curretProductAttribute;
};

const getDeleteErrorData = (failedData: any, deletedIds: string[]): { entity: any; error: string }[] => {
    if (isEmpty(failedData?.error)) {
        return [];
    }

    const { error } = failedData;

    if (error.name === 'AxiosError' && !error.response) {
        return deletedIds.map((id) => ({ entity: { id }, error: 'Test' }));
    }

    const getErrorData = () => {
        if (!isEmpty(error.response?.data?.failed)) {
            return error.response.data.failed;
        }
        if (!isEmpty(error)) {
            return error;
        }
        return undefined;
    };

    const deletedItemsFailed: any[] = getErrorData();
    if (!isEmpty(deletedItemsFailed)) {
        return deletedItemsFailed.map((d) => ({ entity: d.entity, error: 'Test' }));
    }

    return [];
};

const addDeleteFailureBack = (
    deletedData: any,
    deletedIds: any[],
    curretProductAttribute: ProductAttributeData[],
    productAttributes: any[],
) => {
    const failedData = getDeleteErrorData(deletedData, deletedIds);
    const deleteFailedPA: ProductAttributeData[] | undefined = productAttributes?.filter((pa) =>
        failedData.some((f) => pa.id === f.entity.id),
    );

    if (!isEmpty(deleteFailedPA)) {
        return [...curretProductAttribute, ...(deleteFailedPA || [])];
    }

    return [...curretProductAttribute];
};

export const getUpdatedAttributeData = (
    attributeCategories: any,
    attributeData: any,
    productId: string,
    productAttributes: any,
    result: any,
) => {
    const formProductAttributes = buildLatestProductAttributeDataFromFormValues(
        attributeCategories,
        attributeData,
        productId,
        productAttributes,
    );

    const deleteIds = productAttributes
        ?.filter(
            (pa: ProductAttributeData) =>
                !formProductAttributes.some((la) => pa.id && la.attributeId === pa.attributeId),
        )
        .map((pa: any) => pa.id)
        .filter(Boolean);

    let updatedProductAttributes: ProductAttributeData[] = [...formProductAttributes];
    if (!isEmpty(result.createData?.data) && result.createData?.data) {
        updatedProductAttributes = updateNewlyCreatedIds(result.createData?.data, formProductAttributes);
    }

    if (!isEmpty(deleteIds) && !isEmpty(result.deleteData?.error)) {
        updatedProductAttributes = addDeleteFailureBack(
            result.deleteData,
            deleteIds,
            updatedProductAttributes,
            productAttributes,
        );
    }

    return updatedProductAttributes;
};

interface ProductAttributeFormProps {
    verifiedReferenceId?: string;
    attributeCategories: AttributeCategoryData[];
    isBusy: boolean;
}

const ProductAttributeForm = ({ attributeCategories, verifiedReferenceId, isBusy }: ProductAttributeFormProps) => {
    const createAttributeMutation = useCreateAttributeMutation();
    const { control } = useFormContext();

    const disableAttributeForGlobalProduct = (attributeCategoryName: string) =>
        !!verifiedReferenceId && attributeCategoryName !== 'Internal Tags';

    return (
        <Grid container spacing={2}>
            {attributeCategories?.map((item: AttributeCategoryData) => (
                <Grid key={item.id} item xs={12} data-testid="product-attribute-category">
                    <Controller
                        key={item.id}
                        name={`productAttributeDetails.${item.name}`}
                        control={control}
                        render={({ field: { onChange, onBlur, value, ref } }) => (
                            <MultiSelectWithSearch
                                onChange={onChange}
                                onBlur={onBlur}
                                value={value || []}
                                ref={ref}
                                isDisabled={isBusy || disableAttributeForGlobalProduct(item.name)}
                                dataForNewOption={{
                                    attributeCategoryId: item?.id,
                                }}
                                menuItems={getAtributeCategoryMenuItems(item) || []}
                                label={item.name}
                                mutation={createAttributeMutation}
                                createPermissions={[
                                    CatalogPermissions.ADJUST_PRODUCT,
                                    CatalogPermissions.ADJUST_ATTRIBUTE,
                                ]}
                                options={{ createNewOption: true }}
                            />
                        )}
                    />
                    <HookFormErrorBox name={item.name} />
                </Grid>
            ))}
        </Grid>
    );
};

export default ProductAttributeForm;
