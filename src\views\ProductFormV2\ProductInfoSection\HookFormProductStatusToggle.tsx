import React from 'react';
import { convertPxToRem, StaticChip } from '@treez-inc/component-library';
import { Controller, useFormContext } from 'react-hook-form';
import { Box, styled } from '@mui/material';
import ButtonWithConfirmation from '../../../components/ButtonWithConfirmation';
import { Status } from '../../../utils/constants';

interface HookFormVariantStatusToggleProps {
    name: string;
}

const FirstMessageBox = styled(Box)(() => ({
    columnGap: convertPxToRem(10),
    display: 'flex',
    flexDirection: 'row',
    paddingBottom: convertPxToRem(10),
}));

const MessageBox = styled(Box)(() => ({
    paddingTop: convertPxToRem(10),
    paddingBottom: convertPxToRem(10),
}));

export default function HookFormProductStatusToggle({ name }: HookFormVariantStatusToggleProps) {
    const { control } = useFormContext();

    const deactivateVariantMessage = () => (
        <>
            <FirstMessageBox>
                <MessageBox>Are you sure you want to deactivate product?</MessageBox>
            </FirstMessageBox>
        </>
    );

    const activateVariantMessage = () => (
        <>
            <FirstMessageBox>
                <MessageBox>Are you sure you want to activate product?</MessageBox>
            </FirstMessageBox>
        </>
    );

    return (
        <Controller
            control={control}
            name={name}
            render={({ field: { onChange, value } }) => (
                <>
                    {value === Status.ACTIVE ? (
                        <ButtonWithConfirmation
                            iconName="VisibilityOff"
                            label="Deactivate"
                            small
                            confirmModel={{
                                title: 'Confirm Deactivation',
                                content: deactivateVariantMessage(),
                            }}
                            onConfirm={() => onChange(Status.INACTIVE)}
                        />
                    ) : (
                        <>
                            <StaticChip color="gray" label="Deactivated" variant="filled" />
                            <ButtonWithConfirmation
                                iconName="CheckmarkCircle"
                                small
                                label="Activate"
                                confirmModel={{
                                    title: 'Confirm Activation',
                                    content: activateVariantMessage(),
                                }}
                                onConfirm={() => onChange(Status.ACTIVE)}
                            />
                        </>
                    )}
                </>
            )}
        />
    );
}
