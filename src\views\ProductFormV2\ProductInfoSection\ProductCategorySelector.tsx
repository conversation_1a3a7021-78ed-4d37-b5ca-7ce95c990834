import React, { useState } from 'react';
import { Box, Grid, styled } from '@mui/material';
import { allColors, Icon } from '@treez-inc/component-library';
import findCategoryIcon from '../../../utils/categoryUtils';
import { ProductCategoryDto } from '../../../interfaces/dto/productCategory';
import useProduct from '../Hooks/useProduct';
import { useOrganizationEntityInfoContext } from '../../../hooks/organization-entity/useOrganizationEntityInfoContext';
import { RegionType } from '../../../utils/constants';
import { isEmpty } from '../../../utils/common';

const CategoryButton = styled(Box)`
    border-radius: 12px;
    cursor: pointer;
    height: 60px;
    padding: 12px 6px 0;
    width: 60px;

    &:hover {
        background-color: #eee;
    }
`;
const CategoryImage = styled(Box)`
    height: 22px;
    text-align: center;
`;
const CategoryName = styled(Box)`
    color: #595959;
    display: inline-block;
    font-family: 'Roboto';
    font-size: 10px;
    font-style: normal;
    font-weight: 600;
    height: 12px;
    letter-spacing: 0.015em;
    line-height: 12px;
    text-align: center;
    width: 60px;
`;

interface ProductCategorySelectorProps {
    onSelectCategory: (category: ProductCategoryDto) => any;
    categories: ProductCategoryDto[];
    selectedCategory?: ProductCategoryDto;
}

const ProductCategorySelector = ({ categories, selectedCategory, onSelectCategory }: ProductCategorySelectorProps) => {
    const { product } = useProduct();
    const { regions } = useOrganizationEntityInfoContext();
    const [showList, setShowList] = useState(!product.id);

    const handleCategorySelected = (category: ProductCategoryDto) => {
        onSelectCategory(category);
        setShowList(false);
    };

    const handleToggleListVisibility = () => {
        setShowList(!showList);
    };

    const getFilteredCategories = () => {
        if (!isEmpty(regions) && regions?.every((r) => r === RegionType.California)) {
            return categories.filter((c: { name: string }) => c.name.toLowerCase() !== 'cbd');
        }
        return categories;
    };

    return (
        <>
            {showList ? (
                <Grid data-testid="product-categories-container" container spacing={1}>
                    {getFilteredCategories().map((category: ProductCategoryDto) => (
                        <Grid key={category.id} item sm={2}>
                            <CategoryButton
                                data-testid={`category-button-${category.name}`}
                                onClick={() => {
                                    handleCategorySelected(category);
                                }}
                                style={{
                                    backgroundColor:
                                        category.id === selectedCategory?.id ? allColors.midribGreen.main : '',
                                }}
                            >
                                <CategoryImage>
                                    <Icon
                                        color="treezGrey"
                                        fontSize="medium"
                                        iconName={findCategoryIcon(category.name)}
                                    />
                                </CategoryImage>
                                <CategoryName>{category.name?.toUpperCase()}</CategoryName>
                            </CategoryButton>
                        </Grid>
                    ))}
                </Grid>
            ) : (
                <Grid container>
                    {selectedCategory && (
                        <Grid item sm={2}>
                            <CategoryButton
                                data-testid="product-form-category-button"
                                style={{
                                    backgroundColor: allColors.midribGreen.main,
                                }}
                            >
                                <CategoryImage data-testid="category-image">
                                    <Icon
                                        color="treezGrey"
                                        fontSize="medium"
                                        iconName={findCategoryIcon(selectedCategory.name)}
                                    />
                                </CategoryImage>
                                <CategoryName data-testid="category-name">
                                    {selectedCategory?.name?.toUpperCase()}
                                </CategoryName>
                            </CategoryButton>
                        </Grid>
                    )}
                    {!product.id && (
                        <Grid item sm={2}>
                            <CategoryButton
                                data-testid="product-form-category-button-change"
                                onClick={handleToggleListVisibility}
                            >
                                <CategoryImage>
                                    <Icon color="treezGrey" fontSize="medium" iconName="Filter" />
                                </CategoryImage>
                                <CategoryName>CHANGE</CategoryName>
                            </CategoryButton>
                        </Grid>
                    )}
                </Grid>
            )}
        </>
    );
};

export default ProductCategorySelector;
