import React from 'react';
import { Typography } from '@mui/material/';
import { Modal } from '@treez-inc/component-library';

interface ProductFormConfirmDialogProps {
    onOk: () => void;
    onSkip: () => void;
}

const ProductFormNextConfirmDialog = ({ onOk, onSkip }: ProductFormConfirmDialogProps) => (
    <Modal
        testId="product-form-exit-modal"
        title="UNSAVED CHANGES"
        content={
            <Typography>
                This product does not have a Brand name. Are you sure you want to save it without one?
            </Typography>
        }
        onClose={onSkip}
        open
        primaryButton={{ label: 'Save Without Brand Name', onClick: onOk }}
        secondaryButton={{ label: 'Skip', onClick: onSkip }}
    />
);

export default ProductFormNextConfirmDialog;
