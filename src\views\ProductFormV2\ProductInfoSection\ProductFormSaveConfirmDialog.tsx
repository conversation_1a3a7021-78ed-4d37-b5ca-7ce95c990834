import React from 'react';
import { Typography } from '@mui/material/';
import { Modal } from '@treez-inc/component-library';

interface ProductFormConfirmDialogProps {
    onOk: () => void;
    onCancel: () => void;
}

const ProductFormSaveConfirmDialog = ({ onOk, onCancel }: ProductFormConfirmDialogProps) => (
    <Modal
        testId="product-form-exit-modal"
        title="Save Product Without Brand Name?"
        content={
            <Typography>
                This product does not have a Brand name. Are you sure you want to save it without one?
            </Typography>
        }
        onClose={onCancel}
        open
        primaryButton={{ label: 'Save Without Brand Name', onClick: onOk }}
        secondaryButton={{ label: 'Cancel', onClick: onCancel }}
    />
);

export default ProductFormSaveConfirmDialog;
