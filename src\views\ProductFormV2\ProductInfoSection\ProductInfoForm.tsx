import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Grid, styled } from '@mui/material/';
import { ObjectType } from '@treez-inc/file-management';
import { v4 as uuidv4 } from 'uuid';
import { convertPxToRem } from '@treez-inc/component-library/dist/util-functions';
import HookFormInput from '../../../components/hook-form-v2/HookFormInput';
import useReactHookForm from '../../../hooks/useReactHookForm';
import productCategoryApiKeyStore from '../../../api/productCategoryApiKeyStore';
import useLoadData from '../../../hooks/useLoadData';
import LoadingIndicator from '../../../components/LoadingIndicator';
import SubcategoryHookFormSelect from '../../../components/hook-form-v2/SubcategoryHookFormSelect';
import { ProductSubCategoryDto } from '../../../interfaces/dto/productSubCategory';
import { DetailsDto } from '../../../interfaces/dto/details';
import useProduct from '../Hooks/useProduct';
import HookFormDynamicField from '../../../components/hook-form-v2/HookFormDynamicField';
import { FormDetailsProps, InputType } from '../../../interfaces/dto/productCategory';
import brandApiKeyStore from '../../../api/brandApiKeyStore';
import SelectWithSearch from '../../../components/SelectWithSearch';
import CatalogPermissions from '../../../permissions/catalogPermissions';
import useCreateBrandMutation from '../../../mutations/brand/useCreateBrandMutation';
import ReactHookProductTabForm from '../ReactHookProductTabForm';
import { SubmitCallBack } from '../Types/SubmitFormTypes';
import getProductInfoFormSchema from './ProductInfoFormSchema';
import HookFormErrorBox from '../../../components/hook-form-v2/HookFormErrorBox';
import useSaveData, { MutationResponse } from '../../../hooks/useSaveData';
import { ProductData } from '../../../interfaces/dto/productData';
import productApiKeyStore from '../../../api/productApiKeyStore';
import filterMutationErrors, { MutationErrorData } from '../../../utils/MutationResponseUtil';
import { alphabeticalAscOrder, dataDiffInArrays, dataDiffInObjs, isEmpty } from '../../../utils/common';
import Mode from '../Types/Mode';
import HookFormProductStatusToggle from './HookFormProductStatusToggle';
import AttributesSection from './Attributes/AttributesSection';
import {
    getAttributeMutationData,
    getDefaultAttributeFormData,
    getDeletedAttributeIds,
    getUpdatedAttributeData,
} from './Attributes/ProductAttibuteForm';
import { AttributeCategoryData } from '../../../interfaces/dto/attributeCategoryData';
import { ProductAttributeData } from '../../../interfaces/dto/productAttributeData';
import productAttributeApiKeyStore from '../../../api/productAttributeApiKeyStore';
import ImagesContainer from '../Images/ImagesContainer';
import imageApiKeyStore from '../../../api/imageApiKeyStore';
import { ImageDetailsDto } from '../../../interfaces/dto/imageDetails';
import { getImageMutationData, mergeArraysById } from '../Images/ImageUtils';
import ProductInfoFormButton from './ProductInfoFormButton';
import { Status } from '../../../utils/constants';
import SubcategoryChangeDialog from '../SubcategoryChangeDialog';

const FormItem = styled(Grid)({
    padding: convertPxToRem(16),
});
const FormImageItem = styled(Grid)({
    paddingTop: convertPxToRem(16),
    paddingBottom: convertPxToRem(16),
});

export const PRODUCT_CLASSIFICATION_OPTIONS = ['Sativa', 'Indica', 'Hybrid', 'I/S', 'S/I', 'CBD', 'Kush', 'None'].map(
    (value) => ({
        name: value,
        id: value,
        displayName: value,
        displayValue: value,
    }),
);

// Todo - Update models
interface ProductFormData {
    id?: string;
    name?: string;
    brandId?: string;
    productSubCategoryId?: string;
    details?: DetailsDto;
    strain?: string;
    status?: string;
    productAttributeDetails: any;
    images?: any[];
}

interface ProductInfoFormProps {
    attributeCategories: AttributeCategoryData[];
}

const buildUpdatedFormValues = (product: ProductData, attributeCategories: AttributeCategoryData[]) => {
    const { details } = product;
    return {
        id: product.id,
        name: product.name ?? '',
        brandId: product.brandId ?? '',
        status: product.status ?? Status.ACTIVE,
        productSubCategoryId: product.productSubCategoryId ?? '',
        details,
        strain: product?.strain || product.details?.strain,
        productAttributeDetails: getDefaultAttributeFormData(attributeCategories, product.productAttributes),
        images: product?.images ? [...product.images].sort((a: any, b: any) => a.order - b.order) : [],
    };
};

const ProductInfoForm = ({ attributeCategories }: ProductInfoFormProps) => {
    const navigate = useNavigate();
    const [isRedirecting, setIsRedirecting] = useState(false);

    const { product, mode, setProductUpdate, isBulkProduct, isBulkSubCategory } = useProduct();
    const [subcategoryDialogOpen, setSubcategoryDialogOpen] = useState(false);

    const isGlobalContent: boolean = !!product.verifiedReferenceId;
    const [subCategoryMenuItems, setSubCategoryMenuItems] = useState<
        {
            displayValue: string | number;
            displayName: string;
        }[]
    >([]);

    const defaultValues = buildUpdatedFormValues(product, attributeCategories);
    const form = useReactHookForm<ProductFormData>({
        joiSchema: getProductInfoFormSchema(),
        defaultValues,
    });

    const { isLoading: isSaving, mutateAsync } = useSaveData<ProductData[]>({
        mutationConfig: productApiKeyStore.saveProductDetails(),
    });

    const { isLoading: isSavingAttribute, mutateAsync: mutateAttributeAsync } = useSaveData<ProductAttributeData[]>({
        mutationConfig: productAttributeApiKeyStore.saveProductAttributes(),
    });

    const { isLoading: isSavingImages, mutateAsync: mutateImageAsync } = useSaveData<ImageDetailsDto[]>({
        mutationConfig: imageApiKeyStore.saveImageDetails(),
    });

    const {
        isLoading: isSubCategoryLoading,
        isError: isSubCategoryError,
        data: subCategories,
    } = useLoadData<ProductSubCategoryDto[]>({
        queryConfig: productCategoryApiKeyStore.getAllSubCategories(),
    });

    const {
        isLoading: isBrandLoading,
        isError: isBrandError,
        data: brands,
        refetch: refetchBrand,
    } = useLoadData<ProductSubCategoryDto[]>({
        queryConfig: brandApiKeyStore.getBrands(),
    });

    const createBrandMutation = useCreateBrandMutation();

    useEffect(() => {
        if (subCategories && product.productCategory) {
            const subCategoriesFiltered = subCategories
                .filter((s) => {
                    if (product.id && !isBulkProduct()) {
                        return s.productCategoryId === product.productCategory?.id && !isBulkSubCategory(s.name);
                    }
                    return s.productCategoryId === product.productCategory?.id;
                })
                .map((sc) => ({ displayValue: sc.id, displayName: sc.name }));
            setSubCategoryMenuItems(subCategoriesFiltered ?? []);
            const currentSubcategoryValue = form.getValues('productSubCategoryId');
            if (!subCategoriesFiltered.some((s) => s.displayValue === currentSubcategoryValue)) {
                form.resetField('productSubCategoryId');
                form.resetField('uom');
            }
        }
    }, [product, subCategories]);

    const cleanProductData = (data: any, removeValues: any[] = [undefined, '']): any => {
        const updated = Object.keys(data).reduce((obj, key) => {
            if (data[key] !== null && typeof data[key] === 'object') {
                const newValue = cleanProductData(data[key]);
                return { ...obj, [key]: newValue };
            }
            if (removeValues.some((rv) => rv === data[key])) {
                return obj;
            }
            return { ...obj, [key]: data[key] };
        }, {});

        return updated;
    };

    const getAttributeDeleteErrorData = (failedData: any, deletedIds: string[]): { entity: any; error: string }[] => {
        if (isEmpty(failedData?.error)) {
            return [];
        }

        const { error } = failedData;

        if (error.name === 'AxiosError' && !error.response) {
            return deletedIds.map((id) => ({ entity: { id }, error: 'Test' }));
        }

        const getErrorData = () => {
            if (!isEmpty(error.response?.data?.failed)) {
                return error.response.data.failed;
            }
            if (!isEmpty(error)) {
                return error;
            }
            return undefined;
        };

        const deletedItemsFailed: any[] = getErrorData();
        if (!isEmpty(deletedItemsFailed)) {
            return deletedItemsFailed.map((d) => ({ entity: d.entity, error: 'Test' }));
        }

        return [];
    };

    const setFormApiErrors = (response: any, deletedIds: any[], latestAttributes: ProductAttributeData[]) => {
        const failedData = getAttributeDeleteErrorData(response, deletedIds);
        attributeCategories?.forEach((category) => {
            const categoryProductAtts = latestAttributes?.filter(
                (p) => p.attribute.attributeCategory.id === category.id,
            );

            const failedItems = categoryProductAtts?.filter(
                (p) => !p.id || failedData.some((f) => f.entity.id === p.id),
            );
            if (!isEmpty(failedItems)) {
                form.setError(category.name, {
                    message: `Failed to add/delete - ${failedItems.map((p) => p.attribute.name).join(', ')}`,
                });
            }
        });
    };

    const updateImagesToProduct = (imageResult: MutationResponse<ImageDetailsDto[]>) => {
        const images: ImageDetailsDto[] = [];
        if (imageResult.createData?.data) {
            images.push(...imageResult.createData.data);
        }
        if (imageResult.updateData?.data) {
            images.push(...imageResult.updateData.data);
        }
        const completeImages = product.images ? mergeArraysById(product.images, images) : images;

        if (imageResult.deleteData?.data) {
            return completeImages.filter((img) => !imageResult.deleteData?.data?.some((i) => i.id === img.id));
        }

        return completeImages;
    };

    const submitImages = async (
        productId: string,
        formImages: any[],
    ): Promise<{ result: any; errors: MutationErrorData[] | undefined }> => {
        const mutationData = getImageMutationData(formImages ?? [], product.images ?? [], productId);
        if (!isEmpty(mutationData)) {
            const imageResult = await mutateImageAsync(mutationData);

            if (imageResult) {
                return { result: imageResult, errors: filterMutationErrors(imageResult, () => 'Image') };
            }
        }
        return { result: undefined, errors: undefined };
    };

    const setProductContext = (
        productData: ProductData,
        formValues: ProductFormData,
        productResult: { result: any; errors: MutationErrorData[] | undefined },
        attributeResult: { result: any; errors: MutationErrorData[] | undefined },
        imageResult: { result: any; errors: MutationErrorData[] | undefined },
    ): ProductData => {
        const productId: string = productData.id || '';
        const productResultData = formValues.id
            ? productResult.result?.updateData?.data
            : productResult.result?.createData?.data;
        const updatedProduct: ProductData = {
            ...productData,
            ...(productResultData && productResultData[0]),
        };
        const updatedProductAttributes = getUpdatedAttributeData(
            attributeCategories,
            formValues.productAttributeDetails,
            productId,
            productData.productAttributes,
            attributeResult.result,
        );

        const productSubCategory = subCategories?.find((sc) => sc.id === updatedProduct.productSubCategoryId);

        const productUpdate = {
            ...updatedProduct,
            images: updateImagesToProduct(imageResult.result),
            productAttributes: updatedProductAttributes.filter((a) => !!a.id),
            ...(productSubCategory && { productSubCategory }),
        };

        setProductUpdate(productUpdate);
        return productUpdate;
    };

    const submitAttributes = async (
        productId: string,
        formAttributes: any,
    ): Promise<{ result: any; errors: MutationErrorData[] | undefined }> => {
        if (productId) {
            const attributeMutationData = getAttributeMutationData(
                attributeCategories,
                formAttributes,
                productId,
                product.productAttributes,
            );
            if (attributeMutationData) {
                const deletedAttributeIds = getDeletedAttributeIds(
                    attributeCategories,
                    formAttributes,
                    productId,
                    product.productAttributes,
                );

                // Product Attribute changes need to be updated in series, not in parallel. This is because of our eventing framework in the backend, which publishes two product-level events – one for create events (Product Attributes Added), and one for delete events (Product Attributes Removed). If the two API calls are made at the same time, there is the likely possibility of a race condition, where the calculated current state of the product's attributes will be out of sync with the actual end state after both the create and delete events are processed. As a future enhancement, we should look at creating a single API endpoint that can handle both create and delete events in a single call: https://gitlab.com/treez-inc/engineering/back-of-house/product-management-service/-/issues/1212
                const { createData: createAttrs, deleteData: deleteAttrs } = attributeMutationData;

                const createAttrsRes = createAttrs
                    ? (await mutateAttributeAsync({ createData: createAttrs })).createData
                    : undefined;

                const deleteAttrsRes = deleteAttrs
                    ? (await mutateAttributeAsync({ deleteData: deleteAttrs })).deleteData
                    : undefined;

                const resultAttribute = {
                    createData: createAttrsRes,
                    deleteData: deleteAttrsRes,
                };

                const updatedProductAttributes = getUpdatedAttributeData(
                    attributeCategories,
                    formAttributes,
                    productId,
                    product.productAttributes,
                    resultAttribute,
                );
                setFormApiErrors(resultAttribute.deleteData, deletedAttributeIds, updatedProductAttributes);
                return { result: resultAttribute, errors: filterMutationErrors(resultAttribute) };
            }
        }
        return { result: undefined, errors: undefined };
    };

    const submitProduct = async (values: any): Promise<{ result: any; errors: MutationErrorData[] | undefined }> => {
        const result = await mutateAsync({
            ...(!values.id && { createData: [cleanProductData(values)] }),
            ...(values.id && { updateData: [cleanProductData(values)] }),
        });
        return { result, errors: filterMutationErrors(result) };
    };

    const handleSubmit = (callBack?: SubmitCallBack) => async (values: ProductFormData) => {
        // compare data changes between current and new product values
        const { images: currentImages, productAttributeDetails: currentAttributes, ...currentProduct } = defaultValues;
        const { images: newImages, productAttributeDetails: newAttributes, ...newProduct } = values;

        const productDiff = dataDiffInObjs(currentProduct, newProduct, ['id'], ['details'], true);
        const imagesDiff = dataDiffInArrays(currentImages || [], newImages || [], ['id']);

        if (product && product.id) {
            const [productResult, attributeResult, imageResult] = await Promise.all([
                productDiff ? submitProduct(productDiff) : { result: undefined, errors: undefined },
                submitAttributes(product.id, newAttributes),
                imagesDiff ? submitImages(product.id, imagesDiff) : { result: undefined, errors: undefined },
            ]);
            const productUpdated = setProductContext(product, values, productResult, attributeResult, imageResult);

            callBack?.({
                formValue: buildUpdatedFormValues(productUpdated, attributeCategories),
                errors: [
                    ...(productResult.errors ?? []),
                    ...(attributeResult.errors ?? []),
                    ...(imageResult.errors ?? []),
                ],
            });
        } else {
            const productResult = await submitProduct(values);
            const productId: string = productResult.result?.createData?.data?.[0]?.id || product.id;

            if (productId) {
                const [attributeResult, imageResult] = await Promise.all([
                    submitAttributes(productId, newAttributes),
                    imagesDiff ? submitImages(productId, imagesDiff) : { result: undefined, errors: undefined },
                ]);

                const productUpdated = setProductContext(product, values, productResult, attributeResult, imageResult);

                callBack?.({
                    formValue: buildUpdatedFormValues(productUpdated, attributeCategories),
                    errors: [
                        ...(productResult.errors ?? []),
                        ...(attributeResult.errors ?? []),
                        ...(imageResult.errors ?? []),
                    ],
                });

                if (productResult.result.createData?.data?.[0].id && mode === Mode.CREATE) {
                    setIsRedirecting(true);
                    navigate(`${productResult.result.createData?.data?.[0].id}`);
                }
            } else {
                callBack?.({ errors: productResult.errors, isBlockingError: true });
            }
        }
    };

    const overrideFormProps = (details: FormDetailsProps): FormDetailsProps => {
        if (details.input === 'classification') {
            return { ...details, inputType: InputType.SELECT_OPTIONS, options: PRODUCT_CLASSIFICATION_OPTIONS };
        }
        return details;
    };

    const formButtons = ({ handleNavigate, isBusy }: { handleNavigate: any; isBusy?: boolean }) => (
        <>
            <ProductInfoFormButton onNavigate={handleNavigate} isBusy={isBusy} />
        </>
    );

    if (isRedirecting || isSubCategoryLoading || isBrandLoading) {
        return <LoadingIndicator isLoading />;
    }

    if (isSubCategoryError || isBrandError) {
        return <>Error loading data</>;
    }

    return (
        <ReactHookProductTabForm
            formName="ProductInfoForm"
            onSubmit={handleSubmit}
            formContextProps={form}
            isBusy={isSaving || isSavingAttribute || isSavingImages || isSubCategoryLoading || isBrandLoading}
            renderButtons={formButtons}
        >
            <FormImageItem>
                <Grid item sm={12} lg={12}>
                    <ImagesContainer
                        fieldName="images"
                        objectId={product.id ?? uuidv4()}
                        objectType={ObjectType.PRODUCT_IMAGE}
                        hideGlobalImageCheckbox
                    />
                </Grid>
            </FormImageItem>
            <Grid container spacing={2}>
                <FormItem item sm={6}>
                    <HookFormInput disabled={isGlobalContent} name="name" label="Product Name *" />
                </FormItem>
                <FormItem item sm={6}>
                    <HookFormProductStatusToggle name="status" />
                </FormItem>
                <FormItem item sm={6}>
                    <SubcategoryHookFormSelect
                        disabled={isGlobalContent}
                        name="productSubCategoryId"
                        label="Subcategory *"
                        autoSelectDefault
                        menuItems={alphabeticalAscOrder(subCategoryMenuItems, 'displayName')}
                        setOpenSubcategoryChangeDialog={setSubcategoryDialogOpen}
                    />
                </FormItem>
                <FormItem item sm={6}>
                    <SelectWithSearch
                        disabled={isGlobalContent}
                        name="brandId"
                        menuItems={
                            alphabeticalAscOrder(brands ?? [], 'name')?.map((b) => ({
                                displayValue: b.id,
                                displayName: b.name,
                            })) || []
                        }
                        refetch={refetchBrand}
                        label="Brand"
                        dataForNewOption={{}}
                        createMutation={createBrandMutation}
                        createPermissions={[CatalogPermissions.ADJUST_PRODUCT, CatalogPermissions.ADJUST_BRAND]}
                    />
                    <HookFormErrorBox name="brandId" />
                </FormItem>
                <FormItem item sm={12}>
                    <HookFormInput label="Description" name="details.description" multiline rows={3} />
                </FormItem>
                {product.productCategory?.productFormDetails?.map((frm) => (
                    <FormItem key={frm.input} item sm={6}>
                        {/* Strain is a separate field in product and not part of details field. Hence the condition in formatName method below */}
                        <HookFormDynamicField
                            data={overrideFormProps(frm)}
                            disabled={isGlobalContent}
                            formatName={(name) => (name === 'strain' ? name : `details.${name}`)}
                        />
                    </FormItem>
                ))}
                <AttributesSection isBusy={isSaving || isSavingAttribute} />
                <SubcategoryChangeDialog setDialogOpen={setSubcategoryDialogOpen} dialogOpen={subcategoryDialogOpen} />
            </Grid>
        </ReactHookProductTabForm>
    );
};

export default ProductInfoForm;
