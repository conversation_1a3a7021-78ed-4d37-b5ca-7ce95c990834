import React, { useState } from 'react';
import { useFormContext } from 'react-hook-form';
import useProductTabNavigation from '../Hooks/useProductTabNavigation';
import useProduct from '../Hooks/useProduct';
import { TabNavigation, PageName } from '../Types/Navigation.enum';
import {
    BaseNavigationOption,
    TabNavigationOption,
    PageNavigationOption,
    IgnoreNavigationOption,
} from '../Types/NavigationOption';
import Mode from '../Types/Mode';
import ProductFormSaveConfirmDialog from './ProductFormSaveConfirmDialog';
import { isEmpty } from '../../../utils/common';
import ProductInfoFormCreateButton from './ProductInfoFormCreateButton';
import ProductInfoFormEditButton from './ProductInfoFormEditButton';
import ProductFormNextConfirmDialog from './ProductFormNextConfirmDialog';

interface ProductFormButtonsProps {
    onNavigate: (submit: boolean, navigate: BaseNavigationOption) => any;
    isBusy?: boolean;
}

export default function ProductInfoFormButton({ onNavigate, isBusy }: ProductFormButtonsProps) {
    const { product, mode } = useProduct();
    const { getValues } = useFormContext();
    const { tabs, isTabDirty } = useProductTabNavigation();
    const [navigationWithConfirmation, setNavigationWithConfirmation] = useState<{
        type?: 'OPTIONAL_SUBMIT' | 'SUBMIT';
        submit: boolean;
        navigationOption: BaseNavigationOption | null;
    } | null>();

    const getNext = () => {
        const nextIndex = tabs.findIndex((t) => t.isSelected) + 1;
        return nextIndex < tabs.length ? tabs[nextIndex] : undefined;
    };

    const canShowConfirmation = () => {
        if (!isTabDirty) {
            return false;
        }

        if (['misc', 'merch', 'plant'].some((n) => n === product.productCategory?.name?.toLowerCase())) {
            return false;
        }

        const brandValue = getValues('brandId');
        return (mode === Mode.CREATE || product.brandId) && (isEmpty(brandValue) || brandValue === '');
    };

    const navigatePage = (navOptions?: { submit: boolean; navigationOption: BaseNavigationOption | null } | null) => {
        if (navOptions) {
            const { submit, navigationOption } = navOptions;

            if (isTabDirty || !product.id) {
                onNavigate(submit, navigationOption || new IgnoreNavigationOption());
            } else {
                onNavigate(false, navigationOption || new IgnoreNavigationOption());
            }
        }
    };

    const handleSubmit = async () => {
        if (canShowConfirmation()) {
            setNavigationWithConfirmation({ submit: isTabDirty, navigationOption: new IgnoreNavigationOption() });
        } else {
            navigatePage({ submit: isTabDirty, navigationOption: new IgnoreNavigationOption() });
        }
    };

    const handleSubmitAndNext = async () => {
        if (canShowConfirmation()) {
            setNavigationWithConfirmation({
                submit: isTabDirty,
                navigationOption: new TabNavigationOption(TabNavigation.SELECT, getNext()?.tab),
            });
        } else {
            navigatePage({
                submit: isTabDirty,
                navigationOption: new TabNavigationOption(TabNavigation.SELECT, getNext()?.tab),
            });
        }
    };

    const handleNext = async () => {
        if (canShowConfirmation()) {
            setNavigationWithConfirmation({
                type: 'OPTIONAL_SUBMIT',
                submit: isTabDirty,
                navigationOption: new TabNavigationOption(TabNavigation.SELECT, getNext()?.tab, true),
            });
        } else {
            navigatePage({
                submit: false,
                navigationOption: new TabNavigationOption(TabNavigation.SELECT, getNext()?.tab),
            });
        }
    };

    const handleClose = () => {
        if (canShowConfirmation()) {
            setNavigationWithConfirmation({
                type: 'OPTIONAL_SUBMIT',
                submit: isTabDirty,
                navigationOption: new PageNavigationOption(PageName.PRODUCT_CONTROL_PAGE, true),
            });
        } else {
            navigatePage({
                submit: false,
                navigationOption: new PageNavigationOption(PageName.PRODUCT_CONTROL_PAGE),
            });
        }
    };

    const handleOkConfirmation = () => {
        navigatePage(navigationWithConfirmation);
        setNavigationWithConfirmation(null);
    };

    const handleCancelConfirmation = () => {
        setNavigationWithConfirmation(null);
    };

    const handleSkipConfirmation = () => {
        if (navigationWithConfirmation) {
            navigatePage({ ...navigationWithConfirmation, submit: false });
        }
        setNavigationWithConfirmation(null);
    };

    const getConfirmationDialog = () => {
        if (navigationWithConfirmation?.type === 'OPTIONAL_SUBMIT') {
            return <ProductFormNextConfirmDialog onOk={handleOkConfirmation} onSkip={handleSkipConfirmation} />;
        }
        return <ProductFormSaveConfirmDialog onOk={handleOkConfirmation} onCancel={handleCancelConfirmation} />;
    };

    return (
        <>
            {navigationWithConfirmation && getConfirmationDialog()}
            {mode === Mode.CREATE ? (
                <ProductInfoFormCreateButton
                    product={product}
                    onClose={handleClose}
                    onSubmit={handleSubmitAndNext}
                    isBusy={isBusy}
                    isTabDirty={isTabDirty}
                />
            ) : (
                <ProductInfoFormEditButton
                    onCloseClick={handleClose}
                    onSubmitClick={handleSubmit}
                    onNextClick={handleNext}
                    isBusy={isBusy}
                    isTabDirty={isTabDirty}
                />
            )}
        </>
    );
}
