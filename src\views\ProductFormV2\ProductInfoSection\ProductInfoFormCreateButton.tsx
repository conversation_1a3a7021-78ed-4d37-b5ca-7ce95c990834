import React from 'react';
import { Box, Paper, styled } from '@mui/material';
import convertPxToRem from '@treez-inc/component-library/dist/util-functions/convertPxToRem';
import { Icon } from '@treez-inc/component-library';
import { StyledPrimaryButton, StyledSecondaryButton } from '../../../styles/StyledProductForm';
import { ProductData } from '../../../interfaces/dto/productData';

const PreviousButtonRow = styled(Box)(() => ({
    float: 'left',
    padding: convertPxToRem(16),
    marginLeft: '14%',
}));

const NextButtonRow = styled(Box)(() => ({
    float: 'right',
    padding: convertPxToRem(16),
    marginRight: '12%',
}));

const PaperStyled = styled(Paper)(() => ({
    position: 'fixed',
    bottom: convertPxToRem(0),
    left: convertPxToRem(0),
    right: convertPxToRem(0),
    zIndex: 99,
}));

interface ProductFormButtonsProps {
    product: ProductData;
    onSubmit: () => any;
    onClose: () => any;
    isBusy?: boolean;
    isTabDirty?: boolean;
}

export default function ProductInfoFormCreateButton({
    product,
    onSubmit,
    onClose,
    isBusy,
    isTabDirty,
}: ProductFormButtonsProps) {
    return (
        <PaperStyled elevation={3}>
            <PreviousButtonRow>
                <StyledSecondaryButton
                    type="button"
                    name="Previous"
                    data-testid="previous-section-button"
                    onClick={onClose}
                    disabled={isBusy}
                    startIcon={<Icon iconName="ChevronLeft" fontSize="medium" color="green10" />}
                >
                    Close
                </StyledSecondaryButton>
            </PreviousButtonRow>

            {product.id && (
                <>
                    <NextButtonRow>
                        <StyledPrimaryButton
                            name="Save and Next"
                            type="button"
                            data-testid="next-section-button-1"
                            disabled={isBusy}
                            onClick={onSubmit}
                        >
                            {isTabDirty ? 'Save And Next' : 'Next'}
                        </StyledPrimaryButton>
                    </NextButtonRow>
                </>
            )}

            {!product.id && (
                <>
                    <NextButtonRow>
                        <StyledPrimaryButton
                            name="Save and Next"
                            type="button"
                            data-testid="next-section-button-2"
                            disabled={isBusy || !isTabDirty}
                            onClick={onSubmit}
                        >
                            Save And Next
                        </StyledPrimaryButton>
                    </NextButtonRow>
                </>
            )}
        </PaperStyled>
    );
}
