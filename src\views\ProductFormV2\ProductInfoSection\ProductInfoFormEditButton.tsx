import React from 'react';
import { Box, Paper, styled } from '@mui/material';
import convertPxToRem from '@treez-inc/component-library/dist/util-functions/convertPxToRem';
import { Icon } from '@treez-inc/component-library';
import { StyledPrimaryButton, StyledSecondaryButton } from '../../../styles/StyledProductForm';

const PreviousButtonRow = styled(Box)(() => ({
    float: 'left',
    padding: convertPxToRem(16),
    marginLeft: '14%',
}));

const NextButtonRow = styled(Box)(() => ({
    float: 'right',
    padding: convertPxToRem(16),
    marginRight: '12%',
}));

const SaveButtonRow = styled(Box)(() => ({
    float: 'right',
    padding: convertPxToRem(16),
    marginRight: '2px',
    paddingRight: '2px',
}));

const PaperStyled = styled(Paper)(() => ({
    position: 'fixed',
    bottom: convertPxToRem(0),
    left: convertPxToRem(0),
    right: convertPxToRem(0),
    zIndex: 99,
}));

interface ProductFormButtonsProps {
    onSubmitClick: () => any;
    onNextClick: () => any;
    onCloseClick: () => any;
    isBusy?: boolean;
    isTabDirty?: boolean;
}

export default function ProductInfoFormEditButton({
    onSubmitClick,
    onNextClick,
    onCloseClick,
    isBusy,
    isTabDirty,
}: ProductFormButtonsProps) {
    return (
        <PaperStyled elevation={3}>
            <PreviousButtonRow>
                <StyledSecondaryButton
                    type="button"
                    name="Previous"
                    data-testid="previous-section-button"
                    onClick={onCloseClick}
                    disabled={isBusy}
                    startIcon={<Icon iconName="ChevronLeft" fontSize="medium" color="green10" />}
                >
                    Close
                </StyledSecondaryButton>
            </PreviousButtonRow>
            <NextButtonRow>
                <StyledPrimaryButton
                    name="Next"
                    type="button"
                    data-testid="next-section-button"
                    disabled={isBusy}
                    onClick={onNextClick}
                >
                    Next
                </StyledPrimaryButton>
            </NextButtonRow>
            <SaveButtonRow>
                <StyledPrimaryButton 
                    name="Save" 
                    type="button" 
                    disabled={isBusy || !isTabDirty} 
                    onClick={onSubmitClick}
                    data-testid="product-info-form-next-section-button"
                >
                    Save
                </StyledPrimaryButton>
            </SaveButtonRow>
        </PaperStyled>
    );
}
