import Joi from 'joi';

const getProductInfoFormSchema = () =>
    Joi.object({
        id: Joi.string().optional(),
        name: Joi.string().required().messages({ 'string.empty': 'Product Name is required.' }),
        brandId: Joi.string().optional().allow(null, ''),
        productSubCategoryId: Joi.string().messages({ 'string.empty': 'Subcategory is required.' }),
        details: Joi.object({
            description: Joi.string().optional().allow(''),
            classification: Joi.string().optional().allow(''),
            extractionMethod: Joi.string().optional().allow(''),
        }),
        productAttributeDetails: Joi.object(),
        strain: Joi.string().optional().allow(''),
        status: Joi.string().optional(),
        images: Joi.array().optional(),
    });

export default getProductInfoFormSchema;
