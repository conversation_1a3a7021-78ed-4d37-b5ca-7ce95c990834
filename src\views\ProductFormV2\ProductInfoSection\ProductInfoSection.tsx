import React from 'react';
import { Typography } from '@mui/material/';
import { ProductCategoryDto } from '../../../interfaces/dto/productCategory';
import ProductCategorySelector from './ProductCategorySelector';
import useProduct from '../Hooks/useProduct';
import ProductInfoForm from './ProductInfoForm';
import { ProductFormSectionBox, StyledSubHeader } from '../../../styles/globalStyles';
import { AttributeCategoryData } from '../../../interfaces/dto/attributeCategoryData';
import useLoadData from '../../../hooks/useLoadData';
import attributesApiKeyStore from '../../../api/attributesApiKeyStore';
import LoadingIndicator from '../../../components/LoadingIndicator';

interface ProductInfoSectionProps {
    categories: ProductCategoryDto[];
}

const ProductInfoSection = ({ categories }: ProductInfoSectionProps) => {
    const { product, setProductUpdate } = useProduct();
    const { isLoading, data } = useLoadData<AttributeCategoryData[]>({
        queryConfig: attributesApiKeyStore.getAttributeCategories(),
    });

    const handleCategorySelection = (category: ProductCategoryDto) => {
        setProductUpdate({ productCategory: category });
    };

    return !isLoading ? (
        <ProductFormSectionBox>
            <Typography variant="h6" data-testid="product-information-section-header">
                Parent Product Information
            </Typography>
            <StyledSubHeader data-testid="product-information-section-sub-header">Product category *</StyledSubHeader>
            {product && (
                <ProductCategorySelector
                    categories={categories}
                    onSelectCategory={handleCategorySelection}
                    selectedCategory={product.productCategory}
                />
            )}
            {product && product.productCategory && data && <ProductInfoForm attributeCategories={data} />}
        </ProductFormSectionBox>
    ) : (
        <LoadingIndicator isLoading={isLoading} />
    );
};

export default ProductInfoSection;
