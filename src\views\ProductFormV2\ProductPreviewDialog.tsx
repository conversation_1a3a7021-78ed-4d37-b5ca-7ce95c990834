import React from 'react';
import { Dialog, Box, Grid, IconButton, styled } from '@mui/material';
import { CloseOutlined } from '@mui/icons-material';
import { isEmpty } from '../../utils/common';
import { ProductDto } from '../../interfaces/dto/product';
import ProductImageCarousel from '../ProductControl/ProductDetailsDrawer/contentSections/ProductImageCarousel';
import ProductHighlights from '../ProductControl/ProductDetailsDrawer/contentSections/ProductHighlights';
import ProductTitle from '../ProductControl/ProductDetailsDrawer/contentSections/ProductTitle';
import ProductDescription from '../ProductControl/ProductDetailsDrawer/contentSections/ProductDescription';
import ProductVariants from '../ProductControl/ProductDetailsDrawer/contentSections/ProductVariants';
import ProductAttributes from '../ProductControl/ProductDetailsDrawer/contentSections/ProductAttributes';

interface ProductPreviewDialogProps {
    isOpen: boolean;
    onClose: () => void;
    productCategoryName: string | undefined;
    currentProductDetails: ProductDto | undefined;
}

const PreviewDialogStyled = styled(Dialog)`
    .MuiPaper-root {
        padding: 0rem 1.5rem 1.5rem 1.5rem;
        max-width: 600px;
        width: 100%;
    }
`;

const ProductPreviewContainer = styled(Box)`
    display: flex;
    flex-direction: column;
`;

const HeaderBox = styled(Box)`
    display: flex;
    flex-direction: row;
    width: 100%;
    padding: 1.5rem 2rem 1rem 2rem;
`;

const CloseBox = styled(Grid)`
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-left: auto;
    cursor: pointer;
    padding-right: 0.625rem;
`;

const ImageContainer = styled(Box)`
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: auto;
`;

const ProductPreviewDialog: React.FC<ProductPreviewDialogProps> = ({
    isOpen,
    onClose,
    productCategoryName,
    currentProductDetails,
}) => {
    if (!currentProductDetails) return null;

    let topMgThc: number = 0;

    currentProductDetails?.variants?.forEach((variant) => {
        if (!isEmpty(variant?.details?.totalMgThc) && topMgThc < (variant?.details?.totalMgThc || 0)) {
            topMgThc = variant?.details?.totalMgThc || 0;
        }
    });

    return (
        <PreviewDialogStyled open={isOpen} onClose={onClose}>
            <HeaderBox>
                <CloseBox onClick={onClose} item md={1.5}>
                    <IconButton>
                        <CloseOutlined />
                    </IconButton>
                </CloseBox>
            </HeaderBox>
            <ProductPreviewContainer>
                <ImageContainer>
                    <ProductImageCarousel details={currentProductDetails} />
                </ImageContainer>
                <ProductHighlights productInfo={currentProductDetails} />
                <ProductTitle product={currentProductDetails} topMgThc={topMgThc} />
                <ProductDescription description={currentProductDetails?.details?.description || ''} />
                <ProductVariants
                    variants={currentProductDetails?.variants || []}
                    productCategoryName={productCategoryName || ''}
                />
                <ProductAttributes attributes={currentProductDetails?.productAttributes} />
            </ProductPreviewContainer>
        </PreviewDialogStyled>
    );
};

export default ProductPreviewDialog;
