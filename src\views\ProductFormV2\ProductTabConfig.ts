import { TabInfo } from './Types/TabInfo';
import TabName from './Types/TabNames.enum';

const getAllTabs = (selectedTab: TabName): TabInfo[] => [
    {
        tab: TabName.PRODUCT_INFO,
        label: 'Product Information',
        isSelected: selectedTab === TabName.PRODUCT_INFO,
    },
    {
        tab: TabName.SKU,
        label: 'SKU',
        isSelected: selectedTab === TabName.SKU,
    },
    // {
    //     tab: TabName.ATTRIBUTES,
    //     label: 'Attributes',
    //     isSelected: selectedTab === TabName.ATTRIBUTES,
    // },
    {
        tab: TabName.PRICING,
        label: 'Pricing',
        isSelected: selectedTab === TabName.PRICING,
    },
    // {
    //     tab: TabName.IMAGES,
    //     label: 'Images',
    //     isSelected: selectedTab === TabName.IMAGES,
    // },
];

export default getAllTabs;
