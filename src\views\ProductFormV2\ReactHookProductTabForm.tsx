import React, { ReactNode, useEffect, useState } from 'react';
import Box from '@mui/material/Box';
import { FormProvider, UseFormReturn } from 'react-hook-form';
import { styled } from '@mui/system';
import useProductTabNavigation from './Hooks/useProductTabNavigation';
import MutationErrorDialog from './MutationErrorDialog';
import ProductFormButtons from './ProductFormButtons';
import MissingPricesDialog from './MissingPricesDialog';
import { CallbackProps, SubmitFn } from './Types/SubmitFormTypes';
import DirtyFormConfirmDialog from './DirtyFormConfirmDialog';
import LoadingIndicator from '../../components/LoadingIndicator';
import { MutationErrorData } from '../../utils/MutationResponseUtil';
import useSnackbarContext from '../../hooks/snackbar/useSnackbarContext';
import { BaseNavigationOption } from './Types/NavigationOption';
import { isEmpty } from '../../utils/common';

interface ReactHookFormProps {
    onSubmit: SubmitFn;
    children: ReactNode;
    isBusy?: boolean;
    formName: string;
    hasMissingPrices?: boolean;
    isCustomPriceDirty?: boolean;
    formContextProps: UseFormReturn;
    renderButtons?: (props: {
        handleNavigate: (submit: boolean, nav: BaseNavigationOption) => any;
        isBusy?: boolean;
    }) => ReactNode;
    enableNavigation?: boolean;
}

const FormContainer = styled(Box)({
    position: 'relative',
    '&.saving': {
        opacity: 0.6,
        pointerEvents: 'none',
    },
});

const ReactHookProductTabForm = ({
    onSubmit,
    formContextProps,
    children,
    formName,
    isBusy,
    hasMissingPrices,
    isCustomPriceDirty,
    renderButtons,
    enableNavigation = true,
}: ReactHookFormProps) => {
    const { navigate, setTabDirty } = useProductTabNavigation();
    const { setSnackbar } = useSnackbarContext();
    const [nextNavigation, setNextNavigation] = useState<BaseNavigationOption>();
    const [isBlockingError, setIsBlockingError] = useState(false);
    const [mutationErrors, setMutationErrors] = useState<MutationErrorData[] | undefined>();
    const [isIncompleteDialogOpen, setIncompleteDialogOpen] = useState(false);

    useEffect(() => {
        setTabDirty(formContextProps.formState.isDirty && !isEmpty(formContextProps.formState.dirtyFields));
    }, [formContextProps.formState]);

    const handleValidationErrors = (error: any) => {
        if (error) {
            const allErrors = Object.entries(error).reduce((acc: any, [, fieldErrors]: any) => {
                if (Array.isArray(fieldErrors)) {
                    (fieldErrors as Array<{ [key: string]: string }>).forEach((fieldError) => {
                        acc.push(Object.values(fieldError)[0]);
                    });
                } else if (fieldErrors.message) {
                    acc.push(fieldErrors.message);
                }
                return acc;
            }, []);

            setSnackbar({
                message: `${
                    allErrors && allErrors.length > 0 && typeof allErrors[0] === 'string'
                        ? `${allErrors[0]} ${allErrors.length > 1 ? `(+${allErrors.length - 1} more)` : ''}`
                        : 'There are some validation errors in your form.'
                }`,
                severity: 'warning',
                iconName: 'Warning',
            });
        }
    };

    const handleNavigate = async (submit: boolean, nav: BaseNavigationOption) => {
        if (submit) {
            setNextNavigation(nav);
            if (onSubmit && formContextProps) {
                formContextProps.handleSubmit(
                    onSubmit((data?: CallbackProps) => {
                        if (!isEmpty(data?.errors)) {
                            setIsBlockingError(data?.isBlockingError ?? false);
                            setMutationErrors(data?.errors);
                            if (!data?.isBlockingError && data?.formValue) {
                                formContextProps.reset(data?.formValue, { keepDirty: true, keepErrors: true });
                            }
                        } else {
                            navigate({ ...nav, force: true, productdata: data?.productData });
                            formContextProps.reset(data?.formValue, { keepDirty: false, keepErrors: true });
                        }
                    }),
                    (data: any) => {
                        handleValidationErrors(data);
                    },
                )();
            }
        } else {
            setMutationErrors(undefined);
            setNextNavigation(undefined);
            if (nav) {
                navigate({ ...nav });
            }
        }
    };

    return (
        <>
            <FormProvider {...formContextProps}>
                <MissingPricesDialog
                    isIncompleteDialogOpen={isIncompleteDialogOpen}
                    setIncompleteDialogOpen={setIncompleteDialogOpen}
                />
                <FormContainer className={isBusy ? 'saving' : ''}>
                    <form onSubmit={formContextProps.handleSubmit(onSubmit())} id={formName}>
                        <Box>{children}</Box>
                        {renderButtons ? (
                            renderButtons({ handleNavigate, isBusy })
                        ) : (
                            <ProductFormButtons
                                onNavigate={handleNavigate}
                                isBusy={isBusy}
                                isCustomPriceDirty={isCustomPriceDirty}
                                hasMissingPrices={hasMissingPrices}
                                setIncompleteDialogOpen={setIncompleteDialogOpen}
                                enableNavigation={enableNavigation}
                            />
                        )}
                        {!isEmpty(mutationErrors) && mutationErrors && nextNavigation && (
                            <MutationErrorDialog
                                errors={mutationErrors}
                                navigate={nextNavigation}
                                onNavigate={handleNavigate}
                                isBlockingError={isBlockingError}
                            />
                        )}
                        {isEmpty(mutationErrors) && <DirtyFormConfirmDialog onNavigate={handleNavigate} />}
                    </form>
                </FormContainer>
                <LoadingIndicator isLoading={isBusy} />
            </FormProvider>
        </>
    );
};
export default ReactHookProductTabForm;
