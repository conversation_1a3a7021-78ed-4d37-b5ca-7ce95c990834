import React from 'react';
import { Typography } from '@mui/material/';
import { Modal } from '@treez-inc/component-library';

interface DialogProps {
    dialogOpen: boolean;
    setDialogOpen: (val: boolean) => void;
}

export default function SubcategoryChangeDialog({ dialogOpen, setDialogOpen }: DialogProps) {
    const handleClose = () => {
        setDialogOpen(false);
    };

    return (
        <Modal
            testId="product-form-exit-modal"
            title="Bulk Pricing Conflict"
            content={
                <Typography>
                    This product is currently using a bulk price tier, which is only supported for bulk subcategories.
                    To change the subcategory to a non-bulk type, update the product to use a flat price first.
                </Typography>
            }
            onClose={handleClose}
            open={dialogOpen}
        />
    );
}
