/* eslint-disable @typescript-eslint/lines-between-class-members */
/* eslint-disable max-classes-per-file */
import { ProductData } from '../../../interfaces/dto/productData';
import { TabNavigation, PageName } from './Navigation.enum';
import TabName from './TabNames.enum';

export class BaseNavigationOption {
    force?: boolean;
    productdata?: ProductData;

    constructor(public type: 'Tab' | 'Page' | 'Ignore') {}
}

export class TabNavigationOption extends BaseNavigationOption {
    constructor(public navigation: TabNavigation, public tabName?: TabName, public force?: boolean) {
        super('Tab');
    }
}

export class PageNavigationOption extends BaseNavigationOption {
    constructor(public pageName: PageName, public force?: boolean) {
        super('Page');
    }
}

export class IgnoreNavigationOption extends BaseNavigationOption {
    constructor() {
        super('Ignore');
    }
}
