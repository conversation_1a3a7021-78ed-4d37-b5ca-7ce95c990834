import { PricingMethodType } from '../../../utils/constants';

export interface PricingFormData {
    pricingMethod: PricingMethodType;
    priceTierId: string | null;
    defaultPrices: { base: string | null }[];
    stores: {
        id?: string;
        skus: {
            id?: string;
            defaultPrices: { base: string | null };
            unitCount: number;
            uom: string | null;
            amount: number | null;
            merchandiseSize: string | null;
            priceTierId?: string | null;
            isCustomPrice?: boolean;
        }[];
    }[];
}

export interface PriceTierThreshold {
    end: number;
    start: number;
    value: number;
    tolerance: number;
}

export interface PriceTier {
    id: string;
    createdAt: string;
    updatedAt: string;
    deletedAt: string | null;
    organizationId: string;
    name: string;
    type: string;
    isActive: boolean;
    thresholds: PriceTierThreshold[];
}
