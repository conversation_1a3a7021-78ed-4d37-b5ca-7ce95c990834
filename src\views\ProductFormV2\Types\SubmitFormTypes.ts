import { MutationResponse } from '../../../hooks/useSaveData';
import { ProductData } from '../../../interfaces/dto/productData';
import { MutationErrorData } from '../../../utils/MutationResponseUtil';

export interface CallbackProps {
    mutationResult?: MutationResponse<any>;
    errors?: MutationErrorData[];
    isBlockingError?: boolean;
    productData?: ProductData;
    formValue?: any;
}

export type SubmitCallBack = (data?: CallbackProps) => void;

export type SubmitFn = (callback?: SubmitCallBack) => (values: any) => Promise<void>;
