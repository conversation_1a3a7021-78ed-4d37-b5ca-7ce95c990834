import React from 'react';
import { Grid } from '@mui/material/';
import { UseFieldArrayRemove } from 'react-hook-form';
import { IconButton } from '@treez-inc/component-library';
import HookFormInput from '../../../components/hook-form-v2/HookFormInput';

interface AdditionalSkuComponentProps {
    disabled?: boolean;
    fieldName: string;
    index: number;
    remove: UseFieldArrayRemove;
}

const AdditionalSkuComponent = ({ disabled = false, fieldName, index, remove }: AdditionalSkuComponentProps) => (
    <>
        <Grid>
            <HookFormInput disabled={disabled} name={`${fieldName}.${index}`} label="Additional SKU Barcode" />
        </Grid>
        <Grid>
            <IconButton
                disabled={disabled}
                iconName="Delete"
                variant="secondary"
                onClick={() => {
                    remove(index);
                }}
            />
        </Grid>
    </>
);

export default AdditionalSkuComponent;
