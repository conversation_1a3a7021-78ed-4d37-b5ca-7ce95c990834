import React from 'react';
import { styled, Box, Typography, Grid } from '@mui/material/';
import HookFormInput from '../../../components/hook-form-v2/HookFormInput';
import VariantReferenceIds from './VariantReferenceIds';
import VariantMenuHideSwitch from './VariantMenuHideSwitch';
import VariantAccordion from './VariantAccordion';
import { VariantField } from '../../../styles/globalStyles';
import HookFormInputPricing from '../../../components/hook-form-v2/HookFormInputPricingSamplePromo';
import SkuComponent from './SkuComponent';
import { SkuDto } from '../../../interfaces/dto/sku';

export const ExternalField = styled(Box)({
    padding: '1em 0 0.5em 0',
});

const VariantDetailsWrapper = styled(Box)({
    display: 'grid',
    gap: '1.1875em',
    marginTop: '0.5em',
});

const SectionContainer = styled(Box)({
    display: 'grid',
    gap: '0.6875em',
});

export const EcommerceBlock = styled(Box)({
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: '0.3125em',
});

export const GlobalDescriptionOuterContainer = styled(Box)(() => ({
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'flex-start',
    alignSelf: 'stretch',
    gap: '0.4375em',
}));

export const GlobalDescriptionContainer = styled(Box)(() => ({
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'flex-start',
    alignSelf: 'stretch',
}));

export const VariantDetailsHeader = styled(Typography)(({ theme }) => ({
    color: theme.palette.primaryBlackText.main,
    fontSize: '0.75rem',
    fontWeight: 500,
    lineHeight: '1em',
}));

const StyledContentContainer = styled(Box)(() => ({
    display: 'flex',
    flexDirection: 'column',
    gap: '0.75em',
}));

const ChildStyledBox = styled(Box)`
    border: none;
    box-shadow: none;
    padding: 0.625rem 0;
    margin: 0 !important;
    & .MuiAccordionSummary-root {
        gap: 0.375rem;
        overflow: hidden;
        height: 0 !important;
        min-height: 1.25rem !important;
        padding: 0;
        flex-direction: row-reverse;
    }

    & .MuiAccordionSummary-content {
        margin: 0;
    }
`;

const PricingContainer = styled(Box)`
    display: grid;
    gap: 1em;

    grid-template-columns: repeat(2, 1fr);
`;

interface ChildVariantDetailsProps {
    variant: SkuDto;
    fieldName: string;
    componentIndex: number;
    removeItem: (index: number) => void;
}

const ChildVariantForm = ({ variant, fieldName, componentIndex, removeItem }: ChildVariantDetailsProps) => {
    const isGlobalContent: boolean = !!variant.verifiedReferenceId;

    return (
        <VariantAccordion variant={variant} index={componentIndex} removeItem={removeItem}>
            <StyledContentContainer>
                <PricingContainer>
                    <HookFormInputPricing name={`${fieldName}.${componentIndex}.defaultPrices.base`} label="Price *" />
                    <Typography sx={{ color: '#595959', fontSize: '.85rem' }}>
                        Samples and promos are priced based on this field. Tier pricing and store-level custom prices
                        are not applicable.
                    </Typography>
                </PricingContainer>
                <ChildStyledBox>
                    <VariantDetailsWrapper>
                        <SectionContainer data-testid="variant-details-container">
                            <Grid container rowSpacing={2} columnSpacing={1.5} alignItems="center">
                                <SkuComponent fieldName={fieldName} componentIndex={componentIndex} />
                                <VariantReferenceIds
                                    disabled={isGlobalContent}
                                    useRefFieldName={`${fieldName}.${componentIndex}.useReferenceId`}
                                    fieldName={`${fieldName}.${componentIndex}.referenceIds`}
                                />
                            </Grid>
                        </SectionContainer>
                        <SectionContainer data-testid="ecommerce-container">
                            <EcommerceBlock>
                                <VariantDetailsHeader data-testid="ecommerce-header">
                                    ECOMMERCE INFORMATION
                                </VariantDetailsHeader>
                                <VariantMenuHideSwitch
                                    disabled={isGlobalContent}
                                    name={`${fieldName}.${componentIndex}.details.hideFromEcomMenu`}
                                    label="Hide from menu"
                                    toolTipText="When this toggle is on, the sku is hidden from your eCommerce menu."
                                />
                            </EcommerceBlock>
                            <GlobalDescriptionOuterContainer>
                                <VariantField sx={{ width: '100%' }}>
                                    <HookFormInput
                                        name={`${fieldName}.${componentIndex}.details.menuTitle`}
                                        label="Menu Title"
                                    />
                                </VariantField>
                            </GlobalDescriptionOuterContainer>
                        </SectionContainer>
                    </VariantDetailsWrapper>
                </ChildStyledBox>
            </StyledContentContainer>
        </VariantAccordion>
    );
};

export default ChildVariantForm;
