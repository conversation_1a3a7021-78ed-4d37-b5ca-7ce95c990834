import React, { useEffect, useState } from 'react';
import { useFormContext } from 'react-hook-form';
import CheckboxInput from '../../../components/CheckboxInput';
import { SkuDto } from '../../../interfaces/dto/sku';

export interface HookFormVariantReferenceIdsProps {
    disabled?: boolean;
    label: string;
    fieldName: string;
    childKey: 'isSample' | 'isPromo';
    onClick: () => void;
}

export default function CreateChildButton({
    disabled = false,
    label,
    fieldName,
    childKey,
    onClick,
}: HookFormVariantReferenceIdsProps) {
    const { watch } = useFormContext();
    const [checkboxValue, setCheckboxValue] = useState(false);
    const children: SkuDto[] = watch(fieldName);

    useEffect(() => {
        if (children) {
            const hasChild: boolean = children?.some((v) => v.details?.[childKey]);
            setCheckboxValue(hasChild);
        }
    }, [children]);

    const handleSelect = (checked?: boolean) => {
        if (checked) {
            onClick();
        }
    };

    return (
        <CheckboxInput
            label={label}
            onChange={handleSelect}
            value={checkboxValue}
            disabled={checkboxValue || disabled}
            data-testid={`checkbox-${fieldName?.toLowerCase().replaceAll(' ', '-')}`}
        />
    );
}
