import React from 'react';
import { convertPxToRem, StaticChip } from '@treez-inc/component-library';
import { Controller, useFormContext } from 'react-hook-form';
import { Box, styled } from '@mui/material';
import ToolTipWrapper from '../../../components/ToolTipWraper';
import ButtonWithConfirmation from '../../../components/ButtonWithConfirmation';
import useProduct from '../Hooks/useProduct';
import VariantTitle from './VariantTitle';
import { ACTIVATE_VARIANT_TOOL_TIP_MSG, Status } from '../../../utils/constants';
import { SkuDto } from '../../../interfaces/dto/sku';

interface HookFormVariantStatusToggleProps {
    name: string;
    variant: SkuDto;
}

const FirstMessageBox = styled(Box)(() => ({
    columnGap: convertPxToRem(10),
    display: 'flex',
    flexDirection: 'row',
}));

const MessageBox = styled(Box)(() => ({
    paddingBottom: convertPxToRem(10),
}));

export default function HookFormVariantStatusToggle({ name, variant }: HookFormVariantStatusToggleProps) {
    const { product } = useProduct();
    const { control } = useFormContext();

    const deactivateVariantMessage = () => (
        <>
            <FirstMessageBox>
                Q<MessageBox>Are you sure you want to deactivate</MessageBox>
                <VariantTitle variant={variant} productCategoryName={product.productCategory?.name} />
                <MessageBox>?</MessageBox>
            </FirstMessageBox>
            <MessageBox>
                Deactivated variants cannot be invoiced in Purchasing or edited in the Catalog. You can reactivate them
                later, if needed.
            </MessageBox>
            <MessageBox>
                Note: Any remaining inventory will still be sellable. To prevent sales, move the items to an unsellable
                location in Inventory Management.
            </MessageBox>
        </>
    );

    const activateVariantMessage = () => (
        <>
            <FirstMessageBox>
                <MessageBox>Are you sure you want to activate</MessageBox>
                <VariantTitle variant={variant} productCategoryName={product.productCategory?.name} />
                <MessageBox>?</MessageBox>
            </FirstMessageBox>
            <MessageBox>
                Active products can be associated with inventory and invoiced in Purchasing. You can deactivate it
                later, if needed.
            </MessageBox>
        </>
    );

    return (
        <Controller
            control={control}
            name={name}
            render={({ field: { onChange, value } }) => (
                <>
                    {value === Status.INACTIVE && (
                        <>
                            <StaticChip color="gray" label="Deactivated" variant="filled" />
                            {product.status === Status.INACTIVE && (
                                <ToolTipWrapper toolTip={ACTIVATE_VARIANT_TOOL_TIP_MSG}>
                                    <ButtonWithConfirmation
                                        disabled={product.status === Status.INACTIVE}
                                        iconName="CheckmarkCircle"
                                        small
                                        label="Activate"
                                        confirmModel={{
                                            title: 'Confirm Activation',
                                            content: activateVariantMessage(),
                                        }}
                                        onConfirm={() => onChange(Status.ACTIVE)}
                                    />
                                </ToolTipWrapper>
                            )}
                            {product.status !== Status.INACTIVE && (
                                <ButtonWithConfirmation
                                    iconName="CheckmarkCircle"
                                    small
                                    label="Activate"
                                    confirmModel={{
                                        title: 'Confirm Activation',
                                        content: activateVariantMessage(),
                                    }}
                                    onConfirm={() => onChange(Status.ACTIVE)}
                                />
                            )}
                        </>
                    )}
                    {value === Status.ACTIVE && (
                        <ButtonWithConfirmation
                            iconName="VisibilityOff"
                            label="Deactivate"
                            small
                            confirmModel={{
                                title: 'Confirm Deactivation',
                                content: deactivateVariantMessage(),
                            }}
                            onConfirm={() => onChange(Status.INACTIVE)}
                        />
                    )}
                </>
            )}
        />
    );
}
