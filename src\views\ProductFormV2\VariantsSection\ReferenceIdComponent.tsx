import React from 'react';
import { Grid } from '@mui/material/';
import { useFormContext } from 'react-hook-form';
import HookFormInput from '../../../components/hook-form-v2/HookFormInput';

interface ReferenceIdsProps {
    disabled?: boolean;
    fieldName: string;
    index: number;
}

const ReferenceIdComponent = ({ disabled, fieldName, index }: ReferenceIdsProps) => {
    const { getValues } = useFormContext();

    const isImportRefId = () => {
        const sourceName: string = getValues(`${fieldName}.${index}.sourceName`);
        return sourceName?.toLowerCase() === 'importvariantreferenceid';
    };

    return (
        <>
            <Grid item xs={6}>
                <HookFormInput
                    disabled={disabled || isImportRefId()}
                    name={`${fieldName}.${index}.sourceName`}
                    label="Source Name"
                />
            </Grid>
            <Grid item xs={6}>
                <HookFormInput
                    disabled={disabled || isImportRefId()}
                    name={`${fieldName}.${index}.sourceId`}
                    label="External ID"
                />
            </Grid>
        </>
    );
};

export default ReferenceIdComponent;
