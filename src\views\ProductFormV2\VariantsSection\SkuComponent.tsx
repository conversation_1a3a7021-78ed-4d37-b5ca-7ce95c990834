import React from 'react';
import { useFieldArray, useFormContext } from 'react-hook-form';
import { Grid, styled } from '@mui/material/';
import { Button } from '@treez-inc/component-library';
import AdditionalSkuComponent from './AdditionalSkuComponent';
import HookFormInput from '../../../components/hook-form-v2/HookFormInput';

interface SkuComponentProps {
    disabled?: boolean;
    fieldName: string;
    componentIndex: number;
}

const Wrapper = styled(Grid)(() => ({
    display: 'grid',
    gridTemplateColumns: 'repeat(2, 1fr)',
    padding: '1em 0 0 12px',
    alignItems: 'center',
    gap: '1em',
    width: '100%',
}));

const SkuComponent = ({ disabled = false, fieldName, componentIndex }: SkuComponentProps) => {
    const { control } = useFormContext();
    const additionalSkuField = `${fieldName}.${componentIndex}.additionalSku`;

    const {
        fields: additionalSkus,
        append: addSku,
        remove: removeSku,
    } = useFieldArray({
        control,
        name: additionalSkuField,
        keyName: 'refId',
    });

    return (
        <Wrapper>
            <Grid>
                <HookFormInput name={`${fieldName}.${componentIndex}.sku`} label="SKU Barcode" />
            </Grid>
            <Grid>
                <Button
                    disabled={disabled}
                    label="Add SKU Barcode"
                    iconName="Add"
                    onClick={() => addSku('')}
                    variant="text"
                />
            </Grid>
            {additionalSkus.length > 0 &&
                additionalSkus.map((field, index) => (
                    <AdditionalSkuComponent
                        disabled={disabled}
                        fieldName={additionalSkuField}
                        index={index}
                        key={field.refId}
                        remove={removeSku}
                    />
                ))}
        </Wrapper>
    );
};

export default SkuComponent;
