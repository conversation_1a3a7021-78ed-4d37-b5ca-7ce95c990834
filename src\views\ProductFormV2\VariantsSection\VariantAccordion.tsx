import React, { ReactNode, useEffect, useState } from 'react';
import { AccordionDetails, Box, styled } from '@mui/material/';
import { allColors, Button, StaticChip } from '@treez-inc/component-library';
import VariantTitle from './VariantTitle';
import useProduct from '../Hooks/useProduct';
import { SkuDto } from '../../../interfaces/dto/sku';
import { Status } from '../../../utils/constants';

const StyledVariantIndicators = styled(Box)(() => ({
    display: 'flex',
    alignItems: 'center',
    gap: '0.5rem',
}));
const StyledAccordionContainer = styled(Box)<{ customcolor: string }>((props) => ({
    padding: '0.625rem',
    marginTop: '1rem',
    borderRadius: '0.625rem',
    border: `0.0625rem solid ${props.customcolor}`,
}));

interface VariantAccordionProps {
    variant: SkuDto;
    children: ReactNode;
    index: number;
    removeItem: (index: number) => void;
}

function VariantAccordion({ children, variant, removeItem, index }: VariantAccordionProps) {
    const [isExpanded, setIsExpanded] = useState(false);
    const { isBulkProductWithSingleSku } = useProduct();

    useEffect(() => {
        // expand accordion for parent variant and collapse for sample/promo variants
        if (
            !(variant.status === Status.INACTIVE) &&
            !variant.details?.isSample &&
            !variant.details?.isPromo &&
            !variant.parentId &&
            !isExpanded
        ) {
            setIsExpanded(true);
        }
    }, [variant]);

    const handleRemoveVariant = () => {
        removeItem(index);
    };

    const bulkProductWithSingleSku = isBulkProductWithSingleSku();

    return (
        <StyledAccordionContainer customcolor={allColors.grey04.main}>
            <Box data-testid="variant-header">
                {!bulkProductWithSingleSku && (
                    <Box display="grid" gridTemplateColumns="repeat(2, 1fr)" padding="0.35em">
                        <StyledVariantIndicators>
                            <VariantTitle variant={variant} />
                            {!variant.id && <StaticChip color="blue" label="New" variant="filled" />}
                        </StyledVariantIndicators>
                        {!variant.id && (
                            <Box justifySelf="flex-end" marginRight="0.45em">
                                <Button
                                    iconName="Delete"
                                    small
                                    label="Delete"
                                    onClick={handleRemoveVariant}
                                    variant="secondary"
                                />
                            </Box>
                        )}
                    </Box>
                )}
            </Box>
            <AccordionDetails>{children}</AccordionDetails>
        </StyledAccordionContainer>
    );
}

export default VariantAccordion;
