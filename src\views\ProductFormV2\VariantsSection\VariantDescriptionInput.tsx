import React, { useEffect, useState } from 'react';
import { allColors } from '@treez-inc/component-library';
import { useFormContext } from 'react-hook-form';
import HookFormInput from '../../../components/hook-form-v2/HookFormInput';
import CheckboxInput from '../../../components/CheckboxInput';
import useProduct from '../Hooks/useProduct';
import { VariantField } from '../../../styles/globalStyles';

export interface HookFormInputProps {
    name: string;
    label: string;
}

export default function HookFormVariantDescriptionInput({ name, label }: HookFormInputProps) {
    const { product } = useProduct();
    const { setValue, getValues } = useFormContext();
    const [useGlobalDescription, setUseGlobalDescription] = useState(
        getValues(name) === undefined || getValues(name) === '' || product.details?.description === getValues(name),
    );

    useEffect(() => {
        if (useGlobalDescription) {
            setValue(name, product.details?.description, {
                shouldDirty: product.details?.description !== getValues(name),
            });
        } else if (product.details?.description === getValues(name)) {
            setValue(name, '');
        }
    }, [useGlobalDescription]);

    const handleGlobalDescriptionChange = (checked?: boolean) => {
        setUseGlobalDescription(!!checked);
    };

    return (
        <>
            <VariantField>
                <CheckboxInput
                    value={useGlobalDescription}
                    label="Use global description"
                    onChange={handleGlobalDescriptionChange}
                />
            </VariantField>
            <VariantField
                sx={{
                    color: useGlobalDescription ? allColors.grey04.main : '',
                    fontStyle: useGlobalDescription ? 'italic' : 'normal',
                    width: '100%',
                }}
            >
                <HookFormInput name={name} label={label} disabled={useGlobalDescription} multiline rows={3} />
            </VariantField>
        </>
    );
}
