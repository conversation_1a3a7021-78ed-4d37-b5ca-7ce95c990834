/* eslint-disable import/no-cycle */
import React from 'react';
import { useFieldArray, useFormContext } from 'react-hook-form';
import { ProductCategoryDto } from '../../../interfaces/dto/productCategory';
import ChildVariantForm from './ChildVariantForm';
import useProduct from '../Hooks/useProduct';
import VariantForm from './VariantForm';
import { SkuDto } from '../../../interfaces/dto/sku';

export interface VariantDetailsProps {
    fieldName: string;
    variant: SkuDto;
    componentIndex: number;
    selectedCategory: ProductCategoryDto;
    removeItem: (index: number) => void;
}

const VariantDetailsForm = ({
    fieldName,
    variant,
    componentIndex,
    selectedCategory,
    removeItem,
}: VariantDetailsProps) => {
    const { product, isBulkProductWithSingleSku } = useProduct();

    const { control, getValues, setValue } = useFormContext();

    const { fields, remove: removeChild } = useFieldArray({
        control,
        name: `${fieldName}.${componentIndex}.children`,
        keyName: 'fieldChildKeyId',
    });

    const buildVariantChildDefaultData = (currentVariant: SkuDto, isSample?: boolean, isPromo?: boolean) => {
        const { details, defaultPrices, ...restOfParent } = currentVariant;
        const formDefaultValue: any = {
            ...restOfParent,
            uom: currentVariant.uom ? currentVariant.uom : null,
            id: undefined,
            defaultPrices: { base: '' },
            sku: '',
            additionalSku: [],
            useReferenceId: false,
            referenceIds: [{ sourceName: '', sourceId: '' }],
            details: {
                ...product.productCategory?.variantFormDetails?.reduce(
                    (acc: any, current) => ({ ...acc, [current.input]: '' }),
                    {},
                ),
                ...variant.details,
                isSample,
                isPromo,
                hideFromEcomMenu: true,
                menuTitle: '',
            },
        };

        return formDefaultValue;
    };

    const handleRemoveChild = (index: number) => {
        removeChild(index);
    };

    const handleCreateChild = (options: { isSample?: boolean; isPromo?: boolean }) => {
        if (product?.id) {
            const newData = getValues(`${fieldName}.${componentIndex}`);
            const vData = buildVariantChildDefaultData(newData, options.isSample, options.isPromo);

            setValue(`${fieldName}.${componentIndex}.children`, [...fields, vData], { shouldDirty: true });
        }
    };

    const getChildrenCount = (index: number) => getValues(`variants.${index}.children`)?.length || 0;

    const showSampleAndpromo = !isBulkProductWithSingleSku();

    return (
        <>
            <VariantForm
                fieldName={fieldName}
                variant={variant}
                componentIndex={componentIndex}
                selectedCategory={selectedCategory}
                removeItem={removeItem}
                childrenCount={getChildrenCount(componentIndex) || 0}
                onCreateChild={handleCreateChild}
            />
            {showSampleAndpromo &&
                fields.map((field: any, index: number) => (
                    <ChildVariantForm
                        key={`Child-${field.fieldChildKeyId}`}
                        variant={field}
                        fieldName={`${fieldName}.${componentIndex}.children`}
                        componentIndex={index}
                        removeItem={handleRemoveChild}
                    />
                ))}
        </>
    );
};

export default VariantDetailsForm;
