import React from 'react';
import { useFormContext } from 'react-hook-form';
import ValidationErrorBox from '../../../components/ValidationErrorBox';

interface VariantErrorIndicatorProps {
    arrayName: string;
    index: number;
}

const useVariantErrorIndicator = ({ arrayName, index }: VariantErrorIndicatorProps) => {
    const {
        getFieldState,
        formState: { errors },
    } = useFormContext();

    const hasError = (): boolean => {
        if (errors[`${arrayName}.${index}`]) {
            return !!errors[`${arrayName}.${index}`]?.message?.toString();
        }
        const fieldState = getFieldState(`${arrayName}.${index}`);
        return fieldState.invalid;
    };

    const VariantErrorIndicator = () =>
        hasError() ? <ValidationErrorBox fieldName="variant-details">*</ValidationErrorBox> : <></>;

    return {
        hasError,
        VariantErrorIndicator,
    };
};

export default useVariantErrorIndicator;
