import React, { useEffect, useLayoutEffect, useState } from 'react';
import { sortBy } from 'lodash';
import { Box, Grid, styled } from '@mui/material';
import { Icon, Tooltip } from '@treez-inc/component-library';
import { useFormContext, useWatch } from 'react-hook-form';
import { v4 as uuidv4 } from 'uuid';
import { ObjectType } from '@treez-inc/file-management';
import HookFormInput from '../../../components/hook-form-v2/HookFormInput';
import { FormDetailsProps, InputType, ProductCategoryDto } from '../../../interfaces/dto/productCategory';
import HookFormDynamicField from '../../../components/hook-form-v2/HookFormDynamicField';
import HookFormSwitch from '../../../components/hook-form-v2/HookFormSwitch';
import HookFormVariantDescriptionInput from './VariantDescriptionInput';
import VariantReferenceIds from './VariantReferenceIds';
import VariantAccordion from './VariantAccordion';
import CreateChildButton from './CreateChildButton';
import useProduct from '../Hooks/useProduct';
import PRODUCT_CATEGORIES, {
    COMPLIANCE_FIELDS_ENUM,
    PRODUCT_PACKAGED_GOODS,
    SKU_RUN_TIME_FIELDS,
} from '../../../utils/product-categories-data';
import { VariantField } from '../../../styles/globalStyles';
import SkuComponent from './SkuComponent';
import ImagesContainer from '../Images/ImagesContainer';
import HookFormSelect from '../../../components/hook-form-v2/HookFormSelect';
import { MERCH_SIZE, MERCH_SIZES } from '../../../utils/merchSizes-data';
import VariantSizeDefinitionSubheader from './VariantSizeDefinition';
import { isHeavilyManufacturedGood, Status, uomTypes } from '../../../utils/constants';
import { SkuDetailsDto } from '../../../interfaces/dto/skuDetails';
import { SkuDto } from '../../../interfaces/dto/sku';
import { getVariantSizeLabel } from '../../../utils/variantCardUtils';
import HookFormVariantStatusToggle from './HookFormVariantStatusToggle';
// eslint-disable-next-line import/no-cycle
import { buildVariantDefaultData, getDefaultUom } from './VariantsSection';
import './VariantForm.override.styles.css';
import { filterForBaseSkus } from '../../../utils/productUtils';
import { isLooselyEqual } from '../../../utils/common';
import { sameAsAmountCategories } from './Variant.constants';

const VariantDetailsWrapper = styled(Box)({
    display: 'grid',
    gap: '1.2em',
    '& > *:nth-child(2)': {
        marginTop: '-0.35em',
    },
});

const SectionContainer = styled(Box)({
    display: 'grid',
    gap: '0.6875rem',
});

const EcommerceBlock = styled(Grid)({});

const GlobalDescriptionOuterContainer = styled(Box)(() => ({
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'flex-start',
    alignSelf: 'stretch',
    gap: '0.4375rem',
}));

const GlobalDescriptionContainer = styled(Box)(() => ({
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'flex-start',
    alignSelf: 'stretch',
}));

const VariantDetailsHeader = styled(Grid)(({ theme }) => ({
    display: 'flex',
    width: '100%',
    color: theme.palette.primaryBlackText.main,
    fontSize: '0.75rem',
    fontWeight: 500,
    lineHeight: '1rem',
    textTransform: 'uppercase',
    gap: '0.25em',
}));

const VariantDetailsHeaderGrid = styled(Grid)(({ theme }) => ({
    display: 'grid',
    gridTemplateColumns: 'repeat(2, 1fr)',
    alignItems: 'center',
    color: theme.palette.primaryBlackText.main,
    fontSize: '0.75rem',
    fontWeight: 500,
    lineHeight: '1rem',
    textTransform: 'uppercase',
    gap: '0.25em',
}));

const VariantStatusBox = styled(Box)`
    display: flex;
    gap: '0.32em';
`;

const StyledComplianceFieldsContainer = styled(Grid)(() => ({
    paddingTop: '0.5rem',
    marginBottom: '0.3125rem',
}));

const HidemenuContainer = styled(Box)({
    display: 'flex',
    textAlign: 'left',
    alignItems: 'center',
});

const StyledIconContainer = styled(Box)(() => ({
    display: 'flex',
}));

const NET_WEIGHT_UOM_OPTIONS = [
    {
        displayName: 'FLUID OUNCES',
        displayValue: 'FLUID OUNCES',
    },
    {
        displayName: 'GRAMS',
        displayValue: 'GRAMS',
    },
    {
        displayName: 'LITERS',
        displayValue: 'LITERS',
    },
    {
        displayName: 'MILLIGRAMS',
        displayValue: 'MILLIGRAMS',
    },
    {
        displayName: 'MILLILITERS',
        displayValue: 'MILLILITERS',
    },
    {
        displayName: 'OUNCES',
        displayValue: 'OUNCES',
    },
];

const complianceFields = Object.keys(COMPLIANCE_FIELDS_ENUM) as Array<keyof SkuDetailsDto>;

const isProductCategory = (value: string): value is PRODUCT_CATEGORIES =>
    Object.values(PRODUCT_CATEGORIES).includes(value as PRODUCT_CATEGORIES);

export interface VariantDetailsProps {
    fieldName: string;
    variant: SkuDto;
    componentIndex: number;
    selectedCategory: ProductCategoryDto;
    childrenCount: number;
    removeItem: (index: number) => void;
    onCreateChild: (options: { isSample?: boolean; isPromo?: boolean }) => void;
}

const VariantForm = ({
    variant,
    fieldName,
    componentIndex,
    selectedCategory,
    removeItem,
    childrenCount,
    onCreateChild,
}: VariantDetailsProps) => {
    const { product, isBulkProduct, isBulkProductWithSingleSku } = useProduct();
    const productCategoryName: string = selectedCategory.name;

    const variants = filterForBaseSkus(product.variants!);

    const defaultUom: string | null = getDefaultUom(productCategoryName, isBulkProductWithSingleSku());
    const defaultAmount: number | null = isBulkProductWithSingleSku() ? 1 : null;

    const {
        control,
        setValue,
        getValues,
        formState: { isDirty },
    } = useFormContext();

    const [, setRefresh] = useState(false);

    const variantId = getValues(`${fieldName}.${componentIndex}.id`);
    const amount = useWatch({
        control,
        name: `${fieldName}.${componentIndex}.amount`,
    });
    const uom = useWatch({
        control,
        name: `${fieldName}.${componentIndex}.uom`,
    });
    const unitCount = useWatch({
        control,
        name: `${fieldName}.${componentIndex}.unitCount`,
    });
    const merchandiseSize = useWatch({
        control,
        name: `${fieldName}.${componentIndex}.merchandiseSize`,
    });
    const name = useWatch({
        control,
        name: `${fieldName}.${componentIndex}.name`,
    });
    const variantDetails = useWatch({
        control,
        name: `${fieldName}.${componentIndex}.details`,
    });
    const useCustomName = useWatch({
        control,
        name: `${fieldName}.${componentIndex}.details.useCustomName`,
    });

    const totalMgThc = useWatch({
        control,
        name: `${fieldName}.${componentIndex}.details.${COMPLIANCE_FIELDS_ENUM.TOTAL_MG_THC}`,
    });

    // ensure same as amount is setup
    const fieldPath = `${fieldName}.${componentIndex}.details.${SKU_RUN_TIME_FIELDS.SAME_AS_AMOUNT}`;
    if (getValues(fieldPath) === undefined) {
        setValue(
            fieldPath,
            Boolean(
                uom === uomTypes.MILLIGRAMS.toUpperCase() &&
                    sameAsAmountCategories.includes(product.productCategory?.name!) &&
                    isLooselyEqual(amount, totalMgThc),
            ),
            { shouldTouch: false, shouldDirty: false },
        );
    }

    const sameAsAmount = useWatch({
        control,
        name: `${fieldName}.${componentIndex}.details.${SKU_RUN_TIME_FIELDS.SAME_AS_AMOUNT}`,
    });

    const isGlobalContent = Boolean(variant.verifiedReferenceId);

    const isUomDisabled: boolean = !!(
        isBulkProduct() ||
        (isHeavilyManufacturedGood(productCategoryName) && uom && uom.toLowerCase() === uomTypes.MILLIGRAMS)
    );

    type VariantFormDetails = (typeof selectedCategory)['variantFormDetails'];

    const sortVariantFormDetails = (variantFormDetails: VariantFormDetails) => {
        const sortByCustom = (customOrder: string[]) =>
            sortBy(variantFormDetails, (item) => {
                const index = customOrder.indexOf(item.input);

                return index === -1 ? Infinity : index;
            });

        const isPackagedGoods = Object.values(PRODUCT_PACKAGED_GOODS).find((pckg) => pckg === selectedCategory.name);

        switch (selectedCategory.name) {
            case isPackagedGoods:
                return sortByCustom([
                    COMPLIANCE_FIELDS_ENUM.TOTAL_MG_THC,
                    COMPLIANCE_FIELDS_ENUM.THC_PER_DOSE,
                    COMPLIANCE_FIELDS_ENUM.TOTAL_MG_CBD,
                    COMPLIANCE_FIELDS_ENUM.CBD_PER_DOSE,
                    COMPLIANCE_FIELDS_ENUM.DOSES,
                    COMPLIANCE_FIELDS_ENUM.TOTAL_FLOWER_WEIGHT,
                ]);

            case PRODUCT_CATEGORIES.CBD:
                return sortByCustom([
                    COMPLIANCE_FIELDS_ENUM.TOTAL_MG_CBD,
                    COMPLIANCE_FIELDS_ENUM.CBD_PER_DOSE,
                    COMPLIANCE_FIELDS_ENUM.DOSES,
                ]);
            default:
                return sortByCustom([COMPLIANCE_FIELDS_ENUM.TOTAL_MG_THC, COMPLIANCE_FIELDS_ENUM.TOTAL_MG_CBD]);
        }
    };

    const overrideFormProps = (details: FormDetailsProps): FormDetailsProps => {
        if (details.input === 'netWeightUom') {
            return { ...details, inputType: InputType.SELECT_OPTIONS, options: NET_WEIGHT_UOM_OPTIONS };
        }
        return details;
    };

    const adjustChildVariantName = (variantName: string) => {
        const childVariants: SkuDto[] = getValues(`${fieldName}.${componentIndex}.children`);
        childVariants?.forEach((cv: SkuDto, index: number) => {
            if (cv.details?.isSample) {
                setValue(`${fieldName}.${componentIndex}.children.${index}.name`, `${variantName} - Sample`);
            }
            if (cv.details?.isPromo) {
                setValue(`${fieldName}.${componentIndex}.children.${index}.name`, `${variantName} - Promo`);
            }
        });
    };

    const adjustChildVariantSizeData = (
        newValue: string | number | boolean,
        propertyName: 'uom' | 'amount' | 'unitCount' | 'merchandiseSize',
    ) => {
        setRefresh((prev) => !prev);
        Array.from({ length: childrenCount })?.forEach((_, index) => {
            setValue(`${fieldName}.${componentIndex}.children.${index}.${propertyName}`, newValue);
        });
    };

    const adjustChildComplianceFields = (newValue: string | number | boolean, propertyName: keyof SkuDetailsDto) => {
        setRefresh((prev) => !prev);
        Array.from({ length: childrenCount })?.forEach((_, index) => {
            setValue(`${fieldName}.${componentIndex}.children.${index}.details.${propertyName}`, newValue);
        });
    };

    useEffect(() => {
        const {
            name: currentName,
            amount: currentAmount,
            uom: currentUom,
            unitCount: currentUnitCount,
            merchandiseSize: currentMerchSize,
        } = variant;

        if (name !== currentName) {
            adjustChildVariantName(name);
        }
        if (amount !== currentAmount) {
            adjustChildVariantSizeData(amount, 'amount');
        }
        if (uom !== currentUom) {
            adjustChildVariantSizeData(uom, 'uom');
        }
        if (unitCount !== currentUnitCount) {
            adjustChildVariantSizeData(unitCount, 'unitCount');
        }
        if (merchandiseSize !== currentMerchSize) {
            adjustChildVariantSizeData(merchandiseSize, 'merchandiseSize');
        }
    }, [name, amount, uom, unitCount, merchandiseSize]);

    useEffect(() => {
        const { details } = variant;
        complianceFields.forEach((key: keyof SkuDetailsDto) => {
            if (details && details[key] && details[key] !== variantDetails[key]) {
                adjustChildComplianceFields(variantDetails[key], key);
            }
        });
    }, [variantDetails]);

    const showNonBulkFields = !isBulkProductWithSingleSku();
    const netWeightLineBreakCategories = [PRODUCT_CATEGORIES.CBD, PRODUCT_CATEGORIES.Cartridge] as string[];

    const thcSameAsAmountToggle = (categoryName: string, input: string) =>
        sameAsAmountCategories.includes(categoryName) &&
        input === COMPLIANCE_FIELDS_ENUM.TOTAL_MG_THC &&
        uom === uomTypes.MILLIGRAMS.toUpperCase();

    useEffect(() => {
        const isAmountChanged = amount !== variant.amount;
        const isUoMChanged = uom !== variant.uom;
        const isUnitCountChanged = unitCount !== variant.unitCount;
        const isMerchandiseSizeChanged = merchandiseSize !== variant.merchandiseSize;

        const currentVariant = variants?.[componentIndex] as SkuDto;
        const formVariant = getValues(`variants.${componentIndex}`) as SkuDto;

        const genSkuName = (sku: SkuDto) => `${product.name} - ${getVariantSizeLabel(sku, productCategoryName)}`;

        /**
         * This is used whenever we press + Add SKU name
         * e.g. when variant.id is null
         */
        const newVariant = buildVariantDefaultData(
            product,
            defaultUom,
            defaultAmount,
            1,
            productCategoryName === PRODUCT_CATEGORIES.Merch ? MERCH_SIZE.OneSize : null,
        );

        /**
         * This is for when the component loads, use backend data, if
         * you're creating a new sku, we gen a name with default values
         * from above newVariant
         *
         */
        if (!useCustomName) {
            if (variant.id) {
                setValue(`${fieldName}.${componentIndex}.name`, genSkuName(currentVariant));
            } else {
                setValue(`${fieldName}.${componentIndex}.name`, genSkuName(newVariant));
            }
        } else if (useCustomName && variant.id) {
            setValue(`${fieldName}.${componentIndex}.name`, currentVariant.name);
        }

        /**
         * this is for when changing uom, total amount, unit count and
         * other values we use to computed the sku name
         */
        if (!useCustomName && (isAmountChanged || isUoMChanged || isUnitCountChanged || isMerchandiseSizeChanged)) {
            setValue(`${fieldName}.${componentIndex}.name`, genSkuName(formVariant));
        }
    }, [product, useCustomName, amount, uom, unitCount, merchandiseSize]);

    // This runs when the sameAsAmount toggle is on
    useEffect(() => {
        if (sameAsAmount) {
            setValue(`${fieldName}.${componentIndex}.details.${COMPLIANCE_FIELDS_ENUM.TOTAL_MG_THC}`, amount);
        }
    }, [amount, sameAsAmount, setValue]);

    // This runs before form interaction and dom paints
    useLayoutEffect(() => {
        if (!isDirty) {
            if (uom === uomTypes.MILLIGRAMS.toUpperCase() && isLooselyEqual(amount, totalMgThc)) {
                setValue(`${fieldName}.${componentIndex}.details.${SKU_RUN_TIME_FIELDS.SAME_AS_AMOUNT}`, true);
            }
        }
    }, [isDirty, amount, totalMgThc, uom, setValue]);

    // This runs when the uom, amount or totalMgThc is changed
    useEffect(() => {
        if (uom) {
            if (uom !== uomTypes.MILLIGRAMS.toUpperCase()) {
                setValue(`${fieldName}.${componentIndex}.details.${SKU_RUN_TIME_FIELDS.SAME_AS_AMOUNT}`, false);
            } else if (uom === uomTypes.MILLIGRAMS.toUpperCase() && isLooselyEqual(amount, totalMgThc)) {
                setValue(`${fieldName}.${componentIndex}.details.${SKU_RUN_TIME_FIELDS.SAME_AS_AMOUNT}`, true);
            }
        }
    }, [uom, amount, totalMgThc, setValue]);

    return (
        <>
            <VariantAccordion variant={variant} index={componentIndex} removeItem={removeItem}>
                <VariantDetailsWrapper>
                    <SectionContainer>
                        {variant.id && !isBulkProductWithSingleSku() && (
                            <VariantStatusBox display="flex" justifySelf="flex-end">
                                <HookFormVariantStatusToggle
                                    name={`${fieldName}.${componentIndex}.status`}
                                    variant={variant}
                                />
                            </VariantStatusBox>
                        )}
                    </SectionContainer>
                    <SectionContainer data-testid="variant-sku-name-container">
                        <Grid display="grid" gridTemplateColumns="repeat(2, 1fr)" alignItems="center">
                            <Tooltip
                                variant="multiRow"
                                title="
                            This name appears in SellTreez when creating invoices,
                            managing inventory, making sales in the POS, and on
                            Ecommerce, unless the eCommerce Menu Title is set. "
                            >
                                <Box display="flex" alignItems="center">
                                    <VariantDetailsHeader alignItems="center" data-testid="variant-sku-name-header">
                                        SKU NAME <Icon iconName="ErrorOutlined" />
                                    </VariantDetailsHeader>
                                </Box>
                            </Tooltip>
                            <Box display="flex" alignItems="center" justifySelf="flex-end">
                                <HookFormSwitch
                                    disabled={isGlobalContent}
                                    name={`${fieldName}.${componentIndex}.details.useCustomName`}
                                    label="Use custom name"
                                />
                                <HidemenuContainer>
                                    <Tooltip
                                        variant="multiRow"
                                        title="By default, the SKU Name is a combination of the
                                        Product Name and Total Amount/UoM. This can be overridden
                                        by using the Use custom name toggle."
                                    >
                                        <StyledIconContainer>
                                            <Icon iconName="ErrorOutlined" />
                                        </StyledIconContainer>
                                    </Tooltip>
                                </HidemenuContainer>
                            </Box>
                        </Grid>
                        <Grid item xs={12}>
                            <HookFormInput
                                name={`${fieldName}.${componentIndex}.name`}
                                label="Name"
                                disabled={!useCustomName}
                            />
                        </Grid>
                    </SectionContainer>
                    <SectionContainer data-testid="variant-details-container">
                        {product &&
                            product.productCategory &&
                            product.productCategory.name &&
                            productCategoryName !== PRODUCT_CATEGORIES.Plant &&
                            isProductCategory(productCategoryName) &&
                            showNonBulkFields && (
                                <>
                                    <VariantDetailsHeaderGrid data-testid="variant-details-header">
                                        SIZE DEFINITION
                                    </VariantDetailsHeaderGrid>
                                    <VariantSizeDefinitionSubheader
                                        productCategory={productCategoryName as PRODUCT_CATEGORIES}
                                    />
                                </>
                            )}
                        <Box>
                            <StyledComplianceFieldsContainer container rowSpacing={2} columnSpacing={1.5}>
                                {product &&
                                    product.productCategory &&
                                    productCategoryName !== PRODUCT_CATEGORIES.Merch &&
                                    productCategoryName !== PRODUCT_CATEGORIES.Plant && (
                                        <>
                                            <Grid item xs={6}>
                                                <HookFormInput
                                                    name={`${fieldName}.${componentIndex}.amount`}
                                                    label="Total Amount *"
                                                    disabled={!showNonBulkFields}
                                                />
                                            </Grid>

                                            <Grid item xs={6}>
                                                <HookFormSelect
                                                    disabled={isGlobalContent || isUomDisabled}
                                                    label="UoM *"
                                                    name={`${fieldName}.${componentIndex}.uom`}
                                                    // will be enabled for new variants and disabled for existing variants
                                                    // this is purposely done to trigger validation error if an existing variant has an invalid uom
                                                    autoSelectDefault={!variantId}
                                                    defaultPropKey="isDefault"
                                                    menuItems={
                                                        product.productCategory?.uoms?.map((u) => ({
                                                            displayName: u.label,
                                                            displayValue: u.value,
                                                            isDefault: u.isDefault,
                                                        })) || []
                                                    }
                                                />
                                            </Grid>
                                        </>
                                    )}
                                {productCategoryName === PRODUCT_CATEGORIES.Merch && (
                                    <Grid item xs={6}>
                                        <HookFormSelect
                                            name={`${fieldName}.${componentIndex}.merchandiseSize`}
                                            label="Merchandise Size *"
                                            autoSelectDefault
                                            menuItems={MERCH_SIZES}
                                        />
                                    </Grid>
                                )}
                                {showNonBulkFields && (
                                    <Grid item xs={6}>
                                        <HookFormInput
                                            name={`${fieldName}.${componentIndex}.unitCount`}
                                            label="Unit Count *"
                                        />
                                    </Grid>
                                )}
                                <Grid item xs={12}>
                                    <VariantDetailsHeader>SKU DETAILS</VariantDetailsHeader>
                                </Grid>
                                <Grid item xs={12}>
                                    <HookFormInput
                                        name={`${fieldName}.${componentIndex}.labelPrinter`}
                                        label="Label Printer"
                                    />
                                </Grid>
                                <SkuComponent fieldName={fieldName} componentIndex={componentIndex} />

                                {showNonBulkFields &&
                                    sortVariantFormDetails(selectedCategory?.variantFormDetails)?.map((form) => (
                                        <React.Fragment key={form.input}>
                                            {thcSameAsAmountToggle(selectedCategory.name, form.input) && (
                                                <Grid
                                                    item
                                                    xs={12}
                                                    display="flex"
                                                    alignItems="center"
                                                    marginBottom="-0.6em"
                                                >
                                                    <HookFormSwitch
                                                        name={`${fieldName}.${componentIndex}.details.${SKU_RUN_TIME_FIELDS.SAME_AS_AMOUNT}`}
                                                        label="Same as Total Amount"
                                                    />
                                                </Grid>
                                            )}
                                            {/** this creates a ghost grid to push net weight down */}
                                            {netWeightLineBreakCategories.includes(productCategoryName) &&
                                                form.input === COMPLIANCE_FIELDS_ENUM.NET_WEIGHT && (
                                                    <Grid item xs={6} />
                                                )}
                                            <Grid
                                                item
                                                xs={6}
                                                key={`${fieldName}.${componentIndex}.details.${form.input}-${variant.id}${variant.unitCount}${variant.merchandiseSize}`}
                                            >
                                                <HookFormDynamicField
                                                    disabled={
                                                        isGlobalContent ||
                                                        (form.input === COMPLIANCE_FIELDS_ENUM.TOTAL_MG_THC &&
                                                            sameAsAmount)
                                                    }
                                                    data={overrideFormProps(form)}
                                                    formatName={(propName: string) =>
                                                        `${fieldName}.${componentIndex}.details.${propName}`
                                                    }
                                                    {...(form.input === COMPLIANCE_FIELDS_ENUM.TOTAL_MG_THC && {
                                                        onBlur: () => {
                                                            if (uom !== uomTypes.MILLIGRAMS.toUpperCase()) {
                                                                return;
                                                            }

                                                            setValue(
                                                                `${fieldName}.${componentIndex}.details.${SKU_RUN_TIME_FIELDS.SAME_AS_AMOUNT}`,
                                                                isLooselyEqual(amount, totalMgThc),
                                                            );
                                                        },
                                                    })}
                                                />
                                            </Grid>
                                        </React.Fragment>
                                    ))}
                                <Grid item xs={12}>
                                    <VariantDetailsHeader>EXTERNAL ID</VariantDetailsHeader>
                                </Grid>
                                <VariantReferenceIds
                                    disabled={isGlobalContent}
                                    useRefFieldName={`${fieldName}.${componentIndex}.useReferenceId`}
                                    fieldName={`${fieldName}.${componentIndex}.referenceIds`}
                                />
                            </StyledComplianceFieldsContainer>
                        </Box>
                    </SectionContainer>
                    <SectionContainer data-testid="ecommerce-container">
                        <EcommerceBlock
                            display="grid"
                            gridTemplateColumns="repeat(2, 1fr)"
                            alignItems="center"
                            justifyContent="center"
                        >
                            <VariantDetailsHeader data-testid="ecommerce-header">
                                ECOMMERCE INFORMATION
                            </VariantDetailsHeader>
                            <Box display="flex" alignItems="center" justifySelf="flex-end">
                                <HookFormSwitch
                                    disabled={isGlobalContent}
                                    name={`${fieldName}.${componentIndex}.details.hideFromEcomMenu`}
                                    label="Hide from menu"
                                />
                                <HidemenuContainer>
                                    <Tooltip
                                        title="Turn this toggle on to hide the product variant from your eCommerce menu."
                                        variant="multiRow"
                                        testId="hidemenu-tooltip"
                                    >
                                        <StyledIconContainer>
                                            <Icon iconName="InfoOutlined" color="primaryBlack" />
                                        </StyledIconContainer>
                                    </Tooltip>
                                </HidemenuContainer>
                            </Box>
                        </EcommerceBlock>
                        <GlobalDescriptionOuterContainer>
                            <VariantField sx={{ width: '100%' }}>
                                <HookFormInput
                                    name={`${fieldName}.${componentIndex}.details.menuTitle`}
                                    label="Menu Title"
                                />
                            </VariantField>
                            {showNonBulkFields && (
                                <GlobalDescriptionContainer>
                                    <HookFormVariantDescriptionInput
                                        name={`${fieldName}.${componentIndex}.details.description`}
                                        label="Description"
                                    />
                                </GlobalDescriptionContainer>
                            )}
                        </GlobalDescriptionOuterContainer>
                    </SectionContainer>
                    {showNonBulkFields && (
                        <ImagesContainer
                            fieldName={`${fieldName}.${componentIndex}.images`}
                            objectId={variant.id ?? uuidv4()}
                            objectType={ObjectType.PRODUCT_VARIANT_IMAGE}
                        />
                    )}
                    {!isBulkProduct() && !(variant.status === Status.INACTIVE && !variant.children?.length) && (
                        <SectionContainer>
                            <VariantDetailsHeader data-testid="ecommerce-header">
                                SAMPLES AND PROMOS
                            </VariantDetailsHeader>
                            <CreateChildButton
                                disabled={isGlobalContent}
                                label="Create a Sample version of this SKU"
                                onClick={() => onCreateChild({ isSample: true })}
                                fieldName={`${fieldName}.${componentIndex}.children`}
                                childKey="isSample"
                            />
                            <CreateChildButton
                                disabled={isGlobalContent}
                                label="Create a Promo version of this SKU"
                                onClick={() => onCreateChild({ isPromo: true })}
                                fieldName={`${fieldName}.${componentIndex}.children`}
                                childKey="isPromo"
                            />
                        </SectionContainer>
                    )}
                </VariantDetailsWrapper>
            </VariantAccordion>
        </>
    );
};

export default VariantForm;
