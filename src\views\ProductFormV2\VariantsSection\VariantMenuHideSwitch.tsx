import React from 'react';
import { Box, styled } from '@mui/material';
import { Icon, Tooltip } from '@treez-inc/component-library';
import convertPxToRem from '@treez-inc/component-library/dist/util-functions/convertPxToRem';
import HookFormSwitch from '../../../components/hook-form-v2/HookFormSwitch';

export const HidemenuContainer = styled(Box)({
    display: 'flex',
    padding: `${convertPxToRem(5)} 0`,
    textAlign: 'left',
    alignItems: 'center',
    flexDirection: 'row',
});

export const StyledIconContainer = styled(Box)(() => ({
    display: 'flex',
}));

interface VariantHideMenuProps {
    disabled?: boolean;
    name: string;
    label: string;
    toolTipText: string;
}

const VariantMenuHideSwitch = ({ disabled = false, toolTipText, name, label }: VariantHideMenuProps) => (
    <HidemenuContainer>
        <HookFormSwitch disabled={disabled} name={name} label={label} />
        <Tooltip title={toolTipText} variant="multiRow" testId="tooltip">
            <StyledIconContainer>
                <Icon iconName="InfoOutlined" color="primaryBlack" />
            </StyledIconContainer>
        </Tooltip>
    </HidemenuContainer>
);

export default VariantMenuHideSwitch;
