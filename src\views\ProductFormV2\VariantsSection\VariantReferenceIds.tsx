import React from 'react';
import { Button } from '@treez-inc/component-library';
import { useFieldArray, useFormContext } from 'react-hook-form';
import { Grid } from '@mui/material';
import ReferenceIdComponent from './ReferenceIdComponent';
import HookFormCheckbox from '../../../components/hook-form-v2/HookFormCheckbox';

export interface ******************************** {
    disabled?: boolean;
    fieldName: string;
    useRefFieldName: string;
}

export default function VariantReferenceIds({
    disabled = false,
    fieldName,
    useRefFieldName,
}: ********************************) {
    const { watch } = useFormContext();
    const { control } = useFormContext();
    const showReferenceIds = watch(useRefFieldName);
    const { fields, remove, append } = useFieldArray({
        control,
        name: fieldName,
        keyName: 'refId',
    });

    return (
        <>
            <Grid item xs={12}>
                <HookFormCheckbox disabled={disabled} name={useRefFieldName} label="Use External ID" />
            </Grid>
            {showReferenceIds && (
                <>
                    {fields.length > 0 &&
                        fields.map((field, index) => (
                            <ReferenceIdComponent
                                disabled={disabled}
                                fieldName={fieldName}
                                index={index}
                                key={field.refId}
                            />
                        ))}
                    {fields.length === 0 && (
                        <ReferenceIdComponent disabled={disabled} fieldName={fieldName} index={0} />
                    )}
                    <Grid item xs={6}>
                        <Button
                            disabled={disabled}
                            label="ADD EXTERNAL ID"
                            iconName="Add"
                            onClick={() => append({ sourceName: '', sourceId: '' })}
                            variant="text"
                        />
                    </Grid>
                    {fields.length > 1 && (
                        <Grid item xs={6}>
                            <Button
                                disabled={disabled}
                                label="REMOVE EXTERNAL ID"
                                iconName="Delete"
                                onClick={() => remove(fields.length - 1)}
                                variant="text"
                            />
                        </Grid>
                    )}
                </>
            )}
        </>
    );
}
