import React from 'react';
import { styled, Typography } from '@mui/material';
import { convertPxToRem } from '@treez-inc/component-library';
import PRODUCT_CATEGORIES from '../../../utils/product-categories-data';

const VariantDetailsSubheaderTypography = styled(Typography)(({ theme }) => ({
    color: theme.palette.secondaryText.main,
    fontSize: convertPxToRem(14),
    fontWeight: 400,
    lineHeight: convertPxToRem(20),
}));

const getVariantSizeDescriptionText = (category: PRODUCT_CATEGORIES) => {
    switch (category) {
        case PRODUCT_CATEGORIES.Beverage:
        case PRODUCT_CATEGORIES.Edible:
        case PRODUCT_CATEGORIES.Tincture:
        case PRODUCT_CATEGORIES.Topical:
        case PRODUCT_CATEGORIES.Pill:
            return `${category} size is the total milligrams (mgs) of THC. You can also specify the number of units included, with the default being 1. If there are multiple units, the total mg THC should be the cumulative amount of THC across all units.`;
        case PRODUCT_CATEGORIES.CBD:
            return `${category} size is the total milligrams (mgs) of CBD. You can also specify the number of units included, with the default being 1. If there are multiple units, the total mg CBD should be the cumulative amount of CBD across all units.`;
        case PRODUCT_CATEGORIES.Merch:
        case PRODUCT_CATEGORIES.Misc:
        case PRODUCT_CATEGORIES.NonInv:
            return `${category} size is typically unit based, however, you may choose to define a total amount and unit of measure if it is more suitable for your needs.`;
        case PRODUCT_CATEGORIES.Extract:
        case PRODUCT_CATEGORIES.Flower:
            return `${category} size is the total weight of the combustible good.`;
        case PRODUCT_CATEGORIES.Cartridge:
        case PRODUCT_CATEGORIES.Preroll:
            return `${category} size is the total weight of the combustible good. You can also specify the number of units that are included.`;
        default:
            return '';
    }
};

const VariantSizeDefinitionSubheader = ({ productCategory }: { productCategory: PRODUCT_CATEGORIES }) => (
    <VariantDetailsSubheaderTypography data-testid="variant-size-definition-subheader">
        {getVariantSizeDescriptionText(productCategory)}
    </VariantDetailsSubheaderTypography>
);

export default VariantSizeDefinitionSubheader;
