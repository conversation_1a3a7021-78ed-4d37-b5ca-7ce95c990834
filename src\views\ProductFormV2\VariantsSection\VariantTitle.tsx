import React from 'react';
import { Box, styled } from '@mui/material/';
import { VariantPromoSampleBadge, VariantPromoSampleTypo } from '../../../styles/globalStyles';
import { SkuDto } from '../../../interfaces/dto/sku';
import { getVariantSizeLabel } from '../../../utils/variantCardUtils';

interface VariantTitleProps {
    variant: SkuDto;
    productCategoryName?: string;
}

const VariantTitleBox = styled(Box)(() => ({
    columnGap: '0.625em',
    display: 'flex',
    flexDirection: 'row',
}));

const VariantTitle = ({ variant, productCategoryName }: VariantTitleProps) => {
    // eslint-disable-next-line no-nested-ternary
    const sampleOrPromoText = variant?.details?.isPromo ? 'Promo' : variant?.details?.isSample ? 'Sample' : undefined;
    return (
        <VariantTitleBox>
            {productCategoryName && <Box>{getVariantSizeLabel(variant, productCategoryName)}</Box>}
            {sampleOrPromoText && (
                <VariantPromoSampleBadge sx={{ marginBottom: 0 }}>
                    <VariantPromoSampleTypo>{sampleOrPromoText}</VariantPromoSampleTypo>
                </VariantPromoSampleBadge>
            )}
        </VariantTitleBox>
    );
};

export default VariantTitle;
