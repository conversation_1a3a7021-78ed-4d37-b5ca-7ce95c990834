// validation schema
import <PERSON><PERSON> from 'joi';
import PRODUCT_CATEGORIES from '../../../utils/product-categories-data';
import { isHeavilyManufacturedGood, uomTypes } from '../../../utils/constants';
import { InfusedSubCategories } from '../../../interfaces/dto/productSubCategory';

const optionalNumberValidation = Joi.number().optional().allow(null, '', 0).positive().messages({
    'number.base': 'Must be a number',
    'number.positive': 'Must be a positive number',
});

const preRollFlowerWeightValidation = Joi.number().required().allow(0).empty('').positive().messages({
    'number.base': 'Must be a number',
    'number.positive': 'Must be a positive number',
    'any.required': 'This field is required when the product subcategory is infused.',
});

const isInfusedProduct = (productCategoryName: string, productSubCategoryName: string) =>
    (productCategoryName === PRODUCT_CATEGORIES.Flower &&
        productSubCategoryName === InfusedSubCategories.INFUSED_FLOWER) ||
    (productCategoryName === PRODUCT_CATEGORIES.Preroll &&
        ([InfusedSubCategories.INFUSED, InfusedSubCategories.INFUSED_BLUNT] as string[]).includes(
            productSubCategoryName,
        ));

const complianceFieldsValidation = (
    productCategoryName: string,
    isBaseSku: boolean,
    productSubCategoryName: string = '',
) => {
    const isInfused = isInfusedProduct(productCategoryName, productSubCategoryName);

    const totalFlowerConcentrateWeightValidation =
        isInfused && isBaseSku ? preRollFlowerWeightValidation : optionalNumberValidation;

    return {
        doses: optionalNumberValidation,
        cbdPerDose: optionalNumberValidation,
        thcPerDose: optionalNumberValidation,
        grossWeight: optionalNumberValidation,
        netWeight: optionalNumberValidation,
        netWeightUom: Joi.string().optional().allow('', null),
        totalMgCbd: optionalNumberValidation,
        totalMgThc: optionalNumberValidation,
        totalFlowerWeight: totalFlowerConcentrateWeightValidation,
        totalConcentrateWeight: totalFlowerConcentrateWeightValidation,
        usableMarijuanaWeight: optionalNumberValidation,
        extractionMethod: Joi.string().optional().allow('', null),
    };
};

const skuObjectSchema = (
    productCategoryName: PRODUCT_CATEGORIES,
    isBaseSku: boolean,
    productSubCategoryName: string = '',
) => ({
    id: Joi.string().optional(),
    sku: Joi.string().optional().allow(null, ''),
    images: Joi.array(),
    additionalSku: Joi.array()
        .items(Joi.string().optional().allow(null, ''))
        .unique()
        .message('SKU should be unique')
        .optional(),
    useReferenceId: Joi.boolean().optional(),
    status: Joi.string().optional(),
    referenceIds: Joi.when('useReferenceId', {
        is: true,
        then: Joi.array().items(
            Joi.object({
                sourceId: Joi.string().required().empty(['', null]).messages({
                    'any.required': 'External Id is required.',
                    'string.empty': 'External Id cannot be empty.',
                    'any.empty': 'External Id is required.',
                }),
                sourceName: Joi.string().required().empty(['', null]).messages({
                    'any.required': 'Source Name is required.',
                    'string.empty': 'Source Name cannot be empty.',
                    'any.empty': 'Source Name is required.',
                }),
            }),
        ),
    }),
    details: {
        description: Joi.string().optional().allow('', null),
        menuTitle: Joi.string().optional().allow('', null),
        ...complianceFieldsValidation(productCategoryName, isBaseSku, productSubCategoryName),
        isSample: Joi.boolean().optional(),
        isPromo: Joi.boolean().optional(),
        hideFromEcomMenu: Joi.boolean().optional(),
        useCustomName: Joi.boolean().optional().default(false),
        sameAsAmount: Joi.boolean().default(false),
    },
    uom:
        productCategoryName !== PRODUCT_CATEGORIES.Merch && productCategoryName !== PRODUCT_CATEGORIES.Plant
            ? Joi.string()
                  .required()
                  .empty(['', null])
                  .custom((value, helpers) => {
                      if (isHeavilyManufacturedGood(productCategoryName)) {
                          if (value.toLowerCase() !== uomTypes.MILLIGRAMS) {
                              return helpers.error('any.only', { value });
                          }
                      }
                      return value;
                  })
                  .messages({
                      'any.required': 'UoM is required.',
                      'any.only': `UoM must be ${uomTypes.MILLIGRAMS} for this category.`,
                      'string.empty': 'UoM cannot be empty.',
                      'any.empty': 'UoM is required.',
                  })
            : Joi.optional().allow('', null),
    amount:
        productCategoryName !== PRODUCT_CATEGORIES.Merch && productCategoryName !== PRODUCT_CATEGORIES.Plant
            ? Joi.number().required().positive().empty(['', null]).messages({
                  'any.required': 'Amount is required.',
                  'any.empty': 'Amount is required.',
                  'number.base': 'Must be a number',
                  'number.positive': 'Must be a positive number',
              })
            : Joi.optional().allow('', null),
    unitCount: Joi.number().required().positive().messages({
        'any.required': 'Unit Count is required.',
        'string.empty': 'Unit Count cannot be empty.',
        'number.base': 'Must be a number',
        'number.positive': 'Must be a positive number',
    }),
    merchandiseSize:
        productCategoryName === PRODUCT_CATEGORIES.Merch
            ? Joi.string().required().empty(['', null]).messages({
                  'any.required': 'Merchandise Size is required.',
                  'string.empty': 'Merchandise Size cannot be empty.',
              })
            : Joi.optional().allow('', null),
    name: Joi.required().empty(['', null]).messages({
        'any.required': "Name is required if you're using a custom name",
        'string.empty': "Name cannot be empty if you're using a custom name",
    }),
    labelPrinter: Joi.string().allow('', null),
});

const VariantsFormSchema = (productCategoryName: PRODUCT_CATEGORIES, productSubCategory: string = '') =>
    Joi.object({
        variants: Joi.array()
            .items(
                Joi.object({
                    ...skuObjectSchema(productCategoryName, true, productSubCategory),
                    children: Joi.array()
                        .items({
                            ...skuObjectSchema(productCategoryName, false, productSubCategory),
                            defaultPrices: Joi.object({
                                base: Joi.number().positive().required(),
                            }).required(),
                        })
                        .optional(),
                }),
            )
            .required(),
    });

export default VariantsFormSchema;
