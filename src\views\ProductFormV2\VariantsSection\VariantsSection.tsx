import React, { useEffect, useState } from 'react';
import { styled } from '@mui/material/';
import { useFieldArray, useFormContext, useWatch } from 'react-hook-form';
import { Button, Tab, Tabs, convertPxToRem } from '@treez-inc/component-library';
import Typography from '@mui/material/Typography';
import { omit } from 'lodash';
import useReactHookForm from '../../../hooks/useReactHookForm';
import useProduct from '../Hooks/useProduct';
import ReactHookProductTabForm from '../ReactHookProductTabForm';
import { SubmitCallBack } from '../Types/SubmitFormTypes';
import useSaveData from '../../../hooks/useSaveData';
import { cleanData, filterUpdates, isEmpty } from '../../../utils/common';
import filterMutationErrors, { MutationErrorData } from '../../../utils/MutationResponseUtil';
import VariantsFormSchema from './VariantsForm.schema';
// eslint-disable-next-line import/no-cycle
import VariantDetailsForm from './VariantDetailsForm';
import { MERCH_SIZES, MERCH_SIZE } from '../../../utils/merchSizes-data';
import TabPanel from '../../../components/TabPanel';
import PRODUCT_CATEGORIES, {
    COMPLIANCE_FIELDS_ENUM,
    SKU_RUN_TIME_FIELDS,
} from '../../../utils/product-categories-data';
import useSaveDataArray, { MutationResponse } from '../../../hooks/useSaveDataArray';
import imageApiKeyStore from '../../../api/imageApiKeyStore';
import { ImageDetailsDto } from '../../../interfaces/dto/imageDetails';
import { hasImageChanges, mergeArraysById } from '../Images/ImageUtils';
import Mode from '../Types/Mode';
import { getVariantName, getVariantSizeLabel, transformSkuSectionData } from '../../../utils/variantCardUtils';
import useSnackbarContext from '../../../hooks/snackbar/useSnackbarContext';
import { ProductCategoryDto, UomProps } from '../../../interfaces/dto/productCategory';
import useVariantErrorIndicator from './VariantErrorIndicator';
import { isHeavilyManufacturedGood, uomTypes } from '../../../utils/constants';
import { SkuDto, SkuApiDto } from '../../../interfaces/dto/sku';
import skuApiKeyStore from '../../../api/skuApiKeyStore';
import { StyledSubHeader } from '../../../styles/globalStyles';
import { DetailsDto } from '../../../interfaces/dto/details';
import { sameAsAmountCategories } from './Variant.constants';

interface ProductFormData {
    variants?: SkuDto[];
}

const StyledTabs = styled(Tabs)(() => ({
    paddingTop: convertPxToRem(10),
    '& .MuiTabs-flexContainer': {
        alignItems: 'center',
    },
}));

export const getDefaultUom = (productCategoryName: string, bulkProductWithSingleSku: boolean): string | null => {
    if (isHeavilyManufacturedGood(productCategoryName)) {
        return uomTypes.MILLIGRAMS.toUpperCase();
    }
    if (bulkProductWithSingleSku) {
        return uomTypes.GRAMS.toUpperCase();
    }
    if (productCategoryName === PRODUCT_CATEGORIES.Cartridge || productCategoryName === PRODUCT_CATEGORIES.Extract) {
        return uomTypes.GRAMS.toUpperCase();
    }
    return null;
};

const VariantTab = (props: any) => {
    const { product, categoryName, fieldName, variant, index, bulkProductWithSingleSku, ...rest } = props;
    const { control } = useFormContext();
    const productSubCategoryName: string = product.productSubCategory?.name || '';
    const { hasError } = useVariantErrorIndicator({ arrayName: fieldName, index });

    const watchedVariantProperties = useWatch({
        control,
        name: `variants.${index}`,
    });

    const hasSample = variant.children?.some((v: SkuDto) => v.details?.isSample);
    const hasPromo = variant.children?.some((v: SkuDto) => v.details?.isPromo);

    let childInfo = hasSample ? ', Sample' : '';
    childInfo = hasPromo ? `${childInfo}, Promo` : childInfo;

    const getTitle = (productVariant?: SkuDto) => {
        if (bulkProductWithSingleSku) {
            return productSubCategoryName;
        }
        if (productVariant) {
            return `${getVariantSizeLabel(productVariant, categoryName)}${childInfo}`;
        }
        return undefined;
    };

    const iconName = hasError() ? 'Warning' : '';

    return <Tab iconName={iconName} label={getTitle(watchedVariantProperties) ?? 'New'} {...rest} />;
};

const prepareFormVariantChildren = (variants: SkuDto[], categoryName: string) => {
    const parents: SkuDto[] = variants
        .filter(
            (v) =>
                (isEmpty(v.parentId) && !v.details?.isSample && !v.details?.isPromo) ||
                (!isEmpty(v.parentId) && !variants.some((p) => p.id === v.parentId)),
        )
        .map((p) => ({
            ...p,
            images: p?.images ? [...p.images].sort((a: any, b: any) => a.order - b.order) : [],
            details: {
                ...p.details,
                useCustomName: p.details?.useCustomName ?? false,
            },
            children: [],
        }));

    variants?.forEach((variant: SkuDto) => {
        if (variant.details?.isSample || variant.details?.isPromo) {
            const generatedLabel = getVariantSizeLabel(variant, categoryName).toLowerCase();
            const parent = parents.find(
                (p) =>
                    p.id === variant.parentId ||
                    `${getVariantSizeLabel(p, categoryName)} - sample`.toLowerCase() === generatedLabel ||
                    `${getVariantSizeLabel(p, categoryName)} - promo`.toLowerCase() === generatedLabel,
            );
            if (parent) {
                parent.children?.push(variant);
            }
        }
    });
    return parents;
};

const convertVariantSizes = (variantSz: SkuDto): { variantSizes: SkuDto } => {
    if (variantSz.merchandiseSize) {
        const merchSize = MERCH_SIZES.find(
            (m) => m.displayValue.toString().toLowerCase() === variantSz.merchandiseSize?.toLowerCase(),
        );
        return {
            variantSizes: {
                ...variantSz,
                merchandiseSize: merchSize?.displayValue.toString() || null,
            },
        };
    }
    return { variantSizes: variantSz };
};

const getVariantFormValues = (updatedVariants: any, productCategory: ProductCategoryDto | undefined) => ({
    variants: updatedVariants?.map((v: any) => {
        const isUomValid = productCategory?.uoms?.some(
            (u: UomProps) => u.value?.toLowerCase() === v.uom?.toLowerCase(),
        );
        return {
            ...v,
            uom: v.uom && isUomValid ? v.uom.toUpperCase() : '', // To trigger validation error for uom in the form in case an invalid value exists
            additionalSku: v.additionalSku ? v.additionalSku : [],
            details: v.details,
            useReferenceId:
                v.referenceIds &&
                v.referenceIds.length > 0 &&
                v.referenceIds.some((ref: any) => Object.values(ref).some((value) => value)),
            ...convertVariantSizes(v),
            children: v.children?.map((c: any) => ({
                ...c,
                useReferenceId: c.referenceIds && c.referenceIds.length > 0,
                ...convertVariantSizes(v),
            })),
        };
    }),
});

const checkForDuplicateVariants = (variants: SkuDto[] | undefined, productCategoryName: string): boolean => {
    if (!variants) return false;

    const variantLabels: string[] = variants.map((variant) => getVariantSizeLabel(variant, productCategoryName));
    const uniqueLabels: Set<string> = new Set(variantLabels);

    return uniqueLabels.size !== variantLabels.length;
};

export const buildVariantDefaultData = (
    product: any,
    uom: string | null,
    amount: number | null,
    unitCount: number,
    merchandiseSize: string | null,
) => {
    const formDefaultValue: any = {
        id: undefined,
        sku: '',
        additionalSku: [],
        useReferenceId: false,
        labelPrinter: '',
        referenceIds: [{ sourceName: '', sourceId: '' }],
        images: !isEmpty(product.images)
            ? product.images.map((i: ImageDetailsDto, index: number) => ({
                  imageId: i.imageId,
                  ...(i.imageUrl && { imageUrl: i.imageUrl }),
                  ...(i.name && { name: i.name }),
                  ...(i.description && { description: i.description }),
                  organizationId: i.organizationId,
                  order: index + 1,
              }))
            : [],
        details: {
            ...product.productCategory?.variantFormDetails?.reduce(
                (acc: any, current: any) => ({ ...acc, [current.input]: '' }),
                {},
            ),
            menuTitle: '',
            description: '',
            hideFromEcomMenu: false,
            useCustomName: false,
            // this assumes that UoM by default will be milligrams already
            sameAsAmount: sameAsAmountCategories.includes(product.productCategory?.name!),
            isSample: false,
            isPromo: false,
        },
        uom,
        amount,
        unitCount,
        merchandiseSize,
    };

    return formDefaultValue;
};

const VariantsSection = () => {
    const { product, setProductUpdate, mode, isBulkProduct, isBulkProductWithSingleSku, isBulkProductWithMultipleSku } =
        useProduct();

    const { productCategory } = product;
    const productCategoryName: string = productCategory?.name || '';

    const [activeTab, setActiveTab] = useState(0);
    const { setSnackbar } = useSnackbarContext();
    const defaultUom: string | null = getDefaultUom(productCategoryName, isBulkProductWithSingleSku());
    const defaultAmount: number | null = isBulkProductWithSingleSku() ? 1 : null;

    const defaultVariantValues =
        product.variants && !isEmpty(product.variants)
            ? getVariantFormValues(
                  prepareFormVariantChildren([...product.variants], productCategoryName),
                  productCategory,
              ).variants
            : [
                  buildVariantDefaultData(
                      product,
                      null,
                      null,
                      1,
                      product.productCategory?.name === PRODUCT_CATEGORIES.Merch ? MERCH_SIZE.OneSize : null,
                  ),
              ];

    const form = useReactHookForm<ProductFormData>({
        ...(product.productCategory && {
            joiSchema: VariantsFormSchema(
                product.productCategory?.name as PRODUCT_CATEGORIES,
                product.productSubCategory?.name!,
            ),
        }),
        values: {
            variants: defaultVariantValues,
        },
    });

    const { fields, remove, append } = useFieldArray({
        control: form.control,
        name: 'variants',
        keyName: 'fieldKeyId',
    });
    const { isLoading: isSaving, mutateAsync } = useSaveData<SkuApiDto[]>({
        mutationConfig: skuApiKeyStore.saveSkus(),
    });
    const { isLoading: isSavingImages, mutateAsync: mutateImageAsync } = useSaveDataArray<ImageDetailsDto[]>({
        mutationConfig: imageApiKeyStore.saveImageDetails(),
    });

    useEffect(() => {
        if (mode === Mode.CREATE && isEmpty(product.variants)) {
            form.setValue('variants.0.amount', defaultAmount, { shouldDirty: true });
            form.setValue('variants.0.uom', defaultUom, { shouldDirty: true });
        }

        if (mode === Mode.EDIT && !product?.variants?.length) {
            form.setValue('variants.0.amount', defaultAmount, { shouldDirty: true });
            form.setValue('variants.0.uom', defaultUom, { shouldDirty: true });
        }
    }, [product, defaultUom, defaultAmount]);

    const handleRemoveVariant = (index: number) => {
        remove(index);
        setActiveTab(0);
    };

    const mergeToProductVariants = (newVariants?: SkuDto[], updatedVariants?: SkuDto[]): SkuDto[] => {
        let latestVariants: any[] = product.variants ? [...product.variants] : [];

        // Adding all newly variants
        if (newVariants) {
            latestVariants = [...latestVariants, ...newVariants];
        }

        // Updating updated variants
        if (updatedVariants) {
            updatedVariants.forEach((v: SkuDto) => {
                const index: number = latestVariants.findIndex((lv) => lv.id === v.id);
                // updated variant
                if (index !== -1) {
                    const currentEntry: SkuDto = latestVariants[index];
                    const updatedEntry: SkuDto = {
                        ...currentEntry,
                        ...v,
                    };
                    latestVariants[index] = updatedEntry;
                } else {
                    // sample and promo can be sometimes created through the update api and will be returned as part of its response
                    // so we need to add them to latestVariants
                    latestVariants.push(v);
                }
            });
        }

        return latestVariants;
    };

    const getUniqueVariantId = (v: SkuDto) => v.id ?? getVariantSizeLabel(v, productCategoryName);

    const buildMutationData = (values: ProductFormData) => {
        const detailsMap = new Map(values.variants?.map((v) => [getUniqueVariantId(v), v.details]) || []);

        const skusToCreate: SkuApiDto[] = [];
        const skusToUpdate: Partial<SkuApiDto>[] = [];

        // prepare children for update
        const variantProcessed = values.variants?.reduce((result: SkuDto[], v: SkuDto) => {
            const { id, children, details } = v;
            const variantName = v.name && v.name.trim() !== '' ? getVariantName(product, v) : '';

            if (id && children) {
                return [
                    ...result,
                    { ...v, children: undefined, name: variantName, details },
                    ...children.map((c) => ({ ...c, parentId: id })),
                ];
            }
            const res = [...result, { ...v, name: variantName, details }];

            return res;
        }, []);

        // New variants with children
        const addedSkus: SkuDto[] | undefined = variantProcessed
            ?.filter((v) => !v.id)
            .map((v: any) => ({
                ...v,
                name: getVariantName(product, v),
                productId: product.id,
                referenceIds: v.useReferenceId ? v.referenceIds : [],
                useReferenceId: undefined,
                children: v.children?.map((c: any) => {
                    const { defaultPrices, ...rest } = c;
                    return {
                        ...rest,
                        referenceIds: c.useReferenceId ? c.referenceIds : [],
                        ...(!isEmpty(defaultPrices) && {
                            defaultPrices: {
                                ...defaultPrices,
                                base: defaultPrices.base,
                            },
                        }),
                        useReferenceId: undefined,
                    };
                }),
            }));

        const updatedSkus = variantProcessed
            ?.filter((v) => v.id)
            .map((v: any) => {
                const { useReferenceId, uom, defaultPrices, details, ...rest } = v;
                return {
                    ...rest,
                    name: getVariantName(product, v),
                    referenceIds: v.useReferenceId ? v.referenceIds : [],
                    uom: uom ? uom.toLowerCase() : null,
                    details,
                    // allow removal of sku
                    sku: v.sku === '' ? null : v.sku,
                    ...(!isEmpty(defaultPrices) && {
                        defaultPrices: {
                            ...defaultPrices,
                            base: defaultPrices.base,
                        },
                    }),
                };
            });

        // remove unwanted fields before comparison
        const skusData = product.variants?.map((s: any) => {
            const {
                createdAt,
                updatedAt,
                deletedAt,
                children,
                label,
                liquidVolumeAmount,
                liquidVolumeUom,
                organizationId,
                productId,
                targetMergedVariantId,
                variantImages,
                verifiedReferenceId,
                variantProperties,
                ...rest
            } = s;

            return rest;
        });

        const cleanedUpAddedSkus = cleanData(addedSkus)?.map((v: SkuDto) => ({
            ...v,
            productId: product.id,
            children: v.children?.map((c) => ({
                ...c,
                productId: product.id,
                name: getVariantName(product, v),
            })),
        }));

        const transformedAddedSkus = transformSkuSectionData(cleanedUpAddedSkus, productCategoryName);
        skusToCreate.push(...transformedAddedSkus.skusToCreate);
        skusToUpdate.push(...transformedAddedSkus.skusToUpdate);

        const cleanedUpCurrentSkus = cleanData(skusData ? [...skusData] : []);
        const cleanedUpUpdatedSkus = cleanData(updatedSkus);
        const transformedCurrentSkus = transformSkuSectionData(cleanedUpCurrentSkus, productCategoryName);
        const transformedUpdatedSkus = transformSkuSectionData(cleanedUpUpdatedSkus, productCategoryName);

        const skusWithUpdatedData: Partial<SkuApiDto>[] | undefined = filterUpdates(
            transformedCurrentSkus.skusToUpdate,
            transformedUpdatedSkus.skusToUpdate,
        );
        if (skusWithUpdatedData && skusWithUpdatedData.length) {
            skusWithUpdatedData.forEach((s: Partial<SkuApiDto>) => {
                // check if an entry for the same sku already exists
                const index = skusToUpdate.findIndex((su: Partial<SkuApiDto>) => su.id === s.id);
                if (index !== -1) {
                    skusToUpdate[index] = {
                        ...skusToUpdate[index],
                        ...s,
                    };
                } else {
                    skusToUpdate.push(s);
                }
            });
        }

        const filterSkuData = (skus: Partial<SkuApiDto>[]) =>
            skus.map((sku) => {
                const { id, details, name, ...rest } = sku;
                const omitRunTimeProperties = (d: DetailsDto) => omit(d, SKU_RUN_TIME_FIELDS.SAME_AS_AMOUNT);

                const updatedDetails = omitRunTimeProperties({
                    ...details,
                });

                const addNameIfCustomNameTrue = detailsMap.get(getUniqueVariantId(sku as SkuDto))?.useCustomName
                    ? { name }
                    : {};

                if (isBulkProduct()) {
                    return {
                        ...rest,
                        id,
                        details: omit(updatedDetails, Object.values(COMPLIANCE_FIELDS_ENUM)),
                        ...addNameIfCustomNameTrue,
                    };
                }

                return {
                    ...rest,
                    id,
                    details: updatedDetails,
                    ...addNameIfCustomNameTrue,
                };
            });

        return {
            ...(!isEmpty(skusToCreate) && {
                createData: filterSkuData(skusToCreate),
            }),
            ...(!isEmpty(skusToUpdate) && {
                updateData: filterSkuData(skusToUpdate),
            }),
        };
    };

    const getTitle = (variant?: SkuDto) => {
        if (variant) {
            return `${getVariantSizeLabel(variant, productCategoryName)}`;
        }
        return undefined;
    };

    const getVariantTitle = (entity: SkuDto) => {
        if (entity?.id) {
            const variant = product.variants?.find((v) => v.id === entity.id);
            return getTitle(variant);
        }
        return getTitle(entity);
    };

    const updateVariantId = (value: SkuDto, latestVariants: SkuDto[]) => {
        if (isEmpty(value.id)) {
            const variantsFiltered = latestVariants.filter(
                (lv) =>
                    getVariantSizeLabel(lv, productCategoryName).toLowerCase() ===
                    getVariantSizeLabel(value, productCategoryName).toLowerCase(),
            );
            return { ...value, id: variantsFiltered.length === 1 ? variantsFiltered[0].id : undefined };
        }

        return value;
    };

    const updateFormVariantIds = (latestVariants: SkuDto[], values: ProductFormData): SkuDto[] => {
        const idsTaken: string[] = [];
        const updatedVariants: any[] | undefined = values.variants?.map((variant) => {
            const updatedVariant = updateVariantId(
                variant,
                latestVariants.filter((v) => !idsTaken.some((i) => i === v.id)),
            );
            if (updatedVariant.id) {
                idsTaken.push(updatedVariant.id);
            }
            return {
                ...updatedVariant,
                children: updatedVariant.children?.map((child) => updateVariantId(child, latestVariants)),
            };
        });
        if (updatedVariants) {
            return updatedVariants?.reduce((acc: SkuDto[], current: SkuDto) => {
                if (current.children) {
                    return [...acc, current, ...current.children];
                }
                return [...acc, current];
            }, []);
        }
        return [];
    };

    const getImage = (variantId?: string, imageId?: string) => {
        const variant = product.variants?.find((v) => v.id === variantId);
        return variant?.images?.find((i) => i.id === imageId);
    };

    const getImageMutationData = (variant: SkuDto) => {
        const createData = variant.images
            ?.filter((i) => !i.id && !i.isDeleted)
            .map((img) => ({ ...img, variantId: variant.id }));
        const updateData = variant.images
            ?.filter((img) => img.id && !img.isDeleted && hasImageChanges(img, getImage(variant.id, img.id)))
            .map((img) => ({
                ...img,
                description: img.description ? img.description : undefined,
                variantId: variant.id,
                productId: img.productId ? img.productId : undefined,
                name: img.name ? img.name : undefined,
            }));

        const deletedImages = variant.images?.filter((img) => img.isDeleted);

        return {
            createData: !isEmpty(createData) ? createData : undefined,
            updateData: !isEmpty(updateData) ? updateData : undefined,
            deleteData: !isEmpty(deletedImages) && deletedImages ? { ids: deletedImages.map((i) => i.id) } : undefined,
        };
    };

    const submitImages = async (formData: SkuDto[]) => {
        const mutationData = formData
            .filter((v) => !isEmpty(v.id) && v.images && v.images.length > 0)
            .map((v) => getImageMutationData(v))
            .filter((m) => m.createData || m.updateData || m.deleteData);

        if (!isEmpty(mutationData)) {
            return mutateImageAsync(mutationData);
        }
        return Promise.resolve(undefined);
    };

    const getCompleteImage = (
        currentImages: ImageDetailsDto[],
        mutationResult: MutationResponse<ImageDetailsDto[]>,
    ) => {
        const images: ImageDetailsDto[] = [];
        if (mutationResult.createData?.data) {
            images.push(...mutationResult.createData.data);
        }
        if (mutationResult.updateData?.data) {
            images.push(...mutationResult.updateData.data);
        }

        const completeImages = currentImages ? mergeArraysById(currentImages, images) : images;

        if (mutationResult.deleteData?.data) {
            return completeImages.filter((img) => !mutationResult.deleteData?.data?.some((i) => i.id === img.id));
        }

        return completeImages;
    };

    const getVariantId = (mutationResult: MutationResponse<ImageDetailsDto[]>) => {
        if (mutationResult.createData?.data) {
            return mutationResult.createData.data[0].variantId;
        }
        if (mutationResult.updateData?.data) {
            return mutationResult.updateData.data[0].variantId;
        }
        if (mutationResult.deleteData?.data) {
            return mutationResult.deleteData.data[0].variantId;
        }

        return undefined;
    };

    const updateVariantWithImages = (latestVariants: SkuDto[], imageResult: MutationResponse<ImageDetailsDto[]>[]) => {
        imageResult?.forEach((r) => {
            const variantId = getVariantId(r);
            const variant = latestVariants.find((v) => v.id === variantId);
            if (variant) {
                variant.images = getCompleteImage(variant.images ?? [], r);
            }
        });

        return latestVariants;
    };

    const handleSubmit = (callBack?: SubmitCallBack) => async (values: ProductFormData) => {
        // Check for duplicates variants
        const hasDuplicateVariant = checkForDuplicateVariants(values.variants, productCategoryName);

        if (hasDuplicateVariant) {
            setSnackbar({
                message: `Duplicate variant found`,
                severity: 'error',
                iconName: 'Error',
            });
            return;
        }

        const mutationData = buildMutationData(values);

        let result;
        const errors: MutationErrorData[] = [];
        if (mutationData.createData || mutationData.updateData) {
            result = await mutateAsync(mutationData);
            const variantErrors = filterMutationErrors(result, getVariantTitle);
            if (variantErrors) {
                errors.push(...variantErrors);
            }
        }
        let latestVariants = result
            ? mergeToProductVariants(result.createData?.data, result.updateData?.data)
            : product.variants ?? [];
        let updatedVariants = updateFormVariantIds(latestVariants, values);

        if (updatedVariants) {
            const imageResult = await submitImages(updatedVariants);
            if (imageResult) {
                latestVariants = updateVariantWithImages(latestVariants, imageResult);
                updatedVariants = updateVariantWithImages(updatedVariants, imageResult);
            }
            imageResult?.forEach((r) => {
                const error = filterMutationErrors(r, () => 'Image');
                if (error) {
                    errors.push(...error);
                }
            });
        }

        if (isEmpty(errors)) {
            const parentVariants = prepareFormVariantChildren(latestVariants, productCategoryName);
            form.reset(
                { variants: parentVariants },
                {
                    keepDefaultValues: true,
                    keepDirty: true,
                    keepErrors: true,
                },
            );
        }

        setProductUpdate({ variants: latestVariants.filter((v) => !!v.id) });
        if (callBack) {
            const variantsUpdated = prepareFormVariantChildren(updatedVariants, productCategoryName);
            callBack({
                errors: !isEmpty(errors) ? errors : undefined,
                formValue: undefined,
            });
            const v = getVariantFormValues(variantsUpdated, productCategory);
            form.setValue('variants', v.variants);
        }
    };

    const handleCreateNewVariant = async () => {
        const variant = buildVariantDefaultData(
            product,
            defaultUom,
            defaultAmount,
            1,
            productCategoryName === PRODUCT_CATEGORIES.Merch ? MERCH_SIZE.OneSize : null,
        );
        if (product?.id) {
            const hasDuplicateVariant = checkForDuplicateVariants(form.getValues('variants'), productCategoryName);

            if (hasDuplicateVariant) {
                setSnackbar({
                    message: `Duplicate variant found`,
                    severity: 'error',
                    iconName: 'Error',
                });
                return;
            }

            append(variant);
            setActiveTab(fields.length);
        }
    };

    const handleChange = (_event: any, newTab: any) => {
        setActiveTab(newTab);
    };

    const productSubCategoryName = product.productSubCategory?.name ? product.productSubCategory?.name : 'Bulk';

    if (!product.productCategory) {
        return <>No product category loaded</>;
    }
    return (
        <>
            <Typography variant="h6">SKU</Typography>
            {isBulkProductWithMultipleSku() && (
                <StyledSubHeader>
                    <>
                        {productSubCategoryName} product contains multiple SKUs. {productSubCategoryName} products must
                        have only one SKU to use Bulk Weight Price Tiers. Please recreate the product with a single SKU.
                    </>
                </StyledSubHeader>
            )}

            <ReactHookProductTabForm
                formName="ProductInfoForm"
                onSubmit={handleSubmit}
                formContextProps={form}
                isBusy={isSaving || isSavingImages}
            >
                <StyledTabs variant="filled" onChange={handleChange} value={activeTab} ariaLabel="variants2">
                    {fields.map((field: any, index: number) => (
                        <VariantTab
                            key={field.fieldKeyId}
                            product={product}
                            variant={field}
                            fieldName="variants"
                            index={index}
                            categoryName={product?.productCategory?.name}
                            bulkProductWithSingleSku={isBulkProductWithSingleSku()}
                        />
                    ))}
                    {!isBulkProduct() && (
                        <Button variant="text" label="Add SKU" iconName="Add" onClick={handleCreateNewVariant} />
                    )}
                </StyledTabs>
                {fields.map((field: any, index: number) => (
                    <TabPanel key={field.fieldKeyId} value={activeTab} index={index}>
                        {product.productCategory && (
                            <VariantDetailsForm
                                fieldName="variants"
                                variant={field}
                                componentIndex={index}
                                selectedCategory={product.productCategory}
                                removeItem={handleRemoveVariant}
                            />
                        )}
                    </TabPanel>
                ))}
            </ReactHookProductTabForm>
        </>
    );
};

export default VariantsSection;
