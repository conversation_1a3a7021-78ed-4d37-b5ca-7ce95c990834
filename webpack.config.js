const { merge } = require('webpack-merge');
const singleSpaDefaults = require('webpack-config-single-spa-react-ts');
const Dotenv = require('dotenv-webpack');
const fs = require('fs');

module.exports = (webpackConfigEnv, argv) => {
    const envFile = `./.env.${webpackConfigEnv.stage}`;
    const defaultConfig = singleSpaDefaults({
        orgName: 'treez',
        projectName: 'product-control',
        webpackConfigEnv,
        argv,
    });

    return merge(defaultConfig, {
        // modify the webpack config however you'd like to by adding to this object
        ...(webpackConfigEnv.https === 'true' && {
            devServer: {
                https: {
                    key: fs.readFileSync('./.cert/key.pem'),
                    cert: fs.readFileSync('./.cert/cert.pem'),
                },
            },
        }),
        plugins: [
            new Dotenv({
                path: envFile,
            }),
        ],
        module: {
            rules: [
                 {
                    test: /\.(woff2?|eot|ttf|otf)$/i,
                    type: 'asset/resource',
                    generator: {
                        filename: 'assets/fonts/[name][hash][ext][query]'
                    }
                },
                {
                    test: /\.svg$/,
                    use: [{ loader: 'file-loader' }],
                },
            ],
        },
        externals: ['react', 'react-dom', 'single-spa', /^@treez\/.+/],
        // Useful for debug / verifying changes:
        // optimization: {
        //     minimize: false,
        // },
    });
};
